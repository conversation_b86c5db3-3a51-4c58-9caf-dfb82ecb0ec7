import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from wordcloud import WordCloud
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns
from typing import Dict, List

class AdvancedLogVisualizer:
    """Advanced visualization components for log analysis results"""
    
    def __init__(self):
        self.color_palette = {
            'error': '#FF6B6B',
            'warning': '#FFD93D',
            'info': '#6BCF7F',
            'anomaly': '#FF8C42',
            'critical': '#DC143C',
            'medium': '#FFA500',
            'low': '#32CD32'
        }
    
    def render_error_classification_dashboard(self, classification_results: Dict):
        """Render comprehensive error classification dashboard"""
        st.subheader("🔍 Error Classification Analysis")
        
        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)
        
        total_errors = sum(len(errors) for errors in classification_results.values())
        classified_errors = sum(len(errors) for cat, errors in classification_results.items() if cat != 'unknown')
        classification_rate = (classified_errors / total_errors * 100) if total_errors > 0 else 0
        
        with col1:
            st.metric("Total Errors", total_errors)
        with col2:
            st.metric("Classified", classified_errors)
        with col3:
            st.metric("Classification Rate", f"{classification_rate:.1f}%")
        with col4:
            unknown_count = len(classification_results.get('unknown', []))
            st.metric("Unknown", unknown_count)
        
        # Category distribution
        col1, col2 = st.columns(2)
        
        with col1:
            # Pie chart of error categories
            categories = []
            counts = []
            colors = []
            
            for category, errors in classification_results.items():
                if errors:
                    categories.append(category.title())
                    counts.append(len(errors))
                    colors.append(self.color_palette.get(category, '#808080'))
            
            if categories:
                fig = go.Figure(data=[go.Pie(
                    labels=categories,
                    values=counts,
                    marker_colors=colors,
                    hole=0.3
                )])
                fig.update_layout(title="Error Distribution by Category")
                st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Severity analysis
            severity_data = []
            for category, errors in classification_results.items():
                for error in errors:
                    severity_data.append({
                        'Category': category.title(),
                        'Severity': error.get('severity', 'medium'),
                        'Confidence': error.get('confidence', 0.5)
                    })
            
            if severity_data:
                df = pd.DataFrame(severity_data)
                fig = px.box(df, x='Category', y='Confidence', color='Severity',
                           title="Classification Confidence by Category")
                st.plotly_chart(fig, use_container_width=True)
        
        # Detailed category analysis
        st.subheader("Detailed Category Analysis")
        
        for category, errors in classification_results.items():
            if errors and category != 'unknown':
                with st.expander(f"{category.title()} Errors ({len(errors)})"):
                    
                    # Show confidence distribution
                    confidences = [error.get('confidence', 0.5) for error in errors]
                    avg_confidence = np.mean(confidences)
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Average Confidence", f"{avg_confidence:.2f}")
                    with col2:
                        high_conf_count = sum(1 for c in confidences if c > 0.8)
                        st.metric("High Confidence", f"{high_conf_count}/{len(errors)}")
                    
                    # Show sample errors
                    st.write("**Sample Errors:**")
                    for i, error in enumerate(errors[:3]):
                        st.code(error['log'], language="text")
                        st.caption(f"Confidence: {error.get('confidence', 0.5):.2f} | "
                                 f"Method: {error.get('method', 'unknown')}")
    
    def render_anomaly_detection_dashboard(self, anomaly_results: Dict):
        """Render anomaly detection dashboard"""
        st.subheader("🚨 Anomaly Detection Analysis")
        
        if 'anomalies' not in anomaly_results:
            st.info("No anomaly detection results available")
            return
        
        anomalies = anomaly_results['anomalies']
        total_logs = anomaly_results.get('total_logs', 0)
        anomaly_rate = anomaly_results.get('anomaly_rate', 0)
        
        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Anomalies", len(anomalies))
        with col2:
            st.metric("Anomaly Rate", f"{anomaly_rate:.2%}")
        with col3:
            if anomalies:
                avg_score = np.mean([a['anomaly_score'] for a in anomalies])
                st.metric("Avg Anomaly Score", f"{avg_score:.2f}")
            else:
                st.metric("Avg Anomaly Score", "N/A")
        with col4:
            methods_used = anomaly_results.get('methods_used', [])
            st.metric("Detection Methods", len(methods_used))
        
        if anomalies:
            # Anomaly score distribution
            col1, col2 = st.columns(2)
            
            with col1:
                scores = [a['anomaly_score'] for a in anomalies]
                fig = go.Figure(data=[go.Histogram(x=scores, nbinsx=20)])
                fig.update_layout(title="Anomaly Score Distribution",
                                xaxis_title="Anomaly Score",
                                yaxis_title="Count")
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # Detection method breakdown
                method_counts = Counter()
                for anomaly in anomalies:
                    for method in anomaly.get('detected_by', []):
                        method_counts[method] += 1
                
                if method_counts:
                    methods, counts = zip(*method_counts.items())
                    fig = go.Figure(data=[go.Bar(x=methods, y=counts)])
                    fig.update_layout(title="Detection Method Usage")
                    st.plotly_chart(fig, use_container_width=True)
            
            # Top anomalies
            st.subheader("Top Anomalies")
            
            for i, anomaly in enumerate(anomalies[:10]):
                with st.expander(f"Anomaly #{i+1} (Score: {anomaly['anomaly_score']:.2f})"):
                    st.code(anomaly['log'], language="text")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**Detection Methods:** {', '.join(anomaly.get('detected_by', []))}")
                    with col2:
                        st.write(f"**Log Index:** {anomaly.get('index', 'N/A')}")
    
    def render_trend_analysis_dashboard(self, trend_results: Dict):
        """Render trend analysis dashboard"""
        st.subheader("📈 Trend Analysis")
        
        if not trend_results or 'error_frequency' not in trend_results:
            st.info("No trend analysis results available")
            return
        
        # Error frequency trends
        if 'error_frequency' in trend_results:
            freq_trends = trend_results['error_frequency']
            
            if 'trend_direction' in freq_trends:
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    direction = freq_trends['trend_direction']
                    icon = "📈" if direction == "increasing" else "📉"
                    st.metric("Trend Direction", f"{icon} {direction.title()}")
                
                with col2:
                    correlation = freq_trends.get('correlation', 0)
                    st.metric("Trend Strength", f"{abs(correlation):.2f}")
                
                with col3:
                    avg_hourly = freq_trends.get('hourly_average', 0)
                    st.metric("Hourly Average", f"{avg_hourly:.1f}")
        
        # Severity trends
        if 'severity_trends' in trend_results:
            st.subheader("Severity Level Trends")
            
            severity_trends = trend_results['severity_trends']
            
            # Create trend visualization
            trend_data = []
            for severity, data in severity_trends.items():
                trend_data.append({
                    'Severity': severity.title(),
                    'Trend': data['trend'],
                    'Slope': data['slope'],
                    'Count': data['total_count']
                })
            
            if trend_data:
                df = pd.DataFrame(trend_data)
                
                col1, col2 = st.columns(2)
                
                with col1:
                    fig = px.bar(df, x='Severity', y='Count', color='Trend',
                               title="Error Count by Severity")
                    st.plotly_chart(fig, use_container_width=True)
                
                with col2:
                    fig = px.scatter(df, x='Severity', y='Slope', size='Count',
                                   color='Trend', title="Trend Slopes by Severity")
                    st.plotly_chart(fig, use_container_width=True)
        
        # Seasonal patterns
        if 'seasonal_patterns' in trend_results:
            seasonal = trend_results['seasonal_patterns']
            
            if 'hourly_pattern' in seasonal:
                st.subheader("Hourly Activity Patterns")
                
                hourly_data = seasonal['hourly_pattern']
                hours = list(hourly_data.keys())
                counts = list(hourly_data.values())
                
                fig = go.Figure(data=[go.Scatter(x=hours, y=counts, mode='lines+markers')])
                fig.update_layout(title="Average Log Activity by Hour",
                                xaxis_title="Hour of Day",
                                yaxis_title="Average Log Count")
                st.plotly_chart(fig, use_container_width=True)
                
                col1, col2 = st.columns(2)
                with col1:
                    peak_hour = seasonal.get('peak_hour', 'N/A')
                    st.metric("Peak Activity Hour", f"{peak_hour}:00")
                with col2:
                    low_hour = seasonal.get('low_hour', 'N/A')
                    st.metric("Lowest Activity Hour", f"{low_hour}:00")
    
    def render_root_cause_analysis_dashboard(self, root_cause_results: List[Dict]):
        """Render root cause analysis dashboard"""
        st.subheader("🔬 Root Cause Analysis")
        
        if not root_cause_results:
            st.info("No root cause analysis results available")
            return
        
        # Summary metrics
        total_causes = len(root_cause_results)
        high_confidence_causes = sum(1 for rca in root_cause_results 
                                   if rca.get('confidence', 0) > 0.7)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Root Causes Identified", total_causes)
        with col2:
            st.metric("High Confidence", high_confidence_causes)
        with col3:
            if total_causes > 0:
                avg_confidence = np.mean([rca.get('confidence', 0) for rca in root_cause_results])
                st.metric("Average Confidence", f"{avg_confidence:.2f}")
        
        # Root cause breakdown
        for i, rca in enumerate(root_cause_results):
            with st.expander(f"Root Cause #{i+1}: {rca.get('root_cause', 'Unknown')}"):
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Category:** {rca.get('category', 'Unknown')}")
                    st.write(f"**Confidence:** {rca.get('confidence', 0):.2f}")
                    st.write(f"**Frequency:** {rca.get('frequency', 0)}")
                
                with col2:
                    severity = rca.get('severity', 'medium')
                    color = self.color_palette.get(severity, '#808080')
                    st.markdown(f"**Severity:** <span style='color: {color}'>{severity.title()}</span>", 
                              unsafe_allow_html=True)
                
                # Show affected logs
                if 'affected_logs' in rca:
                    st.write("**Sample Affected Logs:**")
                    for log in rca['affected_logs'][:3]:
                        st.code(log, language="text")
                
                # Show recommendations
                if 'recommendations' in rca:
                    st.write("**Recommendations:**")
                    for rec in rca['recommendations']:
                        st.write(f"• {rec}")
    
    def render_insights_dashboard(self, insights: Dict):
        """Render high-level insights dashboard"""
        st.subheader("💡 AI-Generated Insights")
        
        # Risk assessment
        risk_level = insights.get('risk_assessment', 'low')
        risk_color = self.color_palette.get(risk_level, '#808080')
        
        st.markdown(f"### Overall Risk Level: <span style='color: {risk_color}'>{risk_level.upper()}</span>", 
                   unsafe_allow_html=True)
        
        # Critical issues
        critical_issues = insights.get('critical_issues', [])
        if critical_issues:
            st.subheader("🚨 Critical Issues")
            for issue in critical_issues:
                st.error(issue)
        
        # Recommendations
        recommendations = insights.get('recommendations', [])
        if recommendations:
            st.subheader("📋 Recommendations")
            for rec in recommendations:
                st.info(rec)
        
        if not critical_issues and not recommendations:
            st.success("✅ No critical issues detected. System appears to be operating normally.")

    def render_historical_analysis_dashboard(self, historical_results: Dict):
        """Render historical analysis and learning dashboard"""
        st.subheader("📚 Historical Analysis & Learning")

        if 'error' in historical_results:
            st.error(f"Historical analysis failed: {historical_results['error']}")
            return

        # Enhanced recommendations from historical data
        enhanced_recs = historical_results.get('enhanced_recommendations', [])
        if enhanced_recs:
            st.subheader("🎯 Enhanced Recommendations (Historical)")

            # Group recommendations by type
            rec_types = {}
            for rec in enhanced_recs:
                rec_type = rec.get('type', 'unknown')
                if rec_type not in rec_types:
                    rec_types[rec_type] = []
                rec_types[rec_type].append(rec)

            # Display recommendations by type
            for rec_type, recs in rec_types.items():
                with st.expander(f"{rec_type.replace('_', ' ').title()} ({len(recs)} recommendations)"):
                    for i, rec in enumerate(recs):
                        col1, col2 = st.columns([3, 1])

                        with col1:
                            st.write(f"**{i+1}.** {rec['text']}")
                            st.caption(f"Source: {rec.get('source', 'Unknown')}")

                        with col2:
                            confidence = rec.get('confidence', 0)
                            effectiveness = rec.get('effectiveness', 0)

                            st.metric("Confidence", f"{confidence:.2f}")
                            if effectiveness > 0:
                                st.metric("Effectiveness", f"{effectiveness:.2f}")

                            # Feedback buttons
                            if st.button(f"👍", key=f"like_{rec_type}_{i}"):
                                st.success("Feedback recorded!")
                            if st.button(f"👎", key=f"dislike_{rec_type}_{i}"):
                                st.info("Feedback recorded!")

        # Similar errors
        similar_errors = historical_results.get('similar_errors', [])
        if similar_errors:
            st.subheader("🔍 Similar Historical Errors")

            for i, error in enumerate(similar_errors[:5]):
                with st.expander(f"Similar Error #{i+1} (Similarity: {error.get('similarity', 0):.2f})"):
                    st.code(error.get('error_text', ''), language="text")

                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.write(f"**Category:** {error.get('category', 'Unknown')}")
                    with col2:
                        st.write(f"**Severity:** {error.get('severity', 'Unknown')}")
                    with col3:
                        st.write(f"**Occurrences:** {error.get('occurrence_count', 0)}")

        # Trending patterns
        trending = historical_results.get('trending_patterns', [])
        if trending:
            st.subheader("📈 Trending Error Patterns (Last 7 Days)")

            # Create trending chart
            if len(trending) > 1:
                trend_data = []
                for error in trending:
                    trend_data.append({
                        'Error': error.get('error_text', '')[:50] + '...',
                        'Count': error.get('occurrence_count', 0),
                        'Category': error.get('category', 'Unknown')
                    })

                df = pd.DataFrame(trend_data)
                fig = px.bar(df, x='Count', y='Error', color='Category',
                           title="Trending Errors by Frequency",
                           orientation='h')
                st.plotly_chart(fig, use_container_width=True)

            # Show trending details
            for i, error in enumerate(trending[:3]):
                st.write(f"**{i+1}.** {error.get('error_text', '')[:100]}...")
                st.caption(f"Category: {error.get('category', 'Unknown')} | "
                          f"Count: {error.get('occurrence_count', 0)} | "
                          f"Last seen: {error.get('last_seen', 'Unknown')}")

    def render_knowledge_base_dashboard(self, historical_insights: Dict):
        """Render knowledge base statistics and insights dashboard"""
        st.subheader("🧠 Knowledge Base & Learning Insights")

        if 'error' in historical_insights:
            st.error(f"Knowledge base analysis failed: {historical_insights['error']}")
            return

        kb_stats = historical_insights.get('knowledge_base_stats', {})
        learning_insights = historical_insights.get('learning_insights', {})

        # Knowledge base statistics
        if kb_stats:
            st.subheader("📊 Knowledge Base Statistics")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Total Errors", kb_stats.get('total_errors', 0))
            with col2:
                st.metric("Total Resolutions", kb_stats.get('total_resolutions', 0))
            with col3:
                st.metric("Feedback Entries", kb_stats.get('total_feedback', 0))
            with col4:
                avg_eff = kb_stats.get('avg_effectiveness', 0)
                st.metric("Avg Effectiveness", f"{avg_eff:.2f}")

            # Top categories
            top_categories = kb_stats.get('top_categories', [])
            if top_categories:
                st.subheader("🏷️ Top Error Categories")

                categories = [cat['category'] for cat in top_categories]
                counts = [cat['count'] for cat in top_categories]

                fig = go.Figure(data=[go.Bar(x=categories, y=counts)])
                fig.update_layout(title="Error Distribution by Category")
                st.plotly_chart(fig, use_container_width=True)

            # Recent activity
            recent_errors = kb_stats.get('recent_errors', 0)
            if recent_errors > 0:
                st.info(f"📅 {recent_errors} errors recorded in the last 7 days")

        # Learning insights
        learning_stats = learning_insights.get('learning_stats', {})
        if learning_stats:
            st.subheader("🤖 Learning System Performance")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Active Users", learning_stats.get('active_users', 0))
            with col2:
                st.metric("Learned Patterns", learning_stats.get('total_patterns', 0))
            with col3:
                avg_weight = learning_stats.get('avg_pattern_weight', 0)
                st.metric("Avg Pattern Weight", f"{avg_weight:.2f}")

            # Top patterns
            top_patterns = learning_stats.get('top_patterns', [])
            if top_patterns:
                st.subheader("🔍 Top Learned Patterns")

                for i, (pattern, weight) in enumerate(top_patterns[:10]):
                    progress_value = min(weight, 1.0)
                    st.write(f"**{i+1}.** {pattern}")
                    st.progress(progress_value)
                    st.caption(f"Weight: {weight:.3f}")

        # Recommendations
        recommendations = historical_insights.get('recommendations', [])
        if recommendations:
            st.subheader("💡 Historical Recommendations")

            for i, rec in enumerate(recommendations):
                st.info(f"**{i+1}.** {rec}")

    def render_feedback_interface(self, error_hash: str = None):
        """Render feedback collection interface"""
        st.subheader("💬 Provide Feedback")

        if not error_hash:
            st.warning("No error selected for feedback")
            return

        with st.form("feedback_form"):
            st.write(f"**Error Hash:** `{error_hash}`")

            # Feedback type
            feedback_type = st.selectbox(
                "Feedback Type",
                ["resolution_effectiveness", "recommendation_quality", "general_feedback"]
            )

            # Rating
            rating = st.slider("Rating (1-5 stars)", 1, 5, 3)

            # Comment
            comment = st.text_area("Additional Comments (optional)")

            # User ID (optional)
            user_id = st.text_input("User ID (optional)", placeholder="<EMAIL>")

            # Resolution text (if applicable)
            resolution_text = st.text_area("Resolution Applied (if any)",
                                         placeholder="Describe what you did to resolve this issue...")

            submitted = st.form_submit_button("Submit Feedback")

            if submitted:
                feedback_data = {
                    'feedback_type': feedback_type,
                    'rating': rating,
                    'comment': comment if comment else None,
                    'user_id': user_id if user_id else None,
                    'resolution_text': resolution_text if resolution_text else None
                }

                # Here you would call the feedback storage function
                st.success("✅ Feedback submitted successfully!")
                st.info("This feedback will help improve future recommendations.")

                return feedback_data

        return None
