"""
DevOps Assistant Configuration
Professional configuration management for enterprise deployment
"""

import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

class Environment(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str = "localhost"
    port: int = 5432
    database: str = "devops_assistant"
    username: str = "devops_user"
    password: str = ""
    ssl_mode: str = "prefer"
    connection_pool_size: int = 10
    connection_timeout: int = 30

@dataclass
class MonitoringConfig:
    """Monitoring and alerting configuration"""
    enable_real_time: bool = True
    refresh_interval: int = 30  # seconds
    alert_thresholds: Dict[str, float] = None
    notification_channels: List[str] = None
    retention_days: int = 90
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                'cpu_usage': 80.0,
                'memory_usage': 85.0,
                'disk_usage': 90.0,
                'error_rate': 5.0,
                'response_time': 2000.0  # milliseconds
            }
        
        if self.notification_channels is None:
            self.notification_channels = ['email', 'slack']

@dataclass
class SecurityConfig:
    """Security configuration"""
    enable_authentication: bool = True
    session_timeout: int = 3600  # seconds
    max_login_attempts: int = 5
    password_min_length: int = 8
    require_mfa: bool = False
    allowed_ip_ranges: List[str] = None
    api_rate_limit: int = 1000  # requests per hour
    
    def __post_init__(self):
        if self.allowed_ip_ranges is None:
            self.allowed_ip_ranges = ['0.0.0.0/0']  # Allow all by default

@dataclass
class IntegrationConfig:
    """External integrations configuration"""
    # CI/CD
    jenkins_url: str = ""
    jenkins_username: str = ""
    jenkins_token: str = ""
    
    # Cloud Providers
    aws_access_key: str = ""
    aws_secret_key: str = ""
    aws_region: str = "us-east-1"
    
    azure_subscription_id: str = ""
    azure_client_id: str = ""
    azure_client_secret: str = ""
    
    gcp_project_id: str = ""
    gcp_service_account_key: str = ""
    
    # Container Orchestration
    kubernetes_config_path: str = "~/.kube/config"
    docker_host: str = "unix:///var/run/docker.sock"
    
    # Monitoring Tools
    prometheus_url: str = ""
    grafana_url: str = ""
    elasticsearch_url: str = ""
    
    # Communication
    slack_webhook_url: str = ""
    slack_bot_token: str = ""
    teams_webhook_url: str = ""
    
    # Ticketing
    jira_url: str = ""
    jira_username: str = ""
    jira_token: str = ""
    
    servicenow_instance: str = ""
    servicenow_username: str = ""
    servicenow_password: str = ""

@dataclass
class AIConfig:
    """AI and ML configuration"""
    enable_deep_learning: bool = True
    enable_historical_learning: bool = True
    model_cache_size: int = 1000  # MB
    analysis_timeout: int = 300  # seconds
    confidence_threshold: float = 0.7
    anomaly_detection_sensitivity: float = 0.1
    pattern_learning_rate: float = 0.01
    max_recommendations: int = 10
    
    # Model paths
    classification_model_path: str = "models/classification.pkl"
    anomaly_model_path: str = "models/anomaly.pkl"
    nlp_model_path: str = "models/nlp.pkl"

@dataclass
class UIConfig:
    """User interface configuration"""
    theme: str = "professional"  # professional, dark, light
    default_page: str = "dashboard"
    items_per_page: int = 50
    auto_refresh: bool = True
    show_advanced_features: bool = True
    enable_dark_mode: bool = False
    chart_animation: bool = True
    
    # Customization
    company_name: str = "DevOps Assistant"
    company_logo: str = ""
    primary_color: str = "#2a5298"
    secondary_color: str = "#28a745"
    accent_color: str = "#ffc107"

@dataclass
class PerformanceConfig:
    """Performance and optimization configuration"""
    max_log_lines: int = 100000
    batch_size: int = 1000
    cache_ttl: int = 3600  # seconds
    max_concurrent_analyses: int = 5
    memory_limit: int = 2048  # MB
    cpu_cores: int = 4
    
    # Database optimization
    db_connection_pool_size: int = 20
    db_query_timeout: int = 30
    db_bulk_insert_size: int = 1000

class DevOpsConfig:
    """Main configuration class for DevOps Assistant"""
    
    def __init__(self, environment: Environment = Environment.DEVELOPMENT):
        self.environment = environment
        self.database = DatabaseConfig()
        self.monitoring = MonitoringConfig()
        self.security = SecurityConfig()
        self.integrations = IntegrationConfig()
        self.ai = AIConfig()
        self.ui = UIConfig()
        self.performance = PerformanceConfig()
        
        # Load environment-specific configurations
        self._load_environment_config()
        
        # Load from environment variables
        self._load_from_env()
    
    def _load_environment_config(self):
        """Load environment-specific configurations"""
        if self.environment == Environment.PRODUCTION:
            # Production settings
            self.security.require_mfa = True
            self.security.session_timeout = 1800  # 30 minutes
            self.monitoring.retention_days = 365
            self.performance.max_concurrent_analyses = 10
            self.ai.confidence_threshold = 0.8
            
        elif self.environment == Environment.STAGING:
            # Staging settings
            self.monitoring.retention_days = 30
            self.performance.max_concurrent_analyses = 3
            
        elif self.environment == Environment.DEVELOPMENT:
            # Development settings
            self.security.enable_authentication = False
            self.monitoring.retention_days = 7
            self.performance.max_concurrent_analyses = 2
            self.ui.show_advanced_features = True
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # Database
        self.database.host = os.getenv('DB_HOST', self.database.host)
        self.database.port = int(os.getenv('DB_PORT', self.database.port))
        self.database.database = os.getenv('DB_NAME', self.database.database)
        self.database.username = os.getenv('DB_USER', self.database.username)
        self.database.password = os.getenv('DB_PASSWORD', self.database.password)
        
        # Security
        self.security.enable_authentication = os.getenv('ENABLE_AUTH', 'true').lower() == 'true'
        self.security.require_mfa = os.getenv('REQUIRE_MFA', 'false').lower() == 'true'
        
        # Integrations
        self.integrations.jenkins_url = os.getenv('JENKINS_URL', self.integrations.jenkins_url)
        self.integrations.jenkins_username = os.getenv('JENKINS_USER', self.integrations.jenkins_username)
        self.integrations.jenkins_token = os.getenv('JENKINS_TOKEN', self.integrations.jenkins_token)
        
        self.integrations.aws_access_key = os.getenv('AWS_ACCESS_KEY_ID', self.integrations.aws_access_key)
        self.integrations.aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY', self.integrations.aws_secret_key)
        self.integrations.aws_region = os.getenv('AWS_REGION', self.integrations.aws_region)
        
        self.integrations.slack_webhook_url = os.getenv('SLACK_WEBHOOK_URL', self.integrations.slack_webhook_url)
        self.integrations.slack_bot_token = os.getenv('SLACK_BOT_TOKEN', self.integrations.slack_bot_token)
        
        # AI Configuration
        self.ai.enable_deep_learning = os.getenv('ENABLE_DEEP_LEARNING', 'true').lower() == 'true'
        self.ai.enable_historical_learning = os.getenv('ENABLE_HISTORICAL_LEARNING', 'true').lower() == 'true'
        
        # UI Configuration
        self.ui.company_name = os.getenv('COMPANY_NAME', self.ui.company_name)
        self.ui.primary_color = os.getenv('PRIMARY_COLOR', self.ui.primary_color)
    
    def get_alert_threshold(self, metric: str) -> float:
        """Get alert threshold for a specific metric"""
        return self.monitoring.alert_thresholds.get(metric, 0.0)
    
    def is_integration_enabled(self, integration: str) -> bool:
        """Check if a specific integration is enabled"""
        integration_map = {
            'jenkins': bool(self.integrations.jenkins_url and self.integrations.jenkins_token),
            'aws': bool(self.integrations.aws_access_key and self.integrations.aws_secret_key),
            'slack': bool(self.integrations.slack_webhook_url or self.integrations.slack_bot_token),
            'kubernetes': bool(self.integrations.kubernetes_config_path),
            'prometheus': bool(self.integrations.prometheus_url),
            'grafana': bool(self.integrations.grafana_url),
            'elasticsearch': bool(self.integrations.elasticsearch_url)
        }
        
        return integration_map.get(integration, False)
    
    def get_database_url(self) -> str:
        """Get database connection URL"""
        return f"postgresql://{self.database.username}:{self.database.password}@{self.database.host}:{self.database.port}/{self.database.database}"
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Check required fields for production
        if self.environment == Environment.PRODUCTION:
            if not self.database.password:
                issues.append("Database password is required for production")
            
            if not self.security.enable_authentication:
                issues.append("Authentication should be enabled for production")
            
            if '0.0.0.0/0' in self.security.allowed_ip_ranges:
                issues.append("Allowing all IP ranges is not recommended for production")
        
        # Check AI model paths
        if self.ai.enable_deep_learning:
            if not os.path.exists(self.ai.classification_model_path):
                issues.append(f"Classification model not found: {self.ai.classification_model_path}")
        
        # Check integration configurations
        if self.integrations.jenkins_url and not self.integrations.jenkins_token:
            issues.append("Jenkins token is required when Jenkins URL is configured")
        
        return issues
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary"""
        return {
            'environment': self.environment.value,
            'database': self.database.__dict__,
            'monitoring': self.monitoring.__dict__,
            'security': self.security.__dict__,
            'integrations': self.integrations.__dict__,
            'ai': self.ai.__dict__,
            'ui': self.ui.__dict__,
            'performance': self.performance.__dict__
        }
    
    @classmethod
    def from_file(cls, config_file: str) -> 'DevOpsConfig':
        """Load configuration from file"""
        import json
        
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        
        # Create instance and update with file data
        instance = cls()
        # Implementation would update instance with config_data
        return instance
    
    def save_to_file(self, config_file: str):
        """Save configuration to file"""
        import json
        
        with open(config_file, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)

# Global configuration instance
config = DevOpsConfig()

# Environment-specific configurations
def get_config(environment: str = None) -> DevOpsConfig:
    """Get configuration for specific environment"""
    if environment:
        env = Environment(environment.lower())
        return DevOpsConfig(env)
    return config
