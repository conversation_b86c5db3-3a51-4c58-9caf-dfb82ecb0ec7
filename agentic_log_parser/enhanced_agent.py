import re
import json
import pickle
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# Import historical database and feedback loop
from historical_database import HistoricalDatabase
from feedback_loop import FeedbackLoop

# ML/NLP Libraries
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.cluster import DBSCAN, KMeans
from sklearn.ensemble import IsolationForest, RandomForestClassifier
from sklearn.decomposition import PCA, LatentDirichletAllocation
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from textblob import TextBlob
import spacy

# Statistical Analysis
from scipy import stats
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.arima.model import ARIMA
import pyod
from pyod.models.iforest import IForest
from pyod.models.lof import LOF
from pyod.models.ocsvm import OCSVM

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Deep Learning (Optional)
try:
    from transformers import pipeline, AutoTokenizer, AutoModel
    from sentence_transformers import SentenceTransformer
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: Transformers not available. Deep learning features will be disabled.")
    TRANSFORMERS_AVAILABLE = False

class EnhancedLogAnalyzer:
    """
    Advanced AI-powered log analyzer with ML/NLP capabilities for:
    - Error Classification & Root Cause Analysis
    - Anomaly & Trend Detection
    - Pattern Recognition & Clustering
    """
    
    def __init__(self, log_data: str, enable_deep_learning: bool = True,
                 enable_historical_learning: bool = True):
        self.log_data = log_data
        self.log_lines = log_data.splitlines()
        self.enable_deep_learning = enable_deep_learning and TRANSFORMERS_AVAILABLE
        self.enable_historical_learning = enable_historical_learning

        # Initialize components
        self._init_nlp_components()
        self._init_ml_models()
        self._init_knowledge_base()

        # Initialize historical database and feedback loop
        if self.enable_historical_learning:
            try:
                self.historical_db = HistoricalDatabase()
                self.feedback_loop = FeedbackLoop()
                print("✅ Historical learning system initialized")
            except Exception as e:
                print(f"Warning: Could not initialize historical learning: {e}")
                self.enable_historical_learning = False
                self.historical_db = None
                self.feedback_loop = None
        else:
            self.historical_db = None
            self.feedback_loop = None

        # Results storage
        self.analysis_results = {
            'errors': [],
            'warnings': [],
            'anomalies': [],
            'clusters': {},
            'trends': {},
            'root_causes': [],
            'suggestions': [],
            'historical_insights': {}
        }
    
    def _init_nlp_components(self):
        """Initialize NLP components"""
        try:
            # Download required NLTK data
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('wordnet', quiet=True)
            
            self.stop_words = set(stopwords.words('english'))
            self.lemmatizer = WordNetLemmatizer()
            
            # Load spaCy model
            try:
                self.nlp = spacy.load("en_core_web_sm")
            except OSError:
                print("Warning: spaCy model not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None
            
            # Initialize sentence transformer for semantic analysis
            if self.enable_deep_learning:
                try:
                    self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
                    self.classifier_pipeline = pipeline("text-classification", 
                                                       model="microsoft/DialoGPT-medium")
                except Exception as e:
                    print(f"Warning: Could not load transformer models: {e}")
                    self.enable_deep_learning = False
                    
        except Exception as e:
            print(f"Warning: NLP initialization failed: {e}")
    
    def _init_ml_models(self):
        """Initialize ML models for anomaly detection and classification"""
        # Anomaly detection models
        self.anomaly_models = {
            'isolation_forest': IsolationForest(contamination=0.1, random_state=42),
            'lof': LOF(contamination=0.1),
            'ocsvm': OCSVM(contamination=0.1)
        }
        
        # Text vectorizers
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        
        self.count_vectorizer = CountVectorizer(
            max_features=500,
            stop_words='english'
        )
        
        # Clustering models
        self.clustering_models = {
            'kmeans': KMeans(n_clusters=5, random_state=42),
            'dbscan': DBSCAN(eps=0.5, min_samples=5)
        }
        
        # Classification model for error types
        self.error_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
    
    def _init_knowledge_base(self):
        """Initialize enhanced knowledge base with error patterns"""
        self.error_patterns = {
            'database': {
                'patterns': [r'database.*error', r'connection.*failed', r'sql.*exception'],
                'severity': 'high',
                'category': 'database',
                'root_causes': ['connection timeout', 'credential issues', 'database overload']
            },
            'network': {
                'patterns': [r'network.*timeout', r'connection.*refused', r'host.*unreachable'],
                'severity': 'high',
                'category': 'network',
                'root_causes': ['network congestion', 'firewall blocking', 'service down']
            },
            'memory': {
                'patterns': [r'out.*of.*memory', r'memory.*leak', r'heap.*space'],
                'severity': 'critical',
                'category': 'memory',
                'root_causes': ['memory leak', 'insufficient resources', 'inefficient code']
            },
            'authentication': {
                'patterns': [r'auth.*failed', r'unauthorized', r'permission.*denied'],
                'severity': 'medium',
                'category': 'security',
                'root_causes': ['expired credentials', 'insufficient permissions', 'configuration error']
            }
        }
    
    def preprocess_text(self, text: str) -> str:
        """Advanced text preprocessing for NLP analysis"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove timestamps and common log prefixes
        text = re.sub(r'\d{4}-\d{2}-\d{2}[\s\d:.-]*', '', text)
        text = re.sub(r'\[(error|warning|info|debug)\]', '', text)
        
        # Remove special characters but keep meaningful punctuation
        text = re.sub(r'[^\w\s\-\.]', ' ', text)
        
        # Tokenize and lemmatize
        if self.nlp:
            doc = self.nlp(text)
            tokens = [token.lemma_ for token in doc if not token.is_stop and not token.is_punct]
        else:
            tokens = word_tokenize(text)
            tokens = [self.lemmatizer.lemmatize(token) for token in tokens 
                     if token not in self.stop_words and token.isalpha()]
        
        return ' '.join(tokens)
    
    def extract_features(self, logs: List[str]) -> Dict:
        """Extract comprehensive features from log data"""
        features = {}
        
        # Preprocess logs
        processed_logs = [self.preprocess_text(log) for log in logs]
        
        # TF-IDF features
        tfidf_matrix = self.tfidf_vectorizer.fit_transform(processed_logs)
        features['tfidf'] = tfidf_matrix.toarray()
        
        # Count features
        count_matrix = self.count_vectorizer.fit_transform(processed_logs)
        features['count'] = count_matrix.toarray()
        
        # Semantic features (if transformers available)
        if self.enable_deep_learning:
            try:
                embeddings = self.sentence_model.encode(processed_logs)
                features['semantic'] = embeddings
            except Exception as e:
                print(f"Warning: Could not generate semantic embeddings: {e}")
        
        # Statistical features
        features['statistical'] = self._extract_statistical_features(logs)
        
        return features
    
    def _extract_statistical_features(self, logs: List[str]) -> np.ndarray:
        """Extract statistical features from logs"""
        features = []
        
        for log in logs:
            log_features = [
                len(log),  # Length
                len(log.split()),  # Word count
                log.count('['),  # Bracket count
                log.count('error'),  # Error mentions
                log.count('warning'),  # Warning mentions
                log.count('failed'),  # Failure mentions
                len(re.findall(r'\d+', log)),  # Number count
                TextBlob(log).sentiment.polarity,  # Sentiment
                TextBlob(log).sentiment.subjectivity  # Subjectivity
            ]
            features.append(log_features)
        
        return np.array(features)

    def classify_errors(self, logs: List[str]) -> Dict:
        """Advanced error classification using ML/NLP"""
        if not logs:
            return {}

        classified_errors = {
            'database': [],
            'network': [],
            'memory': [],
            'authentication': [],
            'unknown': []
        }

        # Rule-based classification first
        for log in logs:
            classified = False
            for category, info in self.error_patterns.items():
                for pattern in info['patterns']:
                    if re.search(pattern, log, re.IGNORECASE):
                        classified_errors[category].append({
                            'log': log,
                            'confidence': 0.9,
                            'method': 'rule-based',
                            'severity': info['severity'],
                            'root_causes': info['root_causes']
                        })
                        classified = True
                        break
                if classified:
                    break

            if not classified:
                # Use ML classification for unknown errors
                if self.enable_deep_learning:
                    category, confidence = self._ml_classify_error(log)
                    classified_errors[category].append({
                        'log': log,
                        'confidence': confidence,
                        'method': 'ml-based',
                        'severity': 'medium',
                        'root_causes': ['requires investigation']
                    })
                else:
                    classified_errors['unknown'].append({
                        'log': log,
                        'confidence': 0.5,
                        'method': 'fallback',
                        'severity': 'medium',
                        'root_causes': ['manual analysis needed']
                    })

        return classified_errors

    def _ml_classify_error(self, log: str) -> Tuple[str, float]:
        """ML-based error classification using semantic similarity"""
        if not self.enable_deep_learning:
            return 'unknown', 0.5

        try:
            # Get embedding for the log
            log_embedding = self.sentence_model.encode([self.preprocess_text(log)])

            # Compare with category embeddings
            category_texts = {
                'database': 'database connection error sql query failed',
                'network': 'network timeout connection refused host unreachable',
                'memory': 'out of memory heap space memory leak',
                'authentication': 'authentication failed unauthorized access denied'
            }

            best_category = 'unknown'
            best_score = 0.0

            for category, text in category_texts.items():
                category_embedding = self.sentence_model.encode([text])
                similarity = cosine_similarity(log_embedding, category_embedding)[0][0]

                if similarity > best_score:
                    best_score = similarity
                    best_category = category

            # Threshold for classification confidence
            if best_score < 0.3:
                best_category = 'unknown'

            return best_category, float(best_score)

        except Exception as e:
            print(f"ML classification error: {e}")
            return 'unknown', 0.5

    def detect_anomalies(self, logs: List[str]) -> Dict:
        """Multi-method anomaly detection"""
        if len(logs) < 10:
            return {'anomalies': [], 'method': 'insufficient_data'}

        # Extract features
        features = self.extract_features(logs)

        anomaly_results = {}

        # Statistical anomaly detection
        statistical_anomalies = self._detect_statistical_anomalies(features['statistical'])

        # Text-based anomaly detection
        text_anomalies = self._detect_text_anomalies(features['tfidf'])

        # Semantic anomaly detection (if available)
        if 'semantic' in features:
            semantic_anomalies = self._detect_semantic_anomalies(features['semantic'])
        else:
            semantic_anomalies = []

        # Combine results
        all_anomaly_indices = set(statistical_anomalies + text_anomalies + semantic_anomalies)

        anomalies = []
        for idx in all_anomaly_indices:
            if idx < len(logs):
                anomalies.append({
                    'log': logs[idx],
                    'index': idx,
                    'anomaly_score': self._calculate_anomaly_score(idx, features),
                    'detected_by': self._get_detection_methods(idx, statistical_anomalies,
                                                             text_anomalies, semantic_anomalies)
                })

        # Sort by anomaly score
        anomalies.sort(key=lambda x: x['anomaly_score'], reverse=True)

        return {
            'anomalies': anomalies,
            'total_logs': len(logs),
            'anomaly_rate': len(anomalies) / len(logs),
            'methods_used': ['statistical', 'text-based'] + (['semantic'] if 'semantic' in features else [])
        }

    def _detect_statistical_anomalies(self, features: np.ndarray) -> List[int]:
        """Detect anomalies using statistical methods"""
        anomalies = []

        # Standardize features
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)

        # Use multiple models
        for name, model in self.anomaly_models.items():
            try:
                predictions = model.fit_predict(scaled_features)
                anomaly_indices = np.where(predictions == -1)[0].tolist()
                anomalies.extend(anomaly_indices)
            except Exception as e:
                print(f"Warning: {name} anomaly detection failed: {e}")

        return list(set(anomalies))

    def _detect_text_anomalies(self, tfidf_features: np.ndarray) -> List[int]:
        """Detect anomalies in text patterns"""
        try:
            # Use Isolation Forest on TF-IDF features
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            predictions = iso_forest.fit_predict(tfidf_features)
            return np.where(predictions == -1)[0].tolist()
        except Exception as e:
            print(f"Text anomaly detection failed: {e}")
            return []

    def _detect_semantic_anomalies(self, semantic_features: np.ndarray) -> List[int]:
        """Detect semantic anomalies using embeddings"""
        try:
            # Use DBSCAN to find outliers
            dbscan = DBSCAN(eps=0.5, min_samples=3)
            clusters = dbscan.fit_predict(semantic_features)

            # Points labeled as -1 are outliers
            return np.where(clusters == -1)[0].tolist()
        except Exception as e:
            print(f"Semantic anomaly detection failed: {e}")
            return []

    def _calculate_anomaly_score(self, idx: int, features: Dict) -> float:
        """Calculate composite anomaly score"""
        scores = []

        # Statistical score
        if 'statistical' in features:
            stat_features = features['statistical'][idx:idx+1]
            scaler = StandardScaler()
            scaled = scaler.fit_transform(features['statistical'])
            stat_score = np.abs(stats.zscore(scaled[idx])).mean()
            scores.append(min(stat_score, 5.0))  # Cap at 5.0

        # Text-based score
        if 'tfidf' in features:
            tfidf_features = features['tfidf'][idx:idx+1]
            # Calculate distance from centroid
            centroid = np.mean(features['tfidf'], axis=0)
            distance = np.linalg.norm(tfidf_features - centroid)
            scores.append(min(distance, 5.0))

        return np.mean(scores) if scores else 0.0

    def _get_detection_methods(self, idx: int, statistical: List, text: List, semantic: List) -> List[str]:
        """Get which methods detected this anomaly"""
        methods = []
        if idx in statistical:
            methods.append('statistical')
        if idx in text:
            methods.append('text-based')
        if idx in semantic:
            methods.append('semantic')
        return methods

    def analyze_trends(self, logs: List[str]) -> Dict:
        """Comprehensive trend analysis"""
        if len(logs) < 20:
            return {'trends': [], 'message': 'Insufficient data for trend analysis'}

        # Extract timestamps and create time series
        timestamps = self._extract_timestamps(logs)
        if not timestamps:
            return {'trends': [], 'message': 'No timestamps found in logs'}

        # Create time series data
        df = pd.DataFrame({
            'timestamp': timestamps,
            'log': logs[:len(timestamps)]
        })
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')

        # Analyze different aspects
        trends = {}

        # Error frequency trends
        trends['error_frequency'] = self._analyze_error_frequency_trends(df)

        # Severity trends
        trends['severity_trends'] = self._analyze_severity_trends(df)

        # Pattern emergence
        trends['pattern_emergence'] = self._analyze_pattern_emergence(df)

        # Seasonal patterns
        trends['seasonal_patterns'] = self._analyze_seasonal_patterns(df)

        return trends

    def _extract_timestamps(self, logs: List[str]) -> List[str]:
        """Extract timestamps from logs"""
        timestamps = []
        timestamp_patterns = [
            r'\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}',
            r'\d{2}/\d{2}/\d{4}\s\d{2}:\d{2}:\d{2}',
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}',
        ]

        for log in logs:
            for pattern in timestamp_patterns:
                match = re.search(pattern, log)
                if match:
                    timestamps.append(match.group())
                    break

        return timestamps

    def _analyze_error_frequency_trends(self, df: pd.DataFrame) -> Dict:
        """Analyze error frequency trends over time"""
        try:
            # Resample by hour
            df_hourly = df.set_index('timestamp').resample('H').size()

            # Calculate trend
            x = np.arange(len(df_hourly))
            y = df_hourly.values

            if len(y) > 1:
                slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

                return {
                    'trend_direction': 'increasing' if slope > 0 else 'decreasing',
                    'slope': float(slope),
                    'correlation': float(r_value),
                    'significance': float(p_value),
                    'hourly_average': float(np.mean(y)),
                    'peak_hour': df_hourly.idxmax().strftime('%H:%M') if not df_hourly.empty else None
                }
            else:
                return {'message': 'Insufficient data for trend analysis'}

        except Exception as e:
            return {'error': f'Trend analysis failed: {e}'}

    def _analyze_severity_trends(self, df: pd.DataFrame) -> Dict:
        """Analyze severity level trends"""
        severity_counts = defaultdict(list)

        for _, row in df.iterrows():
            log = row['log'].lower()
            timestamp = row['timestamp']

            if 'error' in log:
                severity_counts['error'].append(timestamp)
            elif 'warning' in log:
                severity_counts['warning'].append(timestamp)
            elif 'info' in log:
                severity_counts['info'].append(timestamp)

        trends = {}
        for severity, timestamps in severity_counts.items():
            if len(timestamps) > 1:
                # Convert to hourly counts
                ts_series = pd.Series(1, index=pd.to_datetime(timestamps))
                hourly_counts = ts_series.resample('H').sum().fillna(0)

                # Calculate trend
                x = np.arange(len(hourly_counts))
                y = hourly_counts.values

                if len(y) > 1:
                    slope, _, r_value, p_value, _ = stats.linregress(x, y)
                    trends[severity] = {
                        'trend': 'increasing' if slope > 0 else 'decreasing',
                        'slope': float(slope),
                        'correlation': float(r_value),
                        'total_count': len(timestamps)
                    }

        return trends

    def _analyze_pattern_emergence(self, df: pd.DataFrame) -> Dict:
        """Analyze emerging patterns in logs"""
        try:
            # Group logs by time windows
            df['hour'] = df['timestamp'].dt.hour
            df['day'] = df['timestamp'].dt.date

            # Find patterns that appear more frequently over time
            patterns = {}

            # Extract common error patterns
            error_logs = df[df['log'].str.contains('error', case=False, na=False)]

            if len(error_logs) > 5:
                # Use TF-IDF to find important terms
                processed_logs = [self.preprocess_text(log) for log in error_logs['log']]
                tfidf = TfidfVectorizer(max_features=20, stop_words='english')
                tfidf_matrix = tfidf.fit_transform(processed_logs)

                feature_names = tfidf.get_feature_names_out()
                scores = tfidf_matrix.sum(axis=0).A1

                # Get top patterns
                top_patterns = [(feature_names[i], scores[i]) for i in scores.argsort()[-10:]]
                patterns['emerging_terms'] = top_patterns

            return patterns

        except Exception as e:
            return {'error': f'Pattern analysis failed: {e}'}

    def _analyze_seasonal_patterns(self, df: pd.DataFrame) -> Dict:
        """Analyze seasonal/cyclical patterns"""
        try:
            if len(df) < 24:  # Need at least 24 hours of data
                return {'message': 'Insufficient data for seasonal analysis'}

            # Create hourly time series
            hourly_counts = df.set_index('timestamp').resample('H').size()

            if len(hourly_counts) < 24:
                return {'message': 'Need at least 24 hours of data'}

            # Analyze hourly patterns
            hourly_pattern = hourly_counts.groupby(hourly_counts.index.hour).mean()

            # Find peak and low activity hours
            peak_hour = hourly_pattern.idxmax()
            low_hour = hourly_pattern.idxmin()

            return {
                'peak_hour': int(peak_hour),
                'low_hour': int(low_hour),
                'hourly_pattern': hourly_pattern.to_dict(),
                'pattern_strength': float(hourly_pattern.std() / hourly_pattern.mean())
            }

        except Exception as e:
            return {'error': f'Seasonal analysis failed: {e}'}

    def perform_root_cause_analysis(self, errors: List[str]) -> List[Dict]:
        """Advanced root cause analysis using ML and pattern matching"""
        if not errors:
            return []

        root_causes = []

        # Classify errors first
        classified_errors = self.classify_errors(errors)

        # Analyze each category
        for category, error_list in classified_errors.items():
            if not error_list:
                continue

            category_analysis = self._analyze_error_category(category, error_list)
            root_causes.extend(category_analysis)

        # Cluster similar errors for pattern-based root cause analysis
        if len(errors) > 5:
            cluster_analysis = self._cluster_based_root_cause_analysis(errors)
            root_causes.extend(cluster_analysis)

        return root_causes

    def _analyze_error_category(self, category: str, errors: List[Dict]) -> List[Dict]:
        """Analyze root causes for a specific error category"""
        analysis = []

        if category in self.error_patterns:
            pattern_info = self.error_patterns[category]

            # Count frequency of each root cause
            cause_frequency = Counter()
            for error in errors:
                for cause in pattern_info['root_causes']:
                    # Simple keyword matching for now
                    if any(keyword in error['log'].lower() for keyword in cause.split()):
                        cause_frequency[cause] += 1

            # Generate analysis for top causes
            for cause, frequency in cause_frequency.most_common(3):
                analysis.append({
                    'category': category,
                    'root_cause': cause,
                    'frequency': frequency,
                    'confidence': min(0.9, frequency / len(errors)),
                    'affected_logs': [e['log'] for e in errors if
                                    any(keyword in e['log'].lower() for keyword in cause.split())],
                    'severity': pattern_info['severity'],
                    'recommendations': self._get_recommendations(category, cause)
                })

        return analysis

    def _cluster_based_root_cause_analysis(self, errors: List[str]) -> List[Dict]:
        """Use clustering to find similar error patterns"""
        try:
            # Extract features
            features = self.extract_features(errors)

            # Perform clustering on TF-IDF features
            kmeans = KMeans(n_clusters=min(5, len(errors)//2), random_state=42)
            clusters = kmeans.fit_predict(features['tfidf'])

            cluster_analysis = []

            # Analyze each cluster
            for cluster_id in set(clusters):
                cluster_errors = [errors[i] for i, c in enumerate(clusters) if c == cluster_id]

                if len(cluster_errors) > 1:  # Only analyze clusters with multiple errors
                    # Find common patterns in this cluster
                    common_terms = self._find_common_terms(cluster_errors)

                    cluster_analysis.append({
                        'cluster_id': int(cluster_id),
                        'error_count': len(cluster_errors),
                        'common_patterns': common_terms,
                        'sample_errors': cluster_errors[:3],
                        'potential_root_cause': self._infer_root_cause_from_patterns(common_terms),
                        'confidence': len(cluster_errors) / len(errors)
                    })

            return cluster_analysis

        except Exception as e:
            print(f"Cluster-based analysis failed: {e}")
            return []

    def _find_common_terms(self, errors: List[str]) -> List[str]:
        """Find common terms in a cluster of errors"""
        try:
            processed_errors = [self.preprocess_text(error) for error in errors]

            # Use TF-IDF to find important terms
            vectorizer = TfidfVectorizer(max_features=10, stop_words='english')
            tfidf_matrix = vectorizer.fit_transform(processed_errors)

            feature_names = vectorizer.get_feature_names_out()
            scores = tfidf_matrix.sum(axis=0).A1

            # Get top terms
            top_indices = scores.argsort()[-5:][::-1]
            return [feature_names[i] for i in top_indices]

        except Exception as e:
            print(f"Common terms extraction failed: {e}")
            return []

    def _infer_root_cause_from_patterns(self, patterns: List[str]) -> str:
        """Infer potential root cause from common patterns"""
        pattern_str = ' '.join(patterns).lower()

        # Simple rule-based inference
        if any(term in pattern_str for term in ['connection', 'timeout', 'network']):
            return 'Network connectivity issues'
        elif any(term in pattern_str for term in ['memory', 'heap', 'space']):
            return 'Memory-related issues'
        elif any(term in pattern_str for term in ['database', 'sql', 'query']):
            return 'Database-related issues'
        elif any(term in pattern_str for term in ['auth', 'permission', 'access']):
            return 'Authentication/Authorization issues'
        elif any(term in pattern_str for term in ['config', 'setting', 'parameter']):
            return 'Configuration issues'
        else:
            return 'Unknown - requires manual investigation'

    def _get_recommendations(self, category: str, root_cause: str) -> List[str]:
        """Get actionable recommendations based on category and root cause"""
        recommendations_db = {
            'database': {
                'connection timeout': [
                    'Check database server status',
                    'Verify network connectivity',
                    'Review connection pool settings',
                    'Check for database locks'
                ],
                'credential issues': [
                    'Verify database credentials',
                    'Check user permissions',
                    'Ensure password hasn\'t expired'
                ]
            },
            'network': {
                'network congestion': [
                    'Monitor network bandwidth usage',
                    'Check for network bottlenecks',
                    'Consider load balancing'
                ],
                'firewall blocking': [
                    'Review firewall rules',
                    'Check port accessibility',
                    'Verify security group settings'
                ]
            },
            'memory': {
                'memory leak': [
                    'Profile application memory usage',
                    'Review code for memory leaks',
                    'Increase heap size temporarily',
                    'Implement memory monitoring'
                ]
            }
        }

        return recommendations_db.get(category, {}).get(root_cause, ['Manual investigation required'])

    def run_comprehensive_analysis(self) -> Dict:
        """Run complete AI-powered log analysis"""
        print("🤖 Starting comprehensive AI log analysis...")

        # Extract errors and warnings
        errors = [line for line in self.log_lines if re.search(r'\[?error\]?', line, re.IGNORECASE)]
        warnings = [line for line in self.log_lines if re.search(r'\[?warning\]?', line, re.IGNORECASE)]

        print(f"📊 Found {len(errors)} errors and {len(warnings)} warnings")

        # Perform comprehensive analysis
        results = {
            'summary': {
                'total_logs': len(self.log_lines),
                'errors': len(errors),
                'warnings': len(warnings),
                'analysis_timestamp': datetime.now().isoformat()
            }
        }

        # Error classification
        if errors:
            print("🔍 Classifying errors...")
            results['error_classification'] = self.classify_errors(errors)

        # Anomaly detection
        if len(self.log_lines) >= 10:
            print("🚨 Detecting anomalies...")
            results['anomaly_detection'] = self.detect_anomalies(self.log_lines)

        # Trend analysis
        if len(self.log_lines) >= 20:
            print("📈 Analyzing trends...")
            results['trend_analysis'] = self.analyze_trends(self.log_lines)

        # Root cause analysis
        if errors:
            print("🔬 Performing root cause analysis...")
            results['root_cause_analysis'] = self.perform_root_cause_analysis(errors)

        # Generate insights and recommendations
        print("💡 Generating insights...")
        results['insights'] = self._generate_insights(results)

        # Historical learning analysis
        if self.enable_historical_learning and errors:
            print("📚 Analyzing with historical learning...")
            results['historical_analysis'] = self.analyze_with_historical_learning(errors)
            results['historical_insights'] = self.get_historical_insights()

        print("✅ Analysis complete!")
        return results

    def _generate_insights(self, analysis_results: Dict) -> Dict:
        """Generate high-level insights from analysis results"""
        insights = {
            'critical_issues': [],
            'recommendations': [],
            'risk_assessment': 'low'
        }

        # Analyze error patterns
        if 'error_classification' in analysis_results:
            error_class = analysis_results['error_classification']

            # Check for critical categories
            critical_categories = ['memory', 'database']
            for category in critical_categories:
                if category in error_class and error_class[category]:
                    insights['critical_issues'].append(f"Critical {category} errors detected")
                    insights['risk_assessment'] = 'high'

        # Analyze anomaly rate
        if 'anomaly_detection' in analysis_results:
            anomaly_rate = analysis_results['anomaly_detection'].get('anomaly_rate', 0)
            if anomaly_rate > 0.1:  # More than 10% anomalies
                insights['critical_issues'].append(f"High anomaly rate: {anomaly_rate:.2%}")
                insights['risk_assessment'] = 'medium' if insights['risk_assessment'] == 'low' else 'high'

        # Generate recommendations
        if insights['critical_issues']:
            insights['recommendations'].extend([
                'Immediate investigation required for critical issues',
                'Implement enhanced monitoring',
                'Review system capacity and performance'
            ])

        return insights

    def analyze_with_historical_learning(self, errors: List[str]) -> Dict:
        """Analyze errors using historical database and feedback loop"""
        if not self.enable_historical_learning or not self.feedback_loop:
            return {'message': 'Historical learning not available'}

        historical_results = {
            'enhanced_recommendations': [],
            'similar_errors': [],
            'trending_patterns': [],
            'learning_insights': {}
        }

        try:
            # Process each error with historical context
            for error in errors:
                result = self.feedback_loop.process_error_with_history(
                    error_text=error,
                    category=self._determine_error_category(error),
                    severity=self._determine_error_severity(error)
                )

                historical_results['enhanced_recommendations'].extend(
                    result['enhanced_recommendations']
                )
                historical_results['similar_errors'].extend(
                    result['similar_errors']
                )

            # Get trending errors
            historical_results['trending_patterns'] = self.historical_db.get_trending_errors(days=7)

            # Get learning insights
            historical_results['learning_insights'] = self.feedback_loop.get_learning_insights()

            return historical_results

        except Exception as e:
            print(f"Error in historical analysis: {e}")
            return {'error': str(e)}

    def _determine_error_category(self, error_text: str) -> str:
        """Determine error category for historical storage"""
        error_lower = error_text.lower()

        if any(word in error_lower for word in ['database', 'sql', 'connection', 'query']):
            return 'database'
        elif any(word in error_lower for word in ['network', 'timeout', 'host', 'connection']):
            return 'network'
        elif any(word in error_lower for word in ['memory', 'heap', 'space', 'oom']):
            return 'memory'
        elif any(word in error_lower for word in ['auth', 'permission', 'access', 'credential']):
            return 'authentication'
        elif any(word in error_lower for word in ['config', 'setting', 'parameter']):
            return 'configuration'
        else:
            return 'unknown'

    def _determine_error_severity(self, error_text: str) -> str:
        """Determine error severity for historical storage"""
        error_lower = error_text.lower()

        if any(word in error_lower for word in ['critical', 'fatal', 'emergency', 'panic']):
            return 'critical'
        elif any(word in error_lower for word in ['error', 'failed', 'exception', 'crash']):
            return 'high'
        elif any(word in error_lower for word in ['warning', 'warn', 'deprecated']):
            return 'medium'
        else:
            return 'low'

    def store_user_feedback(self, error_hash: str, feedback_data: Dict) -> bool:
        """Store user feedback for learning"""
        if not self.enable_historical_learning or not self.feedback_loop:
            return False

        try:
            return self.feedback_loop.record_feedback(
                error_hash=error_hash,
                resolution_id=feedback_data.get('resolution_id'),
                feedback_type=feedback_data.get('feedback_type', 'general'),
                rating=feedback_data.get('rating'),
                comment=feedback_data.get('comment'),
                user_id=feedback_data.get('user_id'),
                resolution_text=feedback_data.get('resolution_text')
            )
        except Exception as e:
            print(f"Error storing feedback: {e}")
            return False

    def get_historical_insights(self) -> Dict:
        """Get insights from historical database"""
        if not self.enable_historical_learning or not self.historical_db:
            return {'message': 'Historical learning not available'}

        try:
            stats = self.historical_db.get_knowledge_base_stats()
            trending = self.historical_db.get_trending_errors(days=7)
            learning_insights = self.feedback_loop.get_learning_insights() if self.feedback_loop else {}

            return {
                'knowledge_base_stats': stats,
                'trending_errors': trending,
                'learning_insights': learning_insights,
                'recommendations': self._generate_historical_recommendations(stats, trending)
            }
        except Exception as e:
            print(f"Error getting historical insights: {e}")
            return {'error': str(e)}

    def _generate_historical_recommendations(self, stats: Dict, trending: List[Dict]) -> List[str]:
        """Generate recommendations based on historical data"""
        recommendations = []

        # Recommendations based on stats
        if stats.get('total_errors', 0) > 100:
            recommendations.append("Consider implementing proactive monitoring based on historical patterns")

        if stats.get('avg_effectiveness', 0) < 0.7:
            recommendations.append("Review and improve resolution documentation quality")

        # Recommendations based on trending errors
        if len(trending) > 5:
            recommendations.append("Multiple trending errors detected - investigate common root causes")

        for error in trending[:3]:
            if error.get('occurrence_count', 0) > 10:
                recommendations.append(f"High frequency error in {error.get('category', 'unknown')} category needs attention")

        return recommendations
