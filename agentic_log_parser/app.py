import streamlit as st
from agent import AgenticLogParser
from enhanced_agent import EnhancedLogAnalyzer
from advanced_visualizations import AdvancedLogVisualizer
from collections import Counter, defaultdict
import json
import os
import re
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from sklearn.ensemble import IsolationForest
import pandas as pd
import numpy as np
from dotenv import load_dotenv
load_dotenv()

# Set page configuration with custom theme
st.set_page_config(
    page_title="Agentic AI Log Analyzer",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #4257B2;
        margin-bottom: 0.5rem;
    }
    .sub-header {
        font-size: 1.2rem;
        color: #767C99;
        margin-bottom: 2rem;
    }
    .stat-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #4257B2;
    }
    .stat-label {
        font-size: 1rem;
        color: #767C99;
    }
    .highlight {
        background-color: #fffacd;
        padding: 0.2rem;
        border-radius: 3px;
    }
    .tab-content {
        padding: 1rem 0;
    }
    .suggestion-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #4257B2;
    }
    .error-card {
        border-left: 4px solid #dc3545;
    }
    .warning-card {
        border-left: 4px solid #ffc107;
    }
</style>
""", unsafe_allow_html=True)

# Header
st.markdown('<div class="main-header">🤖 Agentic AI Log Analyzer</div>', unsafe_allow_html=True)
st.markdown('<div class="sub-header">Intelligent analysis and insights for your system logs</div>', unsafe_allow_html=True)

# Constants
MEMORY_FILE = "log_memory.json"
INCIDENT_KB_FILE = "incident_kb.json"
ACTION_HISTORY_FILE = "action_history.json"

# Helper to parse timestamps - example regex for ISO-like timestamps
timestamp_regex = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})')

def extract_timestamps(log_lines):
    timestamps = []
    for line in log_lines:
        match = timestamp_regex.search(line)
        if match:
            try:
                dt = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
                timestamps.append((line, dt))
            except Exception:
                pass
    return timestamps

def update_memory(errors, warnings):
    memory = {"errors": {}, "warnings": {}}
    if os.path.exists(MEMORY_FILE):
        with open(MEMORY_FILE, "r") as f:
            memory = json.load(f)
    for err in errors:
        memory["errors"][err] = memory["errors"].get(err, 0) + 1
    for warn in warnings:
        memory["warnings"][warn] = memory["warnings"].get(warn, 0) + 1
    with open(MEMORY_FILE, "w") as f:
        json.dump(memory, f, indent=2)

def show_memory_summary():
    if not os.path.exists(MEMORY_FILE):
        st.info("No memory file found.")
        return
    with open(MEMORY_FILE, "r") as f:
        memory = json.load(f)
    st.markdown("**Top Frequent Errors:**")
    for err, count in Counter(memory["errors"]).most_common(5):
        st.markdown(f"- `{err}` - {count} times")
    st.markdown("**Top Frequent Warnings:**")
    for warn, count in Counter(memory["warnings"]).most_common(5):
        st.markdown(f"- `{warn}` - {count} times")

def prepare_download_data(result):
    export = {
        "errors": result["errors"],
        "warnings": result["warnings"],
        "suggestions": result["suggestions"]
    }
    return json.dumps(export, indent=2)

def load_incident_kb():
    if os.path.exists(INCIDENT_KB_FILE):
        with open(INCIDENT_KB_FILE) as f:
            return json.load(f)
    return {}

def load_action_history():
    if os.path.exists(ACTION_HISTORY_FILE):
        with open(ACTION_HISTORY_FILE) as f:
            return json.load(f)
    return []

def save_action_history(history):
    with open(ACTION_HISTORY_FILE, "w") as f:
        json.dump(history, f, indent=2)

def detect_anomalies(log_lines):
    # Simple numeric features: length of lines, count of error words, etc.
    features = []
    for line in log_lines:
        length = len(line)
        error_count = line.lower().count("error")
        warning_count = line.lower().count("warning")
        features.append([length, error_count, warning_count])
    clf = IsolationForest(contamination=0.05, random_state=42)
    preds = clf.fit_predict(features)
    anomalies = [log_lines[i] for i, p in enumerate(preds) if p == -1]
    return anomalies

def plot_severity_stats(errors, warnings, anomalies, infos):
    labels = ['Errors', 'Warnings', 'Anomalies', 'Info']
    counts = [len(errors), len(warnings), len(anomalies), len(infos)]
    colors = ['#ff6f61', '#6b5b95', '#88b04b', '#f7cac9'] 
    fig, ax = plt.subplots(figsize=(4, 4), dpi=150)  # higher dpi for crisp image
    ax.pie(
        counts,
        labels=labels,
        autopct='%1.1f%%',     # 1 decimal place for percentages
        startangle=140,
        colors=colors,
        textprops={'fontsize': 5}   # readable font size
    )
    ax.axis('equal')
    st.pyplot(fig)

def plot_error_timeline(timestamps, errors, warnings):
    if not timestamps:
        st.info("No timestamps found for timeline.")
        return
    df = pd.DataFrame(timestamps, columns=['log','timestamp'])
    df['type'] = df['log'].apply(lambda x: 'Error' if any(e in x for e in errors) else ('Warning' if any(w in x for w in warnings) else 'Info'))
    df_grouped = df.groupby(['timestamp', 'type']).size().unstack(fill_value=0)
    df_grouped.plot(kind='line', figsize=(10, 4))
    plt.xlabel("Timestamp")
    plt.ylabel("Count")
    plt.title("Error & Warning Timeline")
    plt.grid(True)
    st.pyplot(plt.gcf())

def filter_logs(log_lines, keyword=None, severity=None, date_range=None):
    filtered = []
    for line in log_lines:
        if keyword and keyword.lower() not in line.lower():
            continue
        if severity:
            sev_check = False
            if severity == "Error" and "[ERROR]" in line:
                sev_check = True
            elif severity == "Warning" and "[WARNING]" in line:
                sev_check = True
            elif severity == "Info" and "[INFO]" in line:
                sev_check = True
            if not sev_check:
                continue
        if date_range:
            match = timestamp_regex.search(line)
            if not match:
                continue
            try:
                dt = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
                if not (date_range[0] <= dt <= date_range[1]):
                    continue
            except:
                continue
        filtered.append(line)
    return filtered

def parse_natural_query(query, log_lines):
    # Very simple keyword + time window parsing
    query = query.lower()
    filtered_logs = log_lines
    if "error" in query:
        filtered_logs = [l for l in filtered_logs if "[error]" in l.lower()]
    elif "warning" in query:
        filtered_logs = [l for l in filtered_logs if "[warning]" in l.lower()]
    elif "info" in query:
        filtered_logs = [l for l in filtered_logs if "[info]" in l.lower()]
    # time frame parsing (last week, today)
    now = datetime.now()
    if "last week" in query:
        start = now - timedelta(days=7)
        filtered_logs = [l for l in filtered_logs if timestamp_regex.search(l) and start <= datetime.strptime(timestamp_regex.search(l).group(1), '%Y-%m-%d %H:%M:%S') <= now]
    elif "today" in query:
        start = datetime(now.year, now.month, now.day)
        filtered_logs = [l for l in filtered_logs if timestamp_regex.search(l) and datetime.strptime(timestamp_regex.search(l).group(1), '%Y-%m-%d %H:%M:%S') >= start]
    return filtered_logs

def trigger_jenkins_job(log_line, action_history):
    # Mock action: append to history and show UI success
    action = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "action": "Trigger Jenkins Sonar Analysis Job",
        "trigger_log": log_line
    }
    action_history.append(action)
    save_action_history(action_history)
    st.success("Action: 🚀 Triggering Jenkins Sonar Analysis Job...")

# Sidebar
with st.sidebar:
    st.markdown("### Configuration")
    
    # OpenAI GPT toggle and key
    use_gpt = st.checkbox("Use GPT fallback", value=True)
    if use_gpt:
        if os.getenv("OPENAI_API_KEY"):
            openai_key = os.getenv("OPENAI_API_KEY")
            st.success("✅ GPT fallback enabled using key from .env")
        else:
            st.warning("⚠️ No OpenAI API key found in .env file.") 
            openai_key = st.text_input("🔑 Enter OpenAI API Key", type="password")
            st.warning("OpenAI API key required for GPT functionality.")
    
    st.markdown("---")
    st.markdown("### About")
    st.markdown("""
    This tool analyzes log files to identify errors, warnings, 
    and anomalies, providing intelligent suggestions for resolution.
    
    **Features:**
    - Error & warning detection
    - Anomaly identification
    - Timeline visualization
    - Natural language queries
    """)

# Main content
log_file_path = "jenkins.log" 
if os.path.exists(log_file_path):
    with open(log_file_path, "r") as file:
        log_text = file.read()
    
    st.success(f"📄 Loaded log file: `{log_file_path}`")
    log_lines = log_text.splitlines()

    parser = AgenticLogParser(log_text, enable_gpt=True)
    result = parser.run()

    # Detect anomalies separately for the tab
    anomalies = detect_anomalies(log_lines)

    timestamps = extract_timestamps(log_lines)
    errors = result["errors"]
    warnings = result["warnings"]
    infos = [line for line in log_lines if "[info]" in line.lower()]

    # Update persistent memory counts
    update_memory(errors, warnings)

    # Load incident knowledge base and action history
    incident_kb = load_incident_kb()
    action_history = load_action_history()

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.markdown('<div class="stat-card">', unsafe_allow_html=True)
        st.markdown(f'<div class="stat-number">{len(errors)}</div>', unsafe_allow_html=True)
        st.markdown('<div class="stat-label">Errors</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="stat-card">', unsafe_allow_html=True)
        st.markdown(f'<div class="stat-number">{len(warnings)}</div>', unsafe_allow_html=True)
        st.markdown('<div class="stat-label">Warnings</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col3:
        st.markdown('<div class="stat-card">', unsafe_allow_html=True)
        st.markdown(f'<div class="stat-number">{len(anomalies)}</div>', unsafe_allow_html=True)
        st.markdown('<div class="stat-label">Anomalies</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col4:
        st.markdown('<div class="stat-card">', unsafe_allow_html=True)
        st.markdown(f'<div class="stat-number">{len(result["suggestions"])}</div>', unsafe_allow_html=True)
        st.markdown('<div class="stat-label">Suggestions</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

    st.markdown("---")

    # Enhanced AI Analysis Toggle
    st.markdown("---")
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown("### 🤖 Enhanced AI Analysis")
        st.markdown("Enable advanced ML/NLP-powered analysis for deeper insights")
    with col2:
        enable_ai_analysis = st.checkbox("Enable AI Analysis", value=False,
                                       help="Uses advanced ML models for error classification, anomaly detection, and root cause analysis")

    # Historical Learning Toggle
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown("### 📚 Historical Learning")
        st.markdown("Enable learning from past errors and user feedback")
    with col2:
        enable_historical_learning = st.checkbox("Enable Historical Learning", value=True,
                                                help="Stores errors and learns from feedback to improve recommendations")

    # Run enhanced analysis if enabled
    enhanced_results = None
    if enable_ai_analysis:
        with st.spinner("🤖 Running enhanced AI analysis..."):
            try:
                enhanced_analyzer = EnhancedLogAnalyzer(
                    log_text,
                    enable_deep_learning=True,
                    enable_historical_learning=enable_historical_learning
                )
                enhanced_results = enhanced_analyzer.run_comprehensive_analysis()
                st.success("✅ Enhanced AI analysis completed!")

                if enable_historical_learning:
                    st.info("📚 Historical learning enabled - system will learn from this analysis")

            except Exception as e:
                st.error(f"❌ Enhanced analysis failed: {e}")
                st.info("Falling back to basic analysis...")
                import traceback
                st.code(traceback.format_exc())

    # Streamlit Tabs
    if enhanced_results:
        if enable_historical_learning and 'historical_analysis' in enhanced_results:
            tabs = st.tabs([
                "📊 Overview",
                "🤖 AI Classification",
                "🚨 Anomaly Detection",
                "📈 Trend Analysis",
                "🔬 Root Cause Analysis",
                "💡 AI Insights",
                "📚 Historical Analysis",
                "🧠 Knowledge Base",
                "💬 Feedback",
                "❌ Errors",
                "⚠️ Warnings",
                "🛠️ Suggestions",
                "🔍 Search & Filter",
                "🗣️ Natural Language Query",
                "⚙️ Actions & Automation"
            ])
        else:
            tabs = st.tabs([
                "📊 Overview",
                "🤖 AI Classification",
                "🚨 Anomaly Detection",
                "📈 Trend Analysis",
                "🔬 Root Cause Analysis",
                "💡 AI Insights",
                "❌ Errors",
                "⚠️ Warnings",
                "🛠️ Suggestions",
                "🔍 Search & Filter",
                "🗣️ Natural Language Query",
                "📚 Knowledge Base",
                "⚙️ Actions & Automation"
            ])
    else:
        tabs = st.tabs([
            "📊 Overview",
            "❌ Errors",
            "⚠️ Warnings",
            "🛠️ Suggestions",
            "🔍 Search & Filter",
            "🗣️ Natural Language Query",
            "📚 Knowledge Base",
            "⚙️ Actions & Automation"
        ])

    # Initialize visualizer for enhanced results
    if enhanced_results:
        visualizer = AdvancedLogVisualizer()

    # Enhanced AI Analysis Tabs
    if enhanced_results:
        # AI Classification Tab
        with tabs[1]:
            if 'error_classification' in enhanced_results:
                visualizer.render_error_classification_dashboard(enhanced_results['error_classification'])
            else:
                st.info("No error classification results available")

        # Anomaly Detection Tab
        with tabs[2]:
            if 'anomaly_detection' in enhanced_results:
                visualizer.render_anomaly_detection_dashboard(enhanced_results['anomaly_detection'])
            else:
                st.info("No anomaly detection results available")

        # Trend Analysis Tab
        with tabs[3]:
            if 'trend_analysis' in enhanced_results:
                visualizer.render_trend_analysis_dashboard(enhanced_results['trend_analysis'])
            else:
                st.info("No trend analysis results available")

        # Root Cause Analysis Tab
        with tabs[4]:
            if 'root_cause_analysis' in enhanced_results:
                visualizer.render_root_cause_analysis_dashboard(enhanced_results['root_cause_analysis'])
            else:
                st.info("No root cause analysis results available")

        # AI Insights Tab
        with tabs[5]:
            if 'insights' in enhanced_results:
                visualizer.render_insights_dashboard(enhanced_results['insights'])
            else:
                st.info("No insights available")

        # Historical Analysis Tab (if enabled)
        if enable_historical_learning and 'historical_analysis' in enhanced_results:
            with tabs[6]:
                visualizer.render_historical_analysis_dashboard(enhanced_results['historical_analysis'])

            # Knowledge Base Tab
            with tabs[7]:
                if 'historical_insights' in enhanced_results:
                    visualizer.render_knowledge_base_dashboard(enhanced_results['historical_insights'])
                else:
                    st.info("No historical insights available")

            # Feedback Tab
            with tabs[8]:
                st.subheader("💬 Provide Feedback")

                # Error selection for feedback
                if errors:
                    selected_error = st.selectbox(
                        "Select an error to provide feedback on:",
                        options=range(len(errors)),
                        format_func=lambda x: f"Error {x+1}: {errors[x][:80]}..."
                    )

                    if selected_error is not None:
                        error_text = errors[selected_error]

                        # Generate error hash for feedback
                        import hashlib
                        error_hash = hashlib.md5(error_text.encode()).hexdigest()

                        # Render feedback interface
                        feedback_data = visualizer.render_feedback_interface(error_hash)

                        # Store feedback if submitted
                        if feedback_data and hasattr(enhanced_analyzer, 'store_user_feedback'):
                            success = enhanced_analyzer.store_user_feedback(error_hash, feedback_data)
                            if success:
                                st.balloons()
                                st.success("🎉 Thank you! Your feedback helps improve the system.")
                            else:
                                st.error("Failed to store feedback. Please try again.")
                else:
                    st.info("No errors found to provide feedback on.")

            # Adjust tab indices for remaining tabs
            error_tab_idx = 9
            warning_tab_idx = 10
            suggestion_tab_idx = 11
            search_tab_idx = 12
            nl_tab_idx = 13
            kb_tab_idx = 14
            action_tab_idx = 14
        else:
            # Adjust tab indices when historical learning is disabled
            error_tab_idx = 6
            warning_tab_idx = 7
            suggestion_tab_idx = 8
            search_tab_idx = 9
            nl_tab_idx = 10
            kb_tab_idx = 11
            action_tab_idx = 12

        # Overview tab (enhanced version)
        with tabs[0]:
            st.markdown('<div class="tab-content">', unsafe_allow_html=True)
            st.subheader("Enhanced Analysis Overview")

            # Show enhanced summary
            if 'summary' in enhanced_results:
                summary = enhanced_results['summary']
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total Logs", summary.get('total_logs', 0))
                with col2:
                    st.metric("Errors Found", summary.get('errors', 0))
                with col3:
                    st.metric("Warnings Found", summary.get('warnings', 0))
                with col4:
                    analysis_time = summary.get('analysis_timestamp', '')
                    if analysis_time:
                        time_str = analysis_time.split('T')[1][:8] if 'T' in analysis_time else analysis_time
                        st.metric("Analysis Time", time_str)

            # Quick insights
            if 'insights' in enhanced_results:
                insights = enhanced_results['insights']
                risk_level = insights.get('risk_assessment', 'low')

                if risk_level == 'high':
                    st.error(f"🚨 High Risk Level Detected")
                elif risk_level == 'medium':
                    st.warning(f"⚠️ Medium Risk Level")
                else:
                    st.success(f"✅ Low Risk Level")

                critical_issues = insights.get('critical_issues', [])
                if critical_issues:
                    st.subheader("Critical Issues")
                    for issue in critical_issues[:3]:
                        st.error(issue)

            # Traditional overview for comparison
            st.subheader("Traditional Analysis")
            col1, col2 = st.columns(2)

            with col1:
                plot_severity_stats(errors, warnings, anomalies, infos)

            with col2:
                severity_counts = {"Errors": len(errors), "Warnings": len(warnings),
                                  "Anomalies": len(anomalies), "Info": len(infos)}
                st.bar_chart(severity_counts)

            st.markdown('</div>', unsafe_allow_html=True)

        # Adjust tab indices for remaining tabs
        error_tab_idx = 6
        warning_tab_idx = 7
        suggestion_tab_idx = 8
        search_tab_idx = 9
        nl_tab_idx = 10
        kb_tab_idx = 11
        action_tab_idx = 12
    else:
        # Overview tab (basic version)
        with tabs[0]:
            st.markdown('<div class="tab-content">', unsafe_allow_html=True)
            st.subheader("Log Severity Distribution")
            col1, col2 = st.columns(2)

            with col1:
                plot_severity_stats(errors, warnings, anomalies, infos)

            with col2:
                severity_counts = {"Errors": len(errors), "Warnings": len(warnings),
                                  "Anomalies": len(anomalies), "Info": len(infos)}
                st.bar_chart(severity_counts)

            st.subheader("Error Timeline")
            plot_error_timeline(timestamps, errors, warnings)

            st.subheader("Recent Anomalies")
            if anomalies:
                for i, a in enumerate(anomalies[:5]):  # Show only top 5
                    st.code(a, language="text")
            else:
                st.info("No anomalies detected.")
            st.markdown('</div>', unsafe_allow_html=True)

        # Set tab indices for basic version
        error_tab_idx = 1
        warning_tab_idx = 2
        suggestion_tab_idx = 3
        search_tab_idx = 4
        nl_tab_idx = 5
        kb_tab_idx = 6
        action_tab_idx = 7

    # Errors tab
    with tabs[error_tab_idx]:
        st.markdown('<div class="tab-content">', unsafe_allow_html=True)
        st.subheader("Detected Errors")
        if errors:
            for err in errors:
                st.markdown(f'<div class="suggestion-card error-card">{err}</div>', unsafe_allow_html=True)
        else:
            st.info("No errors found.")
        st.markdown('</div>', unsafe_allow_html=True)

    # Warnings tab
    with tabs[warning_tab_idx]:
        st.markdown('<div class="tab-content">', unsafe_allow_html=True)
        st.subheader("Detected Warnings")
        if warnings:
            for warn in warnings:
                st.markdown(f'<div class="suggestion-card warning-card">{warn}</div>', unsafe_allow_html=True)
        else:
            st.info("No warnings found.")
        st.markdown('</div>', unsafe_allow_html=True)

    # Suggestions tab
    with tabs[suggestion_tab_idx]:
        st.markdown('<div class="tab-content">', unsafe_allow_html=True)
        st.subheader("Suggested Fixes")
        if result["suggestions"]:
            for sug in result["suggestions"]:
                with st.expander(f"Solution for: {sug['log'][:80]}..."):
                    st.code(sug['log'], language="text")
                    st.markdown("**🔧 Suggested Solution:**")
                    st.write(sug['solution'])

                    if sug.get("comment"):
                        st.markdown(f"**💬 Comment:** {sug['comment']}")

                    st.markdown("**📚 Knowledge base:** Releng")
                    if "sonar" in sug['log'].lower():
                        if st.button("Trigger Jenkins Sonar Analysis Job", key=f"sonar_{hash(sug['log'])}"):
                            trigger_jenkins_job(sug['log'], action_history)
        else:
            st.info("No suggestions found.")
        st.markdown('</div>', unsafe_allow_html=True)

    # Search & Filter tab
    with tabs[search_tab_idx]:
        st.markdown('<div class="tab-content">', unsafe_allow_html=True)
        st.subheader("Search & Filter Logs")

        col1, col2 = st.columns(2)
        with col1:
            keyword = st.text_input("Enter keyword to search")
        with col2:
            severity_filter = st.selectbox("Select severity level", ["All", "Error", "Warning", "Info"])

        col1, col2 = st.columns(2)
        with col1:
            date_start = st.date_input("Start date")
        with col2:
            date_end = st.date_input("End date")

        if date_start > date_end:
            st.error("Start date must be before end date")
        else:
            date_range = (datetime.combine(date_start, datetime.min.time()),
                          datetime.combine(date_end, datetime.max.time()))

            if st.button("Search Logs"):
                filtered_logs = filter_logs(log_lines,
                                           keyword if keyword else None,
                                           severity_filter if severity_filter != "All" else None,
                                           date_range)
                st.write(f"Found {len(filtered_logs)} matching logs:")
                for line in filtered_logs:
                    st.code(line, language="text")
        st.markdown('</div>', unsafe_allow_html=True)

    # Natural Language Query tab
    with tabs[nl_tab_idx]:
        st.markdown('<div class="tab-content">', unsafe_allow_html=True)
        st.subheader("Natural Language Query")
        query = st.text_area("Ask a question about logs (e.g., 'Show me all errors last week')")
        if st.button("Run Query"):
            if not query.strip():
                st.warning("Please enter a query.")
            else:
                nl_filtered = parse_natural_query(query, log_lines)
                st.write(f"Query returned {len(nl_filtered)} logs:")
                for l in nl_filtered:
                    st.code(l, language="text")
        st.markdown('</div>', unsafe_allow_html=True)

    # Knowledge Base tab
    with tabs[kb_tab_idx]:
        st.markdown('<div class="tab-content">', unsafe_allow_html=True)
        st.subheader("Resolved Incident Knowledge Base")
        if incident_kb:
            for err, details in incident_kb.items():
                with st.expander(f"KB: {err}"):
                    st.markdown(f"**Solution:** {details.get('solution','N/A')}")
                    st.markdown(f"**Reference:** {details.get('link','No link available')}")
        else:
            st.info("No incident knowledge base data found.")
        st.markdown('</div>', unsafe_allow_html=True)

    # Actions & Automation tab
    with tabs[action_tab_idx]:
        st.markdown('<div class="tab-content">', unsafe_allow_html=True)
        st.subheader("Action History & Automation")
        if action_history:
            for act in reversed(action_history[-10:]):  # show last 10 actions
                st.markdown(f"**{act['timestamp']}**: {act['action']}")
                st.markdown(f"Triggered by: `{act['trigger_log'][:100]}...`")
                st.markdown("---")
        else:
            st.info("No action history available.")
        st.markdown('</div>', unsafe_allow_html=True)

    # Footer
    st.markdown("---")
    st.markdown("### Export Options")
    col1, col2 = st.columns(2)
    with col1:
        export_data = prepare_download_data(result)
        st.download_button(
            label="Download JSON Results", 
            data=export_data, 
            file_name="log_analysis_results.json", 
            mime="application/json"
        )
    with col2:
        st.download_button(
            label="Download Raw Logs", 
            data=log_text, 
            file_name="raw_logs.txt", 
            mime="text/plain"
        )

else:
    st.info("Upload a `.log` or `.txt` file to start analyzing.")
    uploaded_file = st.file_uploader("📂 Upload a log file", type=["log", "txt"])
    
    if uploaded_file:
        # Process the uploaded file
        log_text = uploaded_file.read().decode("utf-8")
        st.success(f"File uploaded successfully: {uploaded_file.name}")
        st.button("Analyze Logs", type="primary")
