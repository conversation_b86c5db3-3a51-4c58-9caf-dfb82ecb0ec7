#!/usr/bin/env python3
"""
CI/CD Manager for DevOps Assistant
Manages build pipelines and deployments to production environments
"""

import streamlit as st
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import subprocess
import os

@dataclass
class BuildJob:
    """Build job data structure"""
    id: str
    name: str
    repository: str
    branch: str
    status: str  # pending, running, success, failed
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[int] = None  # seconds
    commit_hash: str = ""
    author: str = ""
    build_logs: List[str] = None
    artifacts: List[str] = None
    test_results: Dict = None

@dataclass
class Deployment:
    """Deployment data structure"""
    id: str
    build_id: str
    environment: str
    status: str  # pending, deploying, success, failed, rolled_back
    created_at: datetime
    deployed_at: Optional[datetime] = None
    duration: Optional[int] = None
    version: str = ""
    rollback_version: Optional[str] = None
    approval_required: bool = True
    approved_by: Optional[str] = None
    deployment_logs: List[str] = None

class CICDManager:
    """Manages CI/CD pipelines, builds, and deployments"""
    
    def __init__(self):
        self.builds_file = "cicd_builds.json"
        self.deployments_file = "cicd_deployments.json"
        self.builds = self.load_builds()
        self.deployments = self.load_deployments()
        
        # Initialize with sample data if empty
        if not self.builds:
            self._create_sample_builds()
        if not self.deployments:
            self._create_sample_deployments()
    
    def load_builds(self) -> Dict[str, BuildJob]:
        """Load builds from file"""
        try:
            if os.path.exists(self.builds_file):
                with open(self.builds_file, 'r') as f:
                    data = json.load(f)
                    builds = {}
                    for build_id, build_data in data.items():
                        # Convert datetime strings back to datetime objects
                        build_data['created_at'] = datetime.fromisoformat(build_data['created_at'])
                        if build_data.get('started_at'):
                            build_data['started_at'] = datetime.fromisoformat(build_data['started_at'])
                        if build_data.get('completed_at'):
                            build_data['completed_at'] = datetime.fromisoformat(build_data['completed_at'])
                        builds[build_id] = BuildJob(**build_data)
                    return builds
        except Exception as e:
            st.error(f"Error loading builds: {e}")
        return {}
    
    def save_builds(self):
        """Save builds to file"""
        try:
            data = {}
            for build_id, build in self.builds.items():
                build_dict = asdict(build)
                # Convert datetime objects to strings
                build_dict['created_at'] = build.created_at.isoformat()
                if build.started_at:
                    build_dict['started_at'] = build.started_at.isoformat()
                if build.completed_at:
                    build_dict['completed_at'] = build.completed_at.isoformat()
                data[build_id] = build_dict
            
            with open(self.builds_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            st.error(f"Error saving builds: {e}")
    
    def load_deployments(self) -> Dict[str, Deployment]:
        """Load deployments from file"""
        try:
            if os.path.exists(self.deployments_file):
                with open(self.deployments_file, 'r') as f:
                    data = json.load(f)
                    deployments = {}
                    for deploy_id, deploy_data in data.items():
                        # Convert datetime strings back to datetime objects
                        deploy_data['created_at'] = datetime.fromisoformat(deploy_data['created_at'])
                        if deploy_data.get('deployed_at'):
                            deploy_data['deployed_at'] = datetime.fromisoformat(deploy_data['deployed_at'])
                        deployments[deploy_id] = Deployment(**deploy_data)
                    return deployments
        except Exception as e:
            st.error(f"Error loading deployments: {e}")
        return {}
    
    def save_deployments(self):
        """Save deployments to file"""
        try:
            data = {}
            for deploy_id, deployment in self.deployments.items():
                deploy_dict = asdict(deployment)
                # Convert datetime objects to strings
                deploy_dict['created_at'] = deployment.created_at.isoformat()
                if deployment.deployed_at:
                    deploy_dict['deployed_at'] = deployment.deployed_at.isoformat()
                data[deploy_id] = deploy_dict
            
            with open(self.deployments_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            st.error(f"Error saving deployments: {e}")
    
    def create_build(self, name: str, repository: str, branch: str = "main") -> str:
        """Create a new build job"""
        build_id = f"build_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.builds)}"
        
        build = BuildJob(
            id=build_id,
            name=name,
            repository=repository,
            branch=branch,
            status="pending",
            created_at=datetime.now(),
            build_logs=[],
            artifacts=[],
            test_results={}
        )
        
        self.builds[build_id] = build
        self.save_builds()
        return build_id
    
    def start_build(self, build_id: str, commit_hash: str = "", author: str = "") -> bool:
        """Start a build job"""
        if build_id not in self.builds:
            return False
        
        build = self.builds[build_id]
        build.status = "running"
        build.started_at = datetime.now()
        build.commit_hash = commit_hash or f"abc{random.randint(1000, 9999)}"
        build.author = author or "developer"
        build.build_logs = ["Build started...", "Fetching source code...", "Installing dependencies..."]
        
        self.save_builds()
        return True
    
    def complete_build(self, build_id: str, success: bool = True, logs: List[str] = None) -> bool:
        """Complete a build job"""
        if build_id not in self.builds:
            return False
        
        build = self.builds[build_id]
        build.status = "success" if success else "failed"
        build.completed_at = datetime.now()
        
        if build.started_at:
            build.duration = int((build.completed_at - build.started_at).total_seconds())
        
        if logs:
            build.build_logs.extend(logs)
        else:
            if success:
                build.build_logs.extend([
                    "Running tests...", "Tests passed ✓", 
                    "Building artifacts...", "Build completed successfully ✓"
                ])
                build.artifacts = ["app.jar", "docker-image:latest", "test-reports.xml"]
                build.test_results = {"total": 45, "passed": 45, "failed": 0, "coverage": 92.5}
            else:
                build.build_logs.extend([
                    "Running tests...", "Test failures detected ✗", 
                    "Build failed ✗"
                ])
                build.test_results = {"total": 45, "passed": 42, "failed": 3, "coverage": 89.2}
        
        self.save_builds()
        return True
    
    def create_deployment(self, build_id: str, environment: str, version: str = "") -> str:
        """Create a new deployment"""
        deploy_id = f"deploy_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.deployments)}"
        
        deployment = Deployment(
            id=deploy_id,
            build_id=build_id,
            environment=environment,
            status="pending",
            created_at=datetime.now(),
            version=version or f"v1.{random.randint(0, 10)}.{random.randint(0, 10)}",
            approval_required=(environment in ["staging", "production"]),
            deployment_logs=[]
        )
        
        self.deployments[deploy_id] = deployment
        self.save_deployments()
        return deploy_id
    
    def approve_deployment(self, deploy_id: str, approver: str) -> bool:
        """Approve a deployment"""
        if deploy_id not in self.deployments:
            return False
        
        deployment = self.deployments[deploy_id]
        deployment.approved_by = approver
        deployment.status = "deploying"
        deployment.deployed_at = datetime.now()
        deployment.deployment_logs = [
            f"Deployment approved by {approver}",
            "Starting deployment...",
            "Deploying to environment..."
        ]
        
        self.save_deployments()
        return True
    
    def complete_deployment(self, deploy_id: str, success: bool = True) -> bool:
        """Complete a deployment"""
        if deploy_id not in self.deployments:
            return False
        
        deployment = self.deployments[deploy_id]
        deployment.status = "success" if success else "failed"
        
        if deployment.deployed_at:
            deployment.duration = int((datetime.now() - deployment.deployed_at).total_seconds())
        
        if success:
            deployment.deployment_logs.extend([
                "Application deployed successfully ✓",
                "Health checks passed ✓",
                "Deployment completed ✓"
            ])
        else:
            deployment.deployment_logs.extend([
                "Deployment failed ✗",
                "Rolling back changes...",
                "Rollback completed"
            ])
        
        self.save_deployments()
        return True
    
    def get_recent_builds(self, limit: int = 10) -> List[BuildJob]:
        """Get recent builds"""
        builds = list(self.builds.values())
        builds.sort(key=lambda x: x.created_at, reverse=True)
        return builds[:limit]
    
    def get_recent_deployments(self, limit: int = 10) -> List[Deployment]:
        """Get recent deployments"""
        deployments = list(self.deployments.values())
        deployments.sort(key=lambda x: x.created_at, reverse=True)
        return deployments[:limit]
    
    def get_pending_deployments(self) -> List[Deployment]:
        """Get deployments pending approval"""
        return [d for d in self.deployments.values() if d.status == "pending" and d.approval_required]
    
    def get_running_builds(self) -> List[BuildJob]:
        """Get currently running builds"""
        return [b for b in self.builds.values() if b.status == "running"]
    
    def _create_sample_builds(self):
        """Create sample builds for demonstration"""
        sample_projects = [
            ("Web Application", "https://github.com/company/web-app"),
            ("API Service", "https://github.com/company/api-service"),
            ("Mobile Backend", "https://github.com/company/mobile-backend")
        ]
        
        for name, repo in sample_projects:
            build_id = self.create_build(name, repo)
            self.start_build(build_id, f"abc{random.randint(1000, 9999)}", "developer")
            # Some completed, some running
            if random.random() > 0.3:
                self.complete_build(build_id, random.random() > 0.1)
    
    def _create_sample_deployments(self):
        """Create sample deployments for demonstration"""
        completed_builds = [b for b in self.builds.values() if b.status == "success"]
        
        for build in completed_builds[:3]:
            for env in ["development", "staging"]:
                deploy_id = self.create_deployment(build.id, env)
                if env == "development":
                    # Auto-approve dev deployments
                    self.approve_deployment(deploy_id, "auto-deploy")
                    self.complete_deployment(deploy_id, True)
                elif random.random() > 0.5:
                    # Some staging deployments approved
                    self.approve_deployment(deploy_id, "manager")
                    self.complete_deployment(deploy_id, random.random() > 0.1)
