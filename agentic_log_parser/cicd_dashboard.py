#!/usr/bin/env python3
"""
CI/CD Dashboard Components
Professional CI pipeline management and deployment interface with separate CI and CD views
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import threading
from cicd_manager import CICDManager

class CICDDashboard:
    """CI/CD Dashboard for build and deployment management"""
    
    def __init__(self):
        self.cicd_manager = CICDManager()
    
    def render_cicd_dashboard(self):
        """Render the main CI/CD dashboard with separate CI and CD tabs"""
        st.markdown("## 🚀 CI/CD Pipeline Dashboard")
        
        # Quick stats
        self.render_quick_stats()
        
        # Main dashboard tabs - now with separate CI and CD tabs
        tabs = st.tabs([
            "📊 Overview",
            "🔨 CI Pipeline", 
            "🚀 CD Pipeline",
            "📈 Analytics"
        ])
        
        with tabs[0]:
            self.render_overview_tab()
        
        with tabs[1]:
            self.render_ci_pipeline_tab()
        
        with tabs[2]:
            self.render_cd_pipeline_tab()
        
        with tabs[3]:
            self.render_analytics_tab()
    
    def render_ci_pipeline_tab(self):
        """Render CI pipeline tab with development, testing, and build stages"""
        st.markdown("### 🔨 Continuous Integration Pipeline")
        
        # CI Pipeline Stages
        stages = ["Development", "Testing", "Build"]
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("#### 👨‍💻 Development")
            recent_commits = self._get_recent_commits()
            
            for commit in recent_commits:
                st.markdown(f"""
                <div style="padding: 0.5rem; border-left: 4px solid #28a745; margin-bottom: 0.5rem; background-color: #f8f9fa;">
                    <strong>{commit['hash']}</strong><br>
                    <small>{commit['message']} | {commit['author']} | {commit['time']}</small>
                </div>
                """, unsafe_allow_html=True)
                
            if st.button("+ New Feature Branch", key="new_branch"):
                st.success("Feature branch created!")
        
        with col2:
            st.markdown("#### 🧪 Testing")
            recent_tests = self._get_recent_tests()
            
            for test in recent_tests:
                status_color = "#28a745" if test['status'] == "Passed" else "#dc3545"
                status_icon = "✅" if test['status'] == "Passed" else "❌"
                
                st.markdown(f"""
                <div style="padding: 0.5rem; border-left: 4px solid {status_color}; margin-bottom: 0.5rem; background-color: #f8f9fa;">
                    <strong>{status_icon} {test['name']}</strong><br>
                    <small>Coverage: {test['coverage']}% | Duration: {test['duration']}s</small>
                </div>
                """, unsafe_allow_html=True)
                
            if st.button("Run Tests", key="run_tests"):
                with st.spinner("Running tests..."):
                    time.sleep(2)
                    st.success("Tests completed!")
        
        with col3:
            st.markdown("#### 🏗️ Build")
            running_builds = self.cicd_manager.get_running_builds()
            
            for build in running_builds:
                with st.expander(f"🔨 {build.name} - {build.status.title()}", expanded=True):
                    st.markdown(f"**Branch:** {build.branch}")
                    st.markdown(f"**Started:** {build.started_at.strftime('%H:%M:%S') if build.started_at else 'N/A'}")
                    
                    # Progress simulation
                    progress = min(90, (datetime.now() - build.started_at).seconds / 60 * 30) if build.started_at else 0
                    st.progress(progress / 100)
                    st.caption(f"Progress: {progress:.0f}%")
                    
                    if st.button("🛑 Cancel", key=f"cancel_build_{build.id}"):
                        self.cicd_manager.complete_build(build.id, False, ["Build cancelled by user"])
                        st.rerun()
            
            if st.button("+ New Build", key="new_build"):
                st.session_state.show_build_form = True
                st.rerun()
        
        # CI Metrics
        st.markdown("### 📊 CI Pipeline Metrics")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Commits Today", "15", "+3")
        
        with col2:
            st.metric("Test Pass Rate", "92%", "-2%")
        
        with col3:
            st.metric("Code Coverage", "87%", "+1%")
        
        with col4:
            st.metric("Avg Build Time", "3.2 min", "-0.5 min")
        
        # Build history
        self.render_builds_tab()
    
    def render_cd_pipeline_tab(self):
        """Render CD pipeline tab with deployment stages"""
        st.markdown("### 🚀 Continuous Deployment Pipeline")
        
        # Environment status overview
        st.markdown("#### 🌍 Environment Status")
        environments = ["development", "staging", "production"]
        cols = st.columns(len(environments))
        
        for i, env in enumerate(environments):
            with cols[i]:
                # Get latest deployment for this environment
                env_deployments = [d for d in self.cicd_manager.deployments.values() if d.environment == env]
                latest_deployment = max(env_deployments, key=lambda x: x.created_at) if env_deployments else None
                
                status_color = {
                    'success': '#28a745',
                    'failed': '#dc3545',
                    'deploying': '#007bff',
                    'pending': '#ffc107'
                }.get(latest_deployment.status if latest_deployment else 'unknown', '#6c757d')
                
                st.markdown(f"""
                <div style="background: {status_color}; color: white; padding: 1.5rem; border-radius: 10px; margin-bottom: 1rem;">
                    <h3>{env.title()}</h3>
                    <p><strong>Version:</strong> {latest_deployment.version if latest_deployment else 'None'}</p>
                    <p><strong>Status:</strong> {latest_deployment.status.title() if latest_deployment else 'Unknown'}</p>
                    <p><strong>Last Deploy:</strong> {latest_deployment.created_at.strftime('%m/%d %H:%M') if latest_deployment else 'Never'}</p>
                </div>
                """, unsafe_allow_html=True)
        
        # Pending deployments
        pending_deployments = self.cicd_manager.get_pending_deployments()
        if pending_deployments:
            st.markdown("#### ⏳ Pending Approvals")
            for deployment in pending_deployments:
                build = self.cicd_manager.builds.get(deployment.build_id)
                build_name = build.name if build else "Unknown Build"
                
                with st.expander(f"🚀 {build_name} → {deployment.environment.title()}", expanded=True):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.markdown(f"**Environment:** {deployment.environment}")
                        st.markdown(f"**Version:** {deployment.version}")
                        st.markdown(f"**Created:** {deployment.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
                        if build:
                            st.markdown(f"**Build Status:** {build.status}")
                    
                    with col2:
                        approver = st.text_input("Approved by:", key=f"approver_{deployment.id}")
                        
                        col_approve, col_reject = st.columns(2)
                        with col_approve:
                            if st.button("✅ Approve", key=f"approve_{deployment.id}"):
                                if approver:
                                    self.cicd_manager.approve_deployment(deployment.id, approver)
                                    # Simulate deployment completion
                                    threading.Timer(2.0, lambda: self.cicd_manager.complete_deployment(deployment.id, True)).start()
                                    st.success(f"Deployment approved by {approver}")
                                    st.rerun()
                                else:
                                    st.error("Please enter approver name")
                        
                        with col_reject:
                            if st.button("❌ Reject", key=f"reject_{deployment.id}"):
                                st.warning("Deployment rejected")
        
        # Quick deploy section
        st.markdown("#### ⚡ Quick Deploy")
        
        successful_builds = [b for b in self.cicd_manager.builds.values() if b.status == "success"]
        
        if successful_builds:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                selected_build = st.selectbox(
                    "Build:",
                    options=[b.id for b in successful_builds],
                    format_func=lambda x: next(b.name for b in successful_builds if b.id == x)
                )
            
            with col2:
                environment = st.selectbox(
                    "Environment:",
                    options=["development", "staging", "production"]
                )
            
            with col3:
                version = st.text_input("Version:", value=f"v1.{len(self.cicd_manager.deployments)}.0")
            
            with col4:
                st.markdown("<br>", unsafe_allow_html=True)  # Spacing
                if st.button("🚀 Deploy", use_container_width=True):
                    if selected_build and version:
                        deploy_id = self.cicd_manager.create_deployment(selected_build, environment, version)
                        
                        if environment == "development":
                            self.cicd_manager.approve_deployment(deploy_id, "auto-deploy")
                            threading.Timer(3.0, lambda: self.cicd_manager.complete_deployment(deploy_id, True)).start()
                            st.success(f"Deployment to {environment} started!")
                        else:
                            st.info(f"Deployment to {environment} created and pending approval")
                        
                        st.rerun()
                    else:
                        st.error("Please select build and enter version")
        else:
            st.warning("No successful builds available for deployment")
        
        # Deployment history
        self.render_deployments_tab()
    
    def _get_recent_commits(self):
        """Get recent commits for the CI pipeline view"""
        # This would typically come from a Git API
        return [
            {"hash": "a1b2c3d", "message": "Fix login bug", "author": "<EMAIL>", "time": "10:45 AM"},
            {"hash": "e5f6g7h", "message": "Add new feature", "author": "<EMAIL>", "time": "09:30 AM"},
            {"hash": "i9j0k1l", "message": "Update dependencies", "author": "<EMAIL>", "time": "Yesterday"}
        ]
    
    def _get_recent_tests(self):
        """Get recent test runs for the CI pipeline view"""
        # This would typically come from a test runner API
        return [
            {"name": "Unit Tests", "status": "Passed", "coverage": "92", "duration": "45"},
            {"name": "Integration Tests", "status": "Passed", "coverage": "87", "duration": "120"},
            {"name": "UI Tests", "status": "Failed", "coverage": "75", "duration": "90"}
        ]

    def render_quick_stats(self):
        """Render quick statistics"""
        col1, col2, col3, col4 = st.columns(4)
        
        recent_builds = self.cicd_manager.get_recent_builds(30)
        recent_deployments = self.cicd_manager.get_recent_deployments(30)
        running_builds = self.cicd_manager.get_running_builds()
        pending_deployments = self.cicd_manager.get_pending_deployments()
        
        with col1:
            st.metric("🔨 Total Builds", len(recent_builds))
        
        with col2:
            success_rate = len([b for b in recent_builds if b.status == "success"]) / max(len(recent_builds), 1) * 100
            st.metric("✅ Success Rate", f"{success_rate:.1f}%")
        
        with col3:
            st.metric("🚀 Active Deployments", len([d for d in recent_deployments if d.status == "deploying"]))
        
        with col4:
            st.metric("⏳ Pending Approvals", len(pending_deployments))
    
    def render_overview_tab(self):
        """Render overview tab with pipeline status"""
        st.markdown("### 🔄 Pipeline Status")
        
        # CI/CD Pipeline visualization
        st.markdown("#### 🔄 CI/CD Pipeline Flow")
        
        # Create a visual pipeline flow
        pipeline_stages = [
            {"name": "Development", "status": "✅", "type": "CI"},
            {"name": "Testing", "status": "✅", "type": "CI"},
            {"name": "Build", "status": "🔄", "type": "CI"},
            {"name": "Deployment", "status": "⏳", "type": "CD"},
            {"name": "Monitoring", "status": "⏳", "type": "CD"}
        ]
        
        cols = st.columns(len(pipeline_stages))
        
        for i, stage in enumerate(pipeline_stages):
            with cols[i]:
                badge_color = "#0d6efd" if stage["type"] == "CI" else "#6610f2"
                badge_text = "CI" if stage["type"] == "CI" else "CD"
                
                st.markdown(f"""
                <div style="text-align: center; padding: 1rem; border: 1px solid #dee2e6; border-radius: 0.5rem; margin-bottom: 1rem;">
                    <div style="display: inline-block; background-color: {badge_color}; color: white; padding: 0.2rem 0.5rem; border-radius: 0.25rem; font-size: 0.8rem; margin-bottom: 0.5rem;">
                        {badge_text}
                    </div>
                    <h4>{stage["status"]} {stage["name"]}</h4>
                </div>
                """, unsafe_allow_html=True)
        
        # Running builds
        running_builds = self.cicd_manager.get_running_builds()
        if running_builds:
            st.markdown("#### ⚡ Active Builds (CI)")
            for build in running_builds:
                with st.expander(f"🔨 {build.name} - {build.status.title()}", expanded=True):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.markdown(f"**Repository:** {build.repository}")
                        st.markdown(f"**Branch:** {build.branch}")
                        st.markdown(f"**Started:** {build.started_at.strftime('%H:%M:%S') if build.started_at else 'N/A'}")
                        st.markdown(f"**Author:** {build.author}")
                        
                        # Progress simulation
                        progress = min(90, (datetime.now() - build.started_at).seconds / 60 * 30) if build.started_at else 0
                        st.progress(progress / 100)
                        st.caption(f"Progress: {progress:.0f}%")
                    
                    with col2:
                        if st.button("🛑 Cancel", key=f"cancel_build_{build.id}"):
                            self.cicd_manager.complete_build(build.id, False, ["Build cancelled by user"])
                            st.rerun()
        
        # Pending deployments
        pending_deployments = self.cicd_manager.get_pending_deployments()
        if pending_deployments:
            st.markdown("#### ⏳ Pending Approvals (CD)")
            for deployment in pending_deployments:
                build = self.cicd_manager.builds.get(deployment.build_id)
                build_name = build.name if build else "Unknown Build"
                
                with st.expander(f"🚀 {build_name} → {deployment.environment.title()}", expanded=True):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.markdown(f"**Environment:** {deployment.environment}")
                        st.markdown(f"**Version:** {deployment.version}")
                        st.markdown(f"**Created:** {deployment.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
                        if build:
                            st.markdown(f"**Build Status:** {build.status}")
                    
                    with col2:
                        approver = st.text_input("Approved by:", key=f"approver_{deployment.id}")
                        
                        col_approve, col_reject = st.columns(2)
                        with col_approve:
                            if st.button("✅ Approve", key=f"approve_{deployment.id}"):
                                if approver:
                                    self.cicd_manager.approve_deployment(deployment.id, approver)
                                    # Simulate deployment completion
                                    threading.Timer(2.0, lambda: self.cicd_manager.complete_deployment(deployment.id, True)).start()
                                    st.success(f"Deployment approved by {approver}")
                                    st.rerun()
                                else:
                                    st.error("Please enter approver name")
                        
                        with col_reject:
                            if st.button("❌ Reject", key=f"reject_{deployment.id}"):
                                st.warning("Deployment rejected")
        
        # Recent activity
        st.markdown("### 📈 Recent Activity")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 🔨 Recent CI Activity")
            recent_builds = self.cicd_manager.get_recent_builds(5)
            
            for build in recent_builds:
                status_icon = {
                    "success": "✅",
                    "failed": "❌", 
                    "running": "🔄",
                    "pending": "⏳"
                }.get(build.status, "❓")
                
                status_color = {
                    "success": "green",
                    "failed": "red",
                    "running": "blue", 
                    "pending": "orange"
                }.get(build.status, "gray")
                
                st.markdown(f"""
                <div style="padding: 0.5rem; border-left: 4px solid {status_color}; margin-bottom: 0.5rem; background-color: #f8f9fa;">
                    <strong>{status_icon} {build.name}</strong><br>
                    <small>Branch: {build.branch} | {build.created_at.strftime('%H:%M:%S')}</small>
                </div>
                """, unsafe_allow_html=True)
        
        with col2:
            st.markdown("#### 🚀 Recent CD Activity")
            recent_deployments = self.cicd_manager.get_recent_deployments(5)
            
            for deployment in recent_deployments:
                status_icon = {
                    "success": "✅",
                    "failed": "❌",
                    "deploying": "🔄",
                    "pending": "⏳"
                }.get(deployment.status, "❓")
                
                status_color = {
                    "success": "green",
                    "failed": "red", 
                    "deploying": "blue",
                    "pending": "orange"
                }.get(deployment.status, "gray")
                
                st.markdown(f"""
                <div style="padding: 0.5rem; border-left: 4px solid {status_color}; margin-bottom: 0.5rem; background-color: #f8f9fa;">
                    <strong>{status_icon} {deployment.environment.title()}</strong><br>
                    <small>Version: {deployment.version} | {deployment.created_at.strftime('%H:%M:%S')}</small>
                </div>
                """, unsafe_allow_html=True)
    
    def render_builds_tab(self):
        """Render builds management tab"""
        st.markdown("### 🔨 Build Management")
        
        # Build controls
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.markdown("#### 🛠️ Create New Build")
        
        with col2:
            if st.button("➕ New Build", use_container_width=True):
                st.session_state.show_build_form = True
        
        with col3:
            if st.button("🔄 Refresh", use_container_width=True, key="builds_refresh"):
                st.rerun()
        
        # New build form
        if st.session_state.get('show_build_form', False):
            with st.form("new_build_form"):
                st.markdown("#### ➕ Create New Build")
                
                col1, col2 = st.columns(2)
                with col1:
                    build_name = st.text_input("Build Name", placeholder="e.g., Web Application")
                    repository = st.text_input("Repository URL", placeholder="https://github.com/company/repo")
                
                with col2:
                    branch = st.text_input("Branch", value="main")
                    commit_hash = st.text_input("Commit Hash", placeholder="abc1234")
                
                author = st.text_input("Author", placeholder="<EMAIL>")
                
                col1, col2 = st.columns(2)
                with col1:
                    if st.form_submit_button("🚀 Start Build", use_container_width=True):
                        if build_name and repository:
                            build_id = self.cicd_manager.create_build(build_name, repository, branch)
                            self.cicd_manager.start_build(build_id, commit_hash, author)
                            
                            # Simulate build completion after a delay
                            threading.Timer(5.0, lambda: self.cicd_manager.complete_build(build_id, True)).start()
                            
                            st.success(f"Build started: {build_name}")
                            st.session_state.show_build_form = False
                            st.rerun()
                        else:
                            st.error("Please fill in required fields")
                
                with col2:
                    if st.form_submit_button("❌ Cancel", use_container_width=True):
                        st.session_state.show_build_form = False
                        st.rerun()
        
        # Builds table
        st.markdown("### 📋 Build History")
        
        builds = self.cicd_manager.get_recent_builds(20)
        
        if builds:
            build_data = []
            for build in builds:
                build_data.append({
                    'Name': build.name,
                    'Status': build.status,
                    'Branch': build.branch,
                    'Author': build.author,
                    'Duration': f"{build.duration}s" if build.duration else "N/A",
                    'Created': build.created_at.strftime('%m/%d %H:%M'),
                    'ID': build.id
                })
            
            df = pd.DataFrame(build_data)
            
            # Display table
            st.dataframe(
                df.drop('ID', axis=1),
                use_container_width=True,
                hide_index=True
            )
            
            # Build actions
            selected_build = st.selectbox(
                "Select build for actions:",
                options=[b.id for b in builds],
                format_func=lambda x: next(b.name for b in builds if b.id == x)
            )
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("📊 View Details", key="build_details"):
                    self._show_build_details(selected_build)

            with col2:
                if st.button("🚀 Deploy", key="build_deploy"):
                    self._show_deployment_form(selected_build)

            with col3:
                if st.button("🗑️ Delete", key="build_delete"):
                    if selected_build in self.cicd_manager.builds:
                        del self.cicd_manager.builds[selected_build]
                        self.cicd_manager.save_builds()
                        st.success("Build deleted!")
                        st.rerun()

    def render_deployments_tab(self):
        """Render deployments management tab"""
        st.markdown("### 🚀 Deployment Management")

        # Environment status overview
        st.markdown("#### 🌍 Environment Status")

        environments = ["development", "staging", "production"]
        cols = st.columns(len(environments))

        for i, env in enumerate(environments):
            with cols[i]:
                # Get latest deployment for this environment
                env_deployments = [d for d in self.cicd_manager.deployments.values() if d.environment == env]
                latest_deployment = max(env_deployments, key=lambda x: x.created_at) if env_deployments else None

                status_color = {
                    'success': '#28a745',
                    'failed': '#dc3545',
                    'deploying': '#007bff',
                    'pending': '#ffc107'
                }.get(latest_deployment.status if latest_deployment else 'unknown', '#6c757d')

                st.markdown(f"""
                <div style="background: {status_color}; color: white; padding: 1.5rem; border-radius: 10px; margin-bottom: 1rem;">
                    <h3>{env.title()}</h3>
                    <p><strong>Version:</strong> {latest_deployment.version if latest_deployment else 'None'}</p>
                    <p><strong>Status:</strong> {latest_deployment.status.title() if latest_deployment else 'Unknown'}</p>
                    <p><strong>Last Deploy:</strong> {latest_deployment.created_at.strftime('%m/%d %H:%M') if latest_deployment else 'Never'}</p>
                </div>
                """, unsafe_allow_html=True)

        # Deployment history
        st.markdown("#### 📈 Deployment History")

        deployments = self.cicd_manager.get_recent_deployments(15)

        if deployments:
            deployment_data = []
            for deployment in deployments:
                build = self.cicd_manager.builds.get(deployment.build_id)
                deployment_data.append({
                    'Build': build.name if build else 'Unknown',
                    'Environment': deployment.environment.title(),
                    'Version': deployment.version,
                    'Status': deployment.status,
                    'Duration': f"{deployment.duration}s" if deployment.duration else "N/A",
                    'Deployed': deployment.deployed_at.strftime('%m/%d %H:%M') if deployment.deployed_at else "N/A",
                    'Approved By': deployment.approved_by or "N/A"
                })

            df = pd.DataFrame(deployment_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
        else:
            st.info("No deployments found")

        # Quick deploy section
        st.markdown("#### ⚡ Quick Deploy")

        successful_builds = [b for b in self.cicd_manager.builds.values() if b.status == "success"]

        if successful_builds:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                selected_build = st.selectbox(
                    "Build:",
                    options=[b.id for b in successful_builds],
                    format_func=lambda x: next(b.name for b in successful_builds if b.id == x)
                )

            with col2:
                environment = st.selectbox(
                    "Environment:",
                    options=["development", "staging", "production"]
                )

            with col3:
                version = st.text_input("Version:", value=f"v1.{len(self.cicd_manager.deployments)}.0")

            with col4:
                st.markdown("<br>", unsafe_allow_html=True)  # Spacing
                if st.button("🚀 Deploy", use_container_width=True):
                    if selected_build and version:
                        deploy_id = self.cicd_manager.create_deployment(selected_build, environment, version)

                        if environment == "development":
                            self.cicd_manager.approve_deployment(deploy_id, "auto-deploy")
                            threading.Timer(3.0, lambda: self.cicd_manager.complete_deployment(deploy_id, True)).start()
                            st.success(f"Deployment to {environment} started!")
                        else:
                            st.info(f"Deployment to {environment} created and pending approval")

                        st.rerun()
                    else:
                        st.error("Please select build and enter version")
        else:
            st.warning("No successful builds available for deployment")

    def render_analytics_tab(self):
        """Render analytics and metrics tab with separate CI and CD metrics"""
        st.markdown("### 📈 CI/CD Analytics")

        # Separate CI and CD metrics
        st.markdown("#### 🔨 CI Metrics")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            success_rate = len([b for b in self.cicd_manager.builds.values() if b.status == "success"]) / max(len(self.cicd_manager.builds), 1) * 100
            st.metric("🎯 Build Success Rate", f"{success_rate:.1f}%")

        with col2:
            avg_build_time = np.mean([b.duration for b in self.cicd_manager.builds.values() if b.duration]) if self.cicd_manager.builds else 0
            st.metric("⏱️ Avg Build Time", f"{avg_build_time:.0f}s")

        with col3:
            daily_builds = len([b for b in self.cicd_manager.builds.values() if b.created_at.date() == datetime.now().date()])
            st.metric("📊 Builds Today", daily_builds)

        with col4:
            st.metric("🧪 Test Coverage", "87%", "+2%")

        st.markdown("#### 🚀 CD Metrics")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            deploy_success_rate = len([d for d in self.cicd_manager.deployments.values() if d.status == "success"]) / max(len(self.cicd_manager.deployments), 1) * 100
            st.metric("🚀 Deploy Success Rate", f"{deploy_success_rate:.1f}%")

        with col2:
            avg_deployment_time = np.mean([d.duration for d in self.cicd_manager.deployments.values() if d.duration]) if self.cicd_manager.deployments else 0
            st.metric("⏱️ Avg Deployment Time", f"{avg_deployment_time:.0f}s")

        with col3:
            pending_deployments = len(self.cicd_manager.get_pending_deployments())
            st.metric("⏳ Pending Deployments", pending_deployments)

        with col4:
            st.metric("🌍 Environments", len(["development", "staging", "production"]))

        # Charts
        col1, col2 = st.columns(2)

        with col1:
            # Build status distribution
            if self.cicd_manager.builds:
                status_counts = {}
                for build in self.cicd_manager.builds.values():
                    status_counts[build.status] = status_counts.get(build.status, 0) + 1

                fig = go.Figure(data=[go.Pie(
                    labels=list(status_counts.keys()),
                    values=list(status_counts.values()),
                    hole=0.4
                )])

                fig.update_layout(
                    title="Build Status Distribution",
                    height=300
                )

                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No build data available")

        with col2:
            # Deployment frequency
            if self.cicd_manager.deployments:
                # Group deployments by date
                deployment_dates = [d.created_at.date() for d in self.cicd_manager.deployments.values()]
                date_counts = {}

                # Generate last 7 days
                for i in range(7):
                    date = (datetime.now() - timedelta(days=i)).date()
                    date_counts[date] = deployment_dates.count(date)

                dates = list(date_counts.keys())
                counts = list(date_counts.values())

                fig = go.Figure(data=[go.Bar(
                    x=dates,
                    y=counts,
                    marker_color='#2a5298'
                )])

                fig.update_layout(
                    title="Deployment Frequency (Last 7 Days)",
                    xaxis_title="Date",
                    yaxis_title="Deployments",
                    height=300
                )

                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No deployment data available")

        # Build duration trend
        if self.cicd_manager.builds:
            st.markdown("#### ⏱️ Build Duration Trend")

            # Filter builds with duration data
            builds_with_duration = [b for b in self.cicd_manager.builds.values() if b.duration and b.completed_at]
            builds_with_duration.sort(key=lambda x: x.completed_at)

            if builds_with_duration:
                dates = [b.completed_at for b in builds_with_duration]
                durations = [b.duration / 60 for b in builds_with_duration]  # Convert to minutes

                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=dates,
                    y=durations,
                    mode='lines+markers',
                    name='Build Duration',
                    line=dict(color='#007bff', width=3)
                ))

                fig.update_layout(
                    title="Build Duration Over Time",
                    xaxis_title="Date",
                    yaxis_title="Duration (minutes)",
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No build duration data available")

        # Environment deployment stats
        st.markdown("#### 🌍 Environment Deployment Statistics")

        env_stats = {}
        for env in ["development", "staging", "production"]:
            env_deployments = [d for d in self.cicd_manager.deployments.values() if d.environment == env]
            env_stats[env] = {
                'total': len(env_deployments),
                'success': len([d for d in env_deployments if d.status == "success"]),
                'failed': len([d for d in env_deployments if d.status == "failed"]),
                'success_rate': len([d for d in env_deployments if d.status == "success"]) / max(len(env_deployments), 1) * 100
            }

        env_df = pd.DataFrame(env_stats).T
        env_df.index.name = 'Environment'
        env_df = env_df.reset_index()
        env_df['Environment'] = env_df['Environment'].str.title()

        st.dataframe(env_df, use_container_width=True, hide_index=True)
    
    def _show_build_details(self, build_id: str):
        """Show detailed build information"""
        build = self.cicd_manager.builds.get(build_id)
        if not build:
            st.error("Build not found")
            return
        
        st.markdown(f"### 📊 Build Details: {build.name}")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Basic Information:**")
            st.markdown(f"- **ID:** {build.id}")
            st.markdown(f"- **Repository:** {build.repository}")
            st.markdown(f"- **Branch:** {build.branch}")
            st.markdown(f"- **Status:** {build.status}")
            st.markdown(f"- **Author:** {build.author}")
            st.markdown(f"- **Commit:** {build.commit_hash}")
        
        with col2:
            st.markdown("**Timing:**")
            st.markdown(f"- **Created:** {build.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if build.started_at:
                st.markdown(f"- **Started:** {build.started_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if build.completed_at:
                st.markdown(f"- **Completed:** {build.completed_at.strftime('%Y-%m-%d %H:%M:%S')}")
            if build.duration:
                st.markdown(f"- **Duration:** {build.duration} seconds")
        
        # Test results
        if build.test_results:
            st.markdown("**Test Results:**")
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Tests", build.test_results.get('total', 0))
            with col2:
                st.metric("Passed", build.test_results.get('passed', 0))
            with col3:
                st.metric("Failed", build.test_results.get('failed', 0))
            with col4:
                st.metric("Coverage", f"{build.test_results.get('coverage', 0)}%")
        
        # Build logs
        if build.build_logs:
            st.markdown("**Build Logs:**")
            for log in build.build_logs:
                st.code(log)
        
        # Artifacts
        if build.artifacts:
            st.markdown("**Artifacts:**")
            for artifact in build.artifacts:
                st.markdown(f"- 📦 {artifact}")
    
    def _show_deployment_form(self, build_id: str):
        """Show deployment form for a build"""
        build = self.cicd_manager.builds.get(build_id)
        if not build:
            st.error("Build not found")
            return
        
        if build.status != "success":
            st.error("Can only deploy successful builds")
            return
        
        st.markdown(f"### 🚀 Deploy: {build.name}")
        
        with st.form("deployment_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                environment = st.selectbox(
                    "Environment:",
                    ["development", "staging", "production"]
                )
            
            with col2:
                version = st.text_input("Version:", value=f"v1.{len(self.cicd_manager.deployments)}.0")
            
            if st.form_submit_button("🚀 Deploy"):
                deploy_id = self.cicd_manager.create_deployment(build_id, environment, version)
                
                if environment == "development":
                    # Auto-approve dev deployments
                    self.cicd_manager.approve_deployment(deploy_id, "auto-deploy")
                    threading.Timer(3.0, lambda: self.cicd_manager.complete_deployment(deploy_id, True)).start()
                    st.success(f"Deployment to {environment} started!")
                else:
                    st.info(f"Deployment to {environment} created and pending approval")
                
                st.rerun()
