{"build_20250609_213848_0": {"id": "build_20250609_213848_0", "name": "Web Application", "repository": "https://github.com/company/web-app", "branch": "main", "status": "success", "created_at": "2025-06-09T21:38:48.412269", "started_at": "2025-06-09T21:38:48.412733", "completed_at": "2025-06-09T21:38:48.413035", "duration": 0, "commit_hash": "abc9473", "author": "developer", "build_logs": ["Build started...", "Fetching source code...", "Installing dependencies...", "Running tests...", "Tests passed ✓", "Building artifacts...", "Build completed successfully ✓"], "artifacts": ["app.jar", "docker-image:latest", "test-reports.xml"], "test_results": {"total": 45, "passed": 45, "failed": 0, "coverage": 92.5}}, "build_20250609_213848_1": {"id": "build_20250609_213848_1", "name": "API Service", "repository": "https://github.com/company/api-service", "branch": "main", "status": "running", "created_at": "2025-06-09T21:38:48.413355", "started_at": "2025-06-09T21:38:48.413636", "completed_at": null, "duration": null, "commit_hash": "abc4153", "author": "developer", "build_logs": ["Build started...", "Fetching source code...", "Installing dependencies..."], "artifacts": [], "test_results": {}}, "build_20250609_213848_2": {"id": "build_20250609_213848_2", "name": "Mobile Backend", "repository": "https://github.com/company/mobile-backend", "branch": "main", "status": "success", "created_at": "2025-06-09T21:38:48.413888", "started_at": "2025-06-09T21:38:48.414237", "completed_at": "2025-06-09T21:38:48.414550", "duration": 0, "commit_hash": "abc4560", "author": "developer", "build_logs": ["Build started...", "Fetching source code...", "Installing dependencies...", "Running tests...", "Tests passed ✓", "Building artifacts...", "Build completed successfully ✓"], "artifacts": ["app.jar", "docker-image:latest", "test-reports.xml"], "test_results": {"total": 45, "passed": 45, "failed": 0, "coverage": 92.5}}}