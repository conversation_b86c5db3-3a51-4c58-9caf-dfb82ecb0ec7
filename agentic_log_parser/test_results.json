[{"id": "test_0", "suite_name": "Test Suite 1", "status": "passed", "total_tests": 81, "passed_tests": 81, "failed_tests": 0, "duration": 33.7870542130005, "coverage": 61.081034738747576, "timestamp": "2025-06-09T21:58:11.552678", "failures": []}, {"id": "test_1", "suite_name": "Test Suite 2", "status": "passed", "total_tests": 54, "passed_tests": 54, "failed_tests": 0, "duration": 101.1994849082016, "coverage": 61.16057223783276, "timestamp": "2025-06-09T20:58:11.552724", "failures": []}, {"id": "test_2", "suite_name": "Test Suite 3", "status": "failed", "total_tests": 87, "passed_tests": 61, "failed_tests": 26, "duration": 246.89898878209772, "coverage": 75.28845370307214, "timestamp": "2025-06-09T19:58:11.552749", "failures": [{"type": "AssertionError", "message": "Test assertion failed"}, {"type": "ValueError", "message": "Invalid value provided"}]}, {"id": "test_3", "suite_name": "Test Suite 1", "status": "failed", "total_tests": 99, "passed_tests": 56, "failed_tests": 43, "duration": 97.68449957446283, "coverage": 74.6907118073082, "timestamp": "2025-06-09T18:58:11.552771", "failures": [{"type": "AssertionError", "message": "Test assertion failed"}, {"type": "ValueError", "message": "Invalid value provided"}]}, {"id": "test_4", "suite_name": "Test Suite 2", "status": "passed", "total_tests": 53, "passed_tests": 53, "failed_tests": 0, "duration": 47.433592703935425, "coverage": 67.59832709685857, "timestamp": "2025-06-09T17:58:11.552791", "failures": []}, {"id": "test_5", "suite_name": "Test Suite 3", "status": "passed", "total_tests": 26, "passed_tests": 26, "failed_tests": 0, "duration": 60.00144964351406, "coverage": 71.34852636307994, "timestamp": "2025-06-09T16:58:11.552811", "failures": []}, {"id": "test_6", "suite_name": "Test Suite 1", "status": "passed", "total_tests": 81, "passed_tests": 81, "failed_tests": 0, "duration": 66.68916917911528, "coverage": 69.4992785869145, "timestamp": "2025-06-09T15:58:11.552831", "failures": []}, {"id": "test_7", "suite_name": "Test Suite 2", "status": "passed", "total_tests": 98, "passed_tests": 98, "failed_tests": 0, "duration": 229.17202014359762, "coverage": 86.74676497673029, "timestamp": "2025-06-09T14:58:11.552849", "failures": []}, {"id": "test_8", "suite_name": "Test Suite 3", "status": "passed", "total_tests": 65, "passed_tests": 65, "failed_tests": 0, "duration": 203.0487308282081, "coverage": 75.13028071377349, "timestamp": "2025-06-09T13:58:11.552868", "failures": []}, {"id": "test_9", "suite_name": "Test Suite 1", "status": "passed", "total_tests": 11, "passed_tests": 11, "failed_tests": 0, "duration": 112.33188203853783, "coverage": 78.09788135578339, "timestamp": "2025-06-09T12:58:11.552886", "failures": []}, {"id": "test_10", "suite_name": "Test Suite 2", "status": "passed", "total_tests": 76, "passed_tests": 76, "failed_tests": 0, "duration": 267.55919273252937, "coverage": 72.90316163019747, "timestamp": "2025-06-09T11:58:11.552904", "failures": []}, {"id": "test_11", "suite_name": "Test Suite 3", "status": "passed", "total_tests": 64, "passed_tests": 64, "failed_tests": 0, "duration": 136.77517721802764, "coverage": 83.90841721134899, "timestamp": "2025-06-09T10:58:11.552921", "failures": []}, {"id": "test_12", "suite_name": "Test Suite 1", "status": "passed", "total_tests": 57, "passed_tests": 57, "failed_tests": 0, "duration": 95.6382205117185, "coverage": 73.89368467110407, "timestamp": "2025-06-09T09:58:11.552940", "failures": []}, {"id": "test_13", "suite_name": "Test Suite 2", "status": "failed", "total_tests": 90, "passed_tests": 62, "failed_tests": 28, "duration": 220.23110413756714, "coverage": 72.61299631142678, "timestamp": "2025-06-09T08:58:11.552958", "failures": [{"type": "AssertionError", "message": "Test assertion failed"}, {"type": "ValueError", "message": "Invalid value provided"}]}, {"id": "test_14", "suite_name": "Test Suite 3", "status": "passed", "total_tests": 38, "passed_tests": 38, "failed_tests": 0, "duration": 181.47679669336787, "coverage": 67.36315315424908, "timestamp": "2025-06-09T07:58:11.552975", "failures": []}, {"id": "test_15", "suite_name": "Test Suite 1", "status": "passed", "total_tests": 58, "passed_tests": 58, "failed_tests": 0, "duration": 111.99822277979423, "coverage": 81.62522165436428, "timestamp": "2025-06-09T06:58:11.552993", "failures": []}, {"id": "test_16", "suite_name": "Test Suite 2", "status": "passed", "total_tests": 54, "passed_tests": 54, "failed_tests": 0, "duration": 37.5959593420157, "coverage": 94.30631653617729, "timestamp": "2025-06-09T05:58:11.553010", "failures": []}, {"id": "test_17", "suite_name": "Test Suite 3", "status": "passed", "total_tests": 78, "passed_tests": 78, "failed_tests": 0, "duration": 38.95203291764201, "coverage": 63.226167094260646, "timestamp": "2025-06-09T04:58:11.553030", "failures": []}, {"id": "test_18", "suite_name": "Test Suite 1", "status": "passed", "total_tests": 22, "passed_tests": 22, "failed_tests": 0, "duration": 175.03666140740214, "coverage": 79.47658581197331, "timestamp": "2025-06-09T03:58:11.553048", "failures": []}, {"id": "test_19", "suite_name": "Test Suite 2", "status": "passed", "total_tests": 23, "passed_tests": 23, "failed_tests": 0, "duration": 83.24829098052531, "coverage": 64.08502765259409, "timestamp": "2025-06-09T02:58:11.553065", "failures": []}]