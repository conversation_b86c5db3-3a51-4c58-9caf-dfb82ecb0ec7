#!/usr/bin/env python3
"""
Test script for Enhanced AI Log Analyzer features
"""

from enhanced_agent import EnhancedLogAnalyzer
import json

# Sample log data for testing
sample_logs = """
2024-06-09 10:15:23 [INFO] Application started successfully
2024-06-09 10:16:45 [ERROR] Database connection failed: Connection timeout after 30 seconds
2024-06-09 10:17:12 [WARNING] High memory usage detected: 85% of heap space used
2024-06-09 10:18:33 [ERROR] Network timeout: Unable to reach host api.example.com
2024-06-09 10:19:44 [INFO] User authentication successful for user: john.doe
2024-06-09 10:20:15 [ERROR] Authentication failed: Invalid credentials for user: jane.smith
2024-06-09 10:21:26 [WARNING] Disk space running low: 90% capacity reached
2024-06-09 10:22:37 [ERROR] Out of memory: Java heap space exceeded
2024-06-09 10:23:48 [INFO] Backup process completed successfully
2024-06-09 10:24:59 [ERROR] Database query failed: SQL syntax error in SELECT statement
2024-06-09 10:25:10 [WARNING] SSL certificate expires in 7 days
2024-06-09 10:26:21 [ERROR] Permission denied: User lacks write access to /var/log/
2024-06-09 10:27:32 [INFO] Cache cleared successfully
2024-06-09 10:28:43 [ERROR] Connection refused: Port 8080 is not accessible
2024-06-09 10:29:54 [WARNING] CPU usage spike detected: 95% utilization
"""

def test_enhanced_features():
    """Test the enhanced AI features"""
    print("🧪 Testing Enhanced AI Log Analyzer Features")
    print("=" * 60)
    
    # Initialize analyzer
    print("\n1. Initializing Enhanced Log Analyzer...")
    analyzer = EnhancedLogAnalyzer(sample_logs, enable_deep_learning=False)
    print("✅ Analyzer initialized successfully")
    
    # Test error classification
    print("\n2. Testing Error Classification...")
    errors = [line for line in sample_logs.splitlines() if '[ERROR]' in line]
    print(f"Found {len(errors)} errors to classify")
    
    classification_results = analyzer.classify_errors(errors)
    print("✅ Error classification completed")
    
    for category, classified_errors in classification_results.items():
        if classified_errors:
            print(f"  - {category.title()}: {len(classified_errors)} errors")
    
    # Test anomaly detection
    print("\n3. Testing Anomaly Detection...")
    log_lines = [line.strip() for line in sample_logs.splitlines() if line.strip()]
    
    anomaly_results = analyzer.detect_anomalies(log_lines)
    print("✅ Anomaly detection completed")
    print(f"  - Total logs analyzed: {len(log_lines)}")
    print(f"  - Anomalies detected: {len(anomaly_results.get('anomalies', []))}")
    print(f"  - Anomaly rate: {anomaly_results.get('anomaly_rate', 0):.2%}")
    
    # Test trend analysis
    print("\n4. Testing Trend Analysis...")
    trend_results = analyzer.analyze_trends(log_lines)
    print("✅ Trend analysis completed")
    
    if 'error_frequency' in trend_results:
        freq_trends = trend_results['error_frequency']
        if 'trend_direction' in freq_trends:
            print(f"  - Error frequency trend: {freq_trends['trend_direction']}")
    
    # Test root cause analysis
    print("\n5. Testing Root Cause Analysis...")
    root_cause_results = analyzer.perform_root_cause_analysis(errors)
    print("✅ Root cause analysis completed")
    print(f"  - Root causes identified: {len(root_cause_results)}")
    
    for i, rca in enumerate(root_cause_results[:3]):  # Show first 3
        print(f"  - Cause {i+1}: {rca.get('root_cause', 'Unknown')}")
        print(f"    Category: {rca.get('category', 'Unknown')}")
        print(f"    Confidence: {rca.get('confidence', 0):.2f}")
    
    # Test comprehensive analysis
    print("\n6. Testing Comprehensive Analysis...")
    comprehensive_results = analyzer.run_comprehensive_analysis()
    print("✅ Comprehensive analysis completed")
    
    # Display summary
    summary = comprehensive_results.get('summary', {})
    print(f"\n📊 Analysis Summary:")
    print(f"  - Total logs: {summary.get('total_logs', 0)}")
    print(f"  - Errors found: {summary.get('errors', 0)}")
    print(f"  - Warnings found: {summary.get('warnings', 0)}")
    
    # Display insights
    insights = comprehensive_results.get('insights', {})
    risk_level = insights.get('risk_assessment', 'unknown')
    print(f"  - Risk level: {risk_level.upper()}")
    
    critical_issues = insights.get('critical_issues', [])
    if critical_issues:
        print(f"  - Critical issues: {len(critical_issues)}")
        for issue in critical_issues[:2]:
            print(f"    • {issue}")
    
    print("\n🎉 All tests completed successfully!")
    print("\nThe Enhanced AI Log Analyzer is ready to use with the following features:")
    print("  ✅ Error Classification & Root Cause Analysis")
    print("  ✅ Multi-method Anomaly Detection")
    print("  ✅ Trend Analysis & Pattern Recognition")
    print("  ✅ Comprehensive AI-powered Insights")
    print("  ✅ Interactive Visualizations")
    
    return comprehensive_results

def save_test_results(results):
    """Save test results to file"""
    with open('test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    print("\n💾 Test results saved to 'test_results.json'")

if __name__ == "__main__":
    try:
        results = test_enhanced_features()
        save_test_results(results)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
