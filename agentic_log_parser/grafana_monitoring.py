#!/usr/bin/env python3
"""
Grafana Monitoring Integration Module
Comprehensive monitoring dashboards, alerts, and metrics visualization
"""

import streamlit as st
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import numpy as np

class GrafanaMonitoring:
    """Grafana integration for monitoring and alerting"""
    
    def __init__(self):
        self.grafana_url = self._get_grafana_url()
        self.grafana_token = self._get_grafana_token()
        self.headers = self._get_headers()
        
        # Sample data for demonstration
        self.sample_data = self._generate_sample_data()
    
    def _get_grafana_url(self) -> str:
        """Get Grafana URL from session state or default"""
        return st.session_state.get('grafana_url', 'http://localhost:3000')
    
    def _get_grafana_token(self) -> Optional[str]:
        """Get Grafana API token from session state"""
        return st.session_state.get('grafana_token')
    
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers for Grafana API"""
        if self.grafana_token:
            return {
                "Authorization": f"Bearer {self.grafana_token}",
                "Content-Type": "application/json"
            }
        return {}
    
    def render_grafana_dashboard(self):
        """Render the main Grafana monitoring dashboard"""
        st.markdown("## 📊 Grafana Monitoring & Alerting")
        
        # Grafana connection
        if not self.grafana_token:
            self._render_connection_setup()
            return
        
        # Main dashboard tabs
        tabs = st.tabs([
            "📊 Overview",
            "📈 Dashboards",
            "🚨 Alerts",
            "📋 Data Sources",
            "⚙️ Configuration"
        ])
        
        with tabs[0]:
            self.render_overview_tab()
        
        with tabs[1]:
            self.render_dashboards_tab()
        
        with tabs[2]:
            self.render_alerts_tab()
        
        with tabs[3]:
            self.render_datasources_tab()
        
        with tabs[4]:
            self.render_configuration_tab()
    
    def _render_connection_setup(self):
        """Render Grafana connection setup"""
        st.markdown("### 🔗 Grafana Connection Setup")
        
        st.info("Configure your Grafana connection to access monitoring and alerting features.")
        
        with st.form("grafana_connection"):
            col1, col2 = st.columns(2)
            
            with col1:
                grafana_url = st.text_input(
                    "Grafana URL",
                    value="http://localhost:3000",
                    help="URL of your Grafana instance"
                )
            
            with col2:
                grafana_token = st.text_input(
                    "API Token",
                    type="password",
                    help="Generate a token in Grafana: Configuration > API Keys"
                )
            
            st.markdown("**Required Permissions:**")
            st.markdown("- Admin or Editor role")
            st.markdown("- Dashboard management")
            st.markdown("- Alert management")
            st.markdown("- Data source access")
            
            if st.form_submit_button("🔗 Connect to Grafana"):
                if grafana_url and grafana_token:
                    if self._validate_connection(grafana_url, grafana_token):
                        st.session_state.grafana_url = grafana_url
                        st.session_state.grafana_token = grafana_token
                        st.success("✅ Successfully connected to Grafana!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to connect to Grafana. Please check your URL and token.")
                else:
                    st.error("Please provide both URL and token")
    
    def _validate_connection(self, url: str, token: str) -> bool:
        """Validate Grafana connection"""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{url}/api/health", headers=headers, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def render_overview_tab(self):
        """Render Grafana overview dashboard"""
        st.markdown("### 📊 Monitoring Overview")
        
        # System health metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("🖥️ Active Dashboards", "24", "+2")
        
        with col2:
            st.metric("🚨 Active Alerts", "3", "-1")
        
        with col3:
            st.metric("📊 Data Sources", "8", "+1")
        
        with col4:
            st.metric("👥 Users", "45", "+3")
        
        # Real-time metrics
        st.markdown("### 📈 Real-time System Metrics")
        
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_cpu_usage_chart()
        
        with col2:
            self._render_memory_usage_chart()
        
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_network_traffic_chart()
        
        with col2:
            self._render_disk_usage_chart()
        
        # Recent alerts
        st.markdown("### 🚨 Recent Alerts")
        
        alerts = self.sample_data['alerts']
        
        for alert in alerts[:5]:
            severity_color = {
                'critical': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8'
            }.get(alert['severity'], '#6c757d')
            
            severity_icon = {
                'critical': '🔴',
                'warning': '🟡',
                'info': '🔵'
            }.get(alert['severity'], '⚪')
            
            with st.expander(f"{severity_icon} {alert['title']} - {alert['severity'].title()}"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Message:** {alert['message']}")
                    st.markdown(f"**Dashboard:** {alert['dashboard']}")
                    st.markdown(f"**Time:** {alert['time']}")
                    st.markdown(f"**Status:** {alert['status']}")
                
                with col2:
                    if alert['status'] == 'firing':
                        if st.button("🔕 Silence", key=f"silence_{alert['id']}"):
                            st.success("Alert silenced")
                    
                    if st.button("📊 View Dashboard", key=f"view_{alert['id']}"):
                        st.info(f"Opening dashboard: {alert['dashboard']}")
    
    def render_dashboards_tab(self):
        """Render dashboards management tab"""
        st.markdown("### 📈 Dashboard Management")
        
        # Dashboard controls
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            search_query = st.text_input("🔍 Search Dashboards", placeholder="Enter dashboard name...")
        
        with col2:
            folder_filter = st.selectbox("Folder", ["All", "Infrastructure", "Applications", "Business"])
        
        with col3:
            if st.button("➕ New Dashboard", use_container_width=True):
                self._show_create_dashboard_form()
        
        # Dashboard list
        dashboards = self.sample_data['dashboards']
        
        for dashboard in dashboards:
            with st.expander(f"📊 {dashboard['title']} ({dashboard['folder']})"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Description:** {dashboard['description']}")
                    st.markdown(f"**Folder:** {dashboard['folder']}")
                    st.markdown(f"**Tags:** {', '.join(dashboard['tags'])}")
                    st.markdown(f"**Last Updated:** {dashboard['updated']}")
                    st.markdown(f"**Views:** {dashboard['views']}")
                
                with col2:
                    if st.button("👁️ View", key=f"view_dash_{dashboard['id']}"):
                        self._view_dashboard(dashboard)
                    
                    if st.button("✏️ Edit", key=f"edit_dash_{dashboard['id']}"):
                        self._edit_dashboard(dashboard)
                    
                    if st.button("📤 Export", key=f"export_dash_{dashboard['id']}"):
                        self._export_dashboard(dashboard)
        
        # Dashboard analytics
        st.markdown("### 📊 Dashboard Analytics")
        
        dashboard_data = []
        for dashboard in dashboards:
            dashboard_data.append({
                'Dashboard': dashboard['title'],
                'Folder': dashboard['folder'],
                'Views': dashboard['views'],
                'Panels': dashboard['panels'],
                'Last Updated': dashboard['updated']
            })
        
        df = pd.DataFrame(dashboard_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    
    def render_alerts_tab(self):
        """Render alerts management tab"""
        st.markdown("### 🚨 Alert Management")
        
        # Alert overview
        col1, col2, col3 = st.columns(3)
        
        alerts = self.sample_data['alerts']
        critical_alerts = len([a for a in alerts if a['severity'] == 'critical'])
        warning_alerts = len([a for a in alerts if a['severity'] == 'warning'])
        firing_alerts = len([a for a in alerts if a['status'] == 'firing'])
        
        with col1:
            st.metric("🔴 Critical", critical_alerts)
        
        with col2:
            st.metric("🟡 Warning", warning_alerts)
        
        with col3:
            st.metric("🔥 Firing", firing_alerts)
        
        # Alert rules
        st.markdown("### 📋 Alert Rules")
        
        alert_rules = self.sample_data['alert_rules']
        
        for rule in alert_rules:
            status_icon = "✅" if rule['enabled'] else "❌"
            
            with st.expander(f"{status_icon} {rule['name']} - {rule['condition']}"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Condition:** {rule['condition']}")
                    st.markdown(f"**Frequency:** {rule['frequency']}")
                    st.markdown(f"**For:** {rule['for_duration']}")
                    st.markdown(f"**Notifications:** {', '.join(rule['notifications'])}")
                    st.markdown(f"**Last Evaluation:** {rule['last_eval']}")
                
                with col2:
                    if rule['enabled']:
                        if st.button("⏸️ Disable", key=f"disable_{rule['id']}"):
                            st.warning(f"Alert rule '{rule['name']}' disabled")
                    else:
                        if st.button("▶️ Enable", key=f"enable_{rule['id']}"):
                            st.success(f"Alert rule '{rule['name']}' enabled")
                    
                    if st.button("✏️ Edit", key=f"edit_rule_{rule['id']}"):
                        self._edit_alert_rule(rule)
                    
                    if st.button("🧪 Test", key=f"test_rule_{rule['id']}"):
                        self._test_alert_rule(rule)
        
        # Create new alert rule
        st.markdown("### ➕ Create Alert Rule")
        
        with st.form("new_alert_rule"):
            col1, col2 = st.columns(2)
            
            with col1:
                rule_name = st.text_input("Rule Name")
                metric_query = st.text_area("Metric Query", placeholder="up == 0")
                condition = st.selectbox("Condition", ["IS ABOVE", "IS BELOW", "IS OUTSIDE RANGE", "HAS NO VALUE"])
            
            with col2:
                threshold = st.number_input("Threshold", value=0.0)
                evaluation_frequency = st.selectbox("Evaluation Frequency", ["10s", "30s", "1m", "5m", "10m"])
                for_duration = st.selectbox("For Duration", ["0m", "1m", "5m", "10m", "15m"])
            
            notification_channels = st.multiselect(
                "Notification Channels",
                ["Email", "Slack", "PagerDuty", "Webhook"]
            )
            
            if st.form_submit_button("🚨 Create Alert Rule"):
                if rule_name and metric_query:
                    st.success(f"Alert rule '{rule_name}' created successfully!")
                else:
                    st.error("Please fill in required fields")
    
    def render_datasources_tab(self):
        """Render data sources management tab"""
        st.markdown("### 📋 Data Sources")
        
        # Data sources overview
        data_sources = self.sample_data['data_sources']
        
        for ds in data_sources:
            status_icon = "🟢" if ds['status'] == 'connected' else "🔴"
            
            with st.expander(f"{status_icon} {ds['name']} ({ds['type']})"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Type:** {ds['type']}")
                    st.markdown(f"**URL:** {ds['url']}")
                    st.markdown(f"**Database:** {ds.get('database', 'N/A')}")
                    st.markdown(f"**Status:** {ds['status']}")
                    st.markdown(f"**Last Check:** {ds['last_check']}")
                
                with col2:
                    if st.button("🔍 Test", key=f"test_ds_{ds['id']}"):
                        self._test_data_source(ds)
                    
                    if st.button("✏️ Edit", key=f"edit_ds_{ds['id']}"):
                        self._edit_data_source(ds)
                    
                    if st.button("📊 Explore", key=f"explore_ds_{ds['id']}"):
                        self._explore_data_source(ds)
        
        # Add new data source
        st.markdown("### ➕ Add Data Source")
        
        with st.form("new_data_source"):
            col1, col2 = st.columns(2)
            
            with col1:
                ds_name = st.text_input("Name")
                ds_type = st.selectbox("Type", ["Prometheus", "InfluxDB", "Elasticsearch", "MySQL", "PostgreSQL"])
                ds_url = st.text_input("URL", placeholder="http://localhost:9090")
            
            with col2:
                ds_database = st.text_input("Database (if applicable)")
                ds_username = st.text_input("Username")
                ds_password = st.text_input("Password", type="password")
            
            if st.form_submit_button("➕ Add Data Source"):
                if ds_name and ds_type and ds_url:
                    st.success(f"Data source '{ds_name}' added successfully!")
                else:
                    st.error("Please fill in required fields")
    
    def render_configuration_tab(self):
        """Render Grafana configuration tab"""
        st.markdown("### ⚙️ Grafana Configuration")
        
        # General settings
        st.markdown("#### 🔧 General Settings")
        
        with st.expander("Organization Settings"):
            org_name = st.text_input("Organization Name", value="DevOps Team")
            default_theme = st.selectbox("Default Theme", ["Dark", "Light"])
            timezone = st.selectbox("Timezone", ["UTC", "America/New_York", "Europe/London"])
            
            if st.button("💾 Save Organization Settings"):
                st.success("Organization settings saved!")
        
        # User management
        st.markdown("#### 👥 User Management")
        
        users = self.sample_data['users']
        
        user_data = []
        for user in users:
            user_data.append({
                'Username': user['username'],
                'Email': user['email'],
                'Role': user['role'],
                'Last Login': user['last_login'],
                'Status': user['status']
            })
        
        df = pd.DataFrame(user_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Add new user
        with st.expander("➕ Add New User"):
            col1, col2 = st.columns(2)
            
            with col1:
                new_username = st.text_input("Username")
                new_email = st.text_input("Email")
            
            with col2:
                new_role = st.selectbox("Role", ["Viewer", "Editor", "Admin"])
                send_invite = st.checkbox("Send invitation email")
            
            if st.button("👤 Add User"):
                if new_username and new_email:
                    st.success(f"User '{new_username}' added successfully!")
                else:
                    st.error("Please fill in required fields")
        
        # Notification channels
        st.markdown("#### 📢 Notification Channels")
        
        notification_channels = self.sample_data['notification_channels']
        
        for channel in notification_channels:
            with st.expander(f"📢 {channel['name']} ({channel['type']})"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Type:** {channel['type']}")
                    st.markdown(f"**Settings:** {channel['settings']}")
                    st.markdown(f"**Enabled:** {'Yes' if channel['enabled'] else 'No'}")
                
                with col2:
                    if st.button("🧪 Test", key=f"test_channel_{channel['id']}"):
                        st.success(f"Test notification sent to {channel['name']}")
                    
                    if st.button("✏️ Edit", key=f"edit_channel_{channel['id']}"):
                        self._edit_notification_channel(channel)
    
    def _render_cpu_usage_chart(self):
        """Render CPU usage chart"""
        # Generate sample CPU data
        times = pd.date_range(start=datetime.now() - timedelta(hours=1), end=datetime.now(), freq='1min')
        cpu_usage = [30 + np.random.uniform(-10, 20) for _ in times]
        cpu_usage = np.clip(cpu_usage, 0, 100)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=times,
            y=cpu_usage,
            mode='lines',
            name='CPU Usage %',
            line=dict(color='#007bff', width=2),
            fill='tonexty'
        ))
        
        fig.update_layout(
            title="CPU Usage (Last Hour)",
            xaxis_title="Time",
            yaxis_title="Usage %",
            height=300,
            yaxis=dict(range=[0, 100])
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_memory_usage_chart(self):
        """Render memory usage chart"""
        # Generate sample memory data
        times = pd.date_range(start=datetime.now() - timedelta(hours=1), end=datetime.now(), freq='1min')
        memory_usage = [60 + np.random.uniform(-15, 15) for _ in times]
        memory_usage = np.clip(memory_usage, 0, 100)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=times,
            y=memory_usage,
            mode='lines',
            name='Memory Usage %',
            line=dict(color='#28a745', width=2),
            fill='tonexty'
        ))
        
        fig.update_layout(
            title="Memory Usage (Last Hour)",
            xaxis_title="Time",
            yaxis_title="Usage %",
            height=300,
            yaxis=dict(range=[0, 100])
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_network_traffic_chart(self):
        """Render network traffic chart"""
        # Generate sample network data
        times = pd.date_range(start=datetime.now() - timedelta(hours=1), end=datetime.now(), freq='1min')
        inbound = [50 + np.random.uniform(-20, 30) for _ in times]
        outbound = [30 + np.random.uniform(-15, 25) for _ in times]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=times,
            y=inbound,
            mode='lines',
            name='Inbound (Mbps)',
            line=dict(color='#17a2b8', width=2)
        ))
        fig.add_trace(go.Scatter(
            x=times,
            y=outbound,
            mode='lines',
            name='Outbound (Mbps)',
            line=dict(color='#ffc107', width=2)
        ))
        
        fig.update_layout(
            title="Network Traffic (Last Hour)",
            xaxis_title="Time",
            yaxis_title="Mbps",
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_disk_usage_chart(self):
        """Render disk usage chart"""
        # Sample disk usage data
        disks = ['/', '/var', '/tmp', '/home']
        usage = [45, 67, 23, 78]
        colors = ['green' if u < 80 else 'orange' if u < 90 else 'red' for u in usage]
        
        fig = go.Figure(data=[go.Bar(
            x=disks,
            y=usage,
            marker_color=colors
        )])
        
        fig.update_layout(
            title="Disk Usage by Mount Point",
            xaxis_title="Mount Point",
            yaxis_title="Usage %",
            height=300,
            yaxis=dict(range=[0, 100])
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _generate_sample_data(self) -> Dict:
        """Generate sample Grafana data"""
        return {
            'dashboards': [
                {
                    'id': 'dash_1',
                    'title': 'System Overview',
                    'description': 'Overall system health and performance metrics',
                    'folder': 'Infrastructure',
                    'tags': ['system', 'overview', 'health'],
                    'updated': '2 hours ago',
                    'views': 1247,
                    'panels': 12
                },
                {
                    'id': 'dash_2',
                    'title': 'Application Performance',
                    'description': 'Application-specific performance and error metrics',
                    'folder': 'Applications',
                    'tags': ['application', 'performance', 'errors'],
                    'updated': '4 hours ago',
                    'views': 856,
                    'panels': 8
                },
                {
                    'id': 'dash_3',
                    'title': 'Business Metrics',
                    'description': 'Key business indicators and user analytics',
                    'folder': 'Business',
                    'tags': ['business', 'users', 'revenue'],
                    'updated': '1 day ago',
                    'views': 432,
                    'panels': 6
                }
            ],
            'alerts': [
                {
                    'id': 'alert_1',
                    'title': 'High CPU Usage',
                    'message': 'CPU usage is above 90% for more than 5 minutes',
                    'severity': 'critical',
                    'status': 'firing',
                    'dashboard': 'System Overview',
                    'time': '5 minutes ago'
                },
                {
                    'id': 'alert_2',
                    'title': 'Low Disk Space',
                    'message': 'Disk usage is above 85% on /var partition',
                    'severity': 'warning',
                    'status': 'firing',
                    'dashboard': 'System Overview',
                    'time': '15 minutes ago'
                },
                {
                    'id': 'alert_3',
                    'title': 'Application Error Rate',
                    'message': 'Error rate increased to 5% in the last 10 minutes',
                    'severity': 'warning',
                    'status': 'resolved',
                    'dashboard': 'Application Performance',
                    'time': '1 hour ago'
                }
            ],
            'alert_rules': [
                {
                    'id': 'rule_1',
                    'name': 'High CPU Usage',
                    'condition': 'cpu_usage > 90',
                    'frequency': '30s',
                    'for_duration': '5m',
                    'notifications': ['Email', 'Slack'],
                    'enabled': True,
                    'last_eval': '30 seconds ago'
                },
                {
                    'id': 'rule_2',
                    'name': 'Memory Usage Alert',
                    'condition': 'memory_usage > 85',
                    'frequency': '1m',
                    'for_duration': '10m',
                    'notifications': ['Email'],
                    'enabled': True,
                    'last_eval': '1 minute ago'
                }
            ],
            'data_sources': [
                {
                    'id': 'ds_1',
                    'name': 'Prometheus',
                    'type': 'Prometheus',
                    'url': 'http://localhost:9090',
                    'status': 'connected',
                    'last_check': '2 minutes ago'
                },
                {
                    'id': 'ds_2',
                    'name': 'InfluxDB',
                    'type': 'InfluxDB',
                    'url': 'http://localhost:8086',
                    'database': 'metrics',
                    'status': 'connected',
                    'last_check': '5 minutes ago'
                },
                {
                    'id': 'ds_3',
                    'name': 'Elasticsearch',
                    'type': 'Elasticsearch',
                    'url': 'http://localhost:9200',
                    'status': 'disconnected',
                    'last_check': '1 hour ago'
                }
            ],
            'users': [
                {
                    'username': 'admin',
                    'email': '<EMAIL>',
                    'role': 'Admin',
                    'last_login': '2 hours ago',
                    'status': 'Active'
                },
                {
                    'username': 'devops.engineer',
                    'email': '<EMAIL>',
                    'role': 'Editor',
                    'last_login': '4 hours ago',
                    'status': 'Active'
                },
                {
                    'username': 'developer',
                    'email': '<EMAIL>',
                    'role': 'Viewer',
                    'last_login': '1 day ago',
                    'status': 'Active'
                }
            ],
            'notification_channels': [
                {
                    'id': 'channel_1',
                    'name': 'DevOps Email',
                    'type': 'Email',
                    'settings': '<EMAIL>',
                    'enabled': True
                },
                {
                    'id': 'channel_2',
                    'name': 'Slack Alerts',
                    'type': 'Slack',
                    'settings': '#alerts channel',
                    'enabled': True
                },
                {
                    'id': 'channel_3',
                    'name': 'PagerDuty',
                    'type': 'PagerDuty',
                    'settings': 'Integration key configured',
                    'enabled': False
                }
            ]
        }
    
    def _show_create_dashboard_form(self):
        """Show create dashboard form"""
        st.info("Create new dashboard form would appear here")
    
    def _view_dashboard(self, dashboard: Dict):
        """View dashboard"""
        st.info(f"Opening dashboard: {dashboard['title']}")
    
    def _edit_dashboard(self, dashboard: Dict):
        """Edit dashboard"""
        st.info(f"Editing dashboard: {dashboard['title']}")
    
    def _export_dashboard(self, dashboard: Dict):
        """Export dashboard"""
        st.success(f"Dashboard '{dashboard['title']}' exported successfully!")
    
    def _edit_alert_rule(self, rule: Dict):
        """Edit alert rule"""
        st.info(f"Editing alert rule: {rule['name']}")
    
    def _test_alert_rule(self, rule: Dict):
        """Test alert rule"""
        st.success(f"Alert rule '{rule['name']}' test completed!")
    
    def _test_data_source(self, ds: Dict):
        """Test data source connection"""
        st.success(f"Data source '{ds['name']}' connection test successful!")
    
    def _edit_data_source(self, ds: Dict):
        """Edit data source"""
        st.info(f"Editing data source: {ds['name']}")
    
    def _explore_data_source(self, ds: Dict):
        """Explore data source"""
        st.info(f"Exploring data source: {ds['name']}")
    
    def _edit_notification_channel(self, channel: Dict):
        """Edit notification channel"""
        st.info(f"Editing notification channel: {channel['name']}")
