import sqlite3
import json
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import pandas as pd
from collections import defaultdict, Counter
import pickle
import os

class HistoricalDatabase:
    """
    Historical database for storing and managing past errors, resolutions, and feedback
    Implements a learning system that improves recommendations over time
    """
    
    def __init__(self, db_path: str = "historical_logs.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Errors table - stores all encountered errors
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS errors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                error_hash TEXT UNIQUE NOT NULL,
                error_text TEXT NOT NULL,
                error_category TEXT,
                severity TEXT,
                first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                occurrence_count INTEGER DEFAULT 1,
                pattern_signature TEXT,
                metadata TEXT
            )
        ''')
        
        # Resolutions table - stores solutions and their effectiveness
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS resolutions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                error_hash TEXT NOT NULL,
                resolution_text TEXT NOT NULL,
                resolution_type TEXT,
                source TEXT,
                effectiveness_score REAL DEFAULT 0.0,
                success_count INTEGER DEFAULT 0,
                failure_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (error_hash) REFERENCES errors (error_hash)
            )
        ''')
        
        # Feedback table - stores user feedback on recommendations
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                error_hash TEXT NOT NULL,
                resolution_id INTEGER,
                feedback_type TEXT NOT NULL,
                rating INTEGER,
                comment TEXT,
                user_id TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                context TEXT,
                FOREIGN KEY (error_hash) REFERENCES errors (error_hash),
                FOREIGN KEY (resolution_id) REFERENCES resolutions (id)
            )
        ''')
        
        # Patterns table - stores learned patterns and their success rates
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_text TEXT NOT NULL,
                pattern_type TEXT,
                category TEXT,
                confidence_score REAL DEFAULT 0.0,
                usage_count INTEGER DEFAULT 0,
                success_rate REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Analytics table - stores performance metrics and trends
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                metric_value REAL,
                metric_data TEXT,
                category TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def generate_error_hash(self, error_text: str) -> str:
        """Generate a unique hash for an error based on its normalized content"""
        # Normalize error text by removing timestamps, IDs, and variable data
        normalized = error_text.lower()
        # Remove common variable patterns
        import re
        normalized = re.sub(r'\d{4}-\d{2}-\d{2}[\s\d:.-]*', '', normalized)
        normalized = re.sub(r'\b\d+\b', 'NUM', normalized)
        normalized = re.sub(r'\b[a-f0-9]{8,}\b', 'HASH', normalized)
        normalized = re.sub(r'\b\d+\.\d+\.\d+\.\d+\b', 'IP', normalized)
        
        return hashlib.md5(normalized.encode()).hexdigest()
    
    def store_error(self, error_text: str, category: str = None, 
                   severity: str = "medium", metadata: Dict = None) -> str:
        """Store an error in the database"""
        error_hash = self.generate_error_hash(error_text)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check if error already exists
        cursor.execute('SELECT id, occurrence_count FROM errors WHERE error_hash = ?', (error_hash,))
        existing = cursor.fetchone()
        
        if existing:
            # Update existing error
            cursor.execute('''
                UPDATE errors 
                SET last_seen = CURRENT_TIMESTAMP, 
                    occurrence_count = occurrence_count + 1,
                    severity = COALESCE(?, severity),
                    error_category = COALESCE(?, error_category)
                WHERE error_hash = ?
            ''', (severity, category, error_hash))
        else:
            # Insert new error
            cursor.execute('''
                INSERT INTO errors (error_hash, error_text, error_category, severity, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (error_hash, error_text, category, severity, json.dumps(metadata or {})))
        
        conn.commit()
        conn.close()
        
        return error_hash
    
    def store_resolution(self, error_hash: str, resolution_text: str, 
                        resolution_type: str = "manual", source: str = "user") -> int:
        """Store a resolution for an error"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO resolutions (error_hash, resolution_text, resolution_type, source)
            VALUES (?, ?, ?, ?)
        ''', (error_hash, resolution_text, resolution_type, source))
        
        resolution_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return resolution_id
    
    def store_feedback(self, error_hash: str, feedback_type: str, rating: int = None,
                      comment: str = None, resolution_id: int = None, 
                      user_id: str = None, context: Dict = None):
        """Store user feedback"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO feedback (error_hash, resolution_id, feedback_type, rating, 
                                comment, user_id, context)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (error_hash, resolution_id, feedback_type, rating, comment, 
              user_id, json.dumps(context or {})))
        
        # Update resolution effectiveness if feedback is for a specific resolution
        if resolution_id and rating is not None:
            if rating >= 4:  # Positive feedback (4-5 stars)
                cursor.execute('''
                    UPDATE resolutions 
                    SET success_count = success_count + 1,
                        effectiveness_score = (success_count + 1.0) / (success_count + failure_count + 1.0),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (resolution_id,))
            elif rating <= 2:  # Negative feedback (1-2 stars)
                cursor.execute('''
                    UPDATE resolutions 
                    SET failure_count = failure_count + 1,
                        effectiveness_score = success_count / (success_count + failure_count + 1.0),
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (resolution_id,))
        
        conn.commit()
        conn.close()
    
    def get_historical_resolutions(self, error_hash: str) -> List[Dict]:
        """Get historical resolutions for an error, ordered by effectiveness"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT r.id, r.resolution_text, r.resolution_type, r.source,
                   r.effectiveness_score, r.success_count, r.failure_count,
                   r.created_at, r.updated_at
            FROM resolutions r
            WHERE r.error_hash = ?
            ORDER BY r.effectiveness_score DESC, r.success_count DESC
        ''', (error_hash,))
        
        resolutions = []
        for row in cursor.fetchall():
            resolutions.append({
                'id': row[0],
                'resolution_text': row[1],
                'resolution_type': row[2],
                'source': row[3],
                'effectiveness_score': row[4],
                'success_count': row[5],
                'failure_count': row[6],
                'created_at': row[7],
                'updated_at': row[8]
            })
        
        conn.close()
        return resolutions
    
    def get_similar_errors(self, error_text: str, limit: int = 5) -> List[Dict]:
        """Find similar errors based on text similarity"""
        error_hash = self.generate_error_hash(error_text)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get all errors except the current one
        cursor.execute('''
            SELECT error_hash, error_text, error_category, severity, occurrence_count
            FROM errors
            WHERE error_hash != ?
            ORDER BY occurrence_count DESC
        ''', (error_hash,))
        
        similar_errors = []
        for row in cursor.fetchall():
            # Simple similarity based on common words
            similarity = self._calculate_text_similarity(error_text, row[1])
            if similarity > 0.3:  # Threshold for similarity
                similar_errors.append({
                    'error_hash': row[0],
                    'error_text': row[1],
                    'category': row[2],
                    'severity': row[3],
                    'occurrence_count': row[4],
                    'similarity': similarity
                })
        
        # Sort by similarity and return top results
        similar_errors.sort(key=lambda x: x['similarity'], reverse=True)
        conn.close()
        
        return similar_errors[:limit]
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate simple text similarity based on common words"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def get_trending_errors(self, days: int = 7) -> List[Dict]:
        """Get trending errors in the last N days"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        since_date = datetime.now() - timedelta(days=days)
        
        cursor.execute('''
            SELECT error_hash, error_text, error_category, severity, occurrence_count,
                   last_seen
            FROM errors
            WHERE last_seen >= ?
            ORDER BY occurrence_count DESC
            LIMIT 10
        ''', (since_date,))
        
        trending = []
        for row in cursor.fetchall():
            trending.append({
                'error_hash': row[0],
                'error_text': row[1],
                'category': row[2],
                'severity': row[3],
                'occurrence_count': row[4],
                'last_seen': row[5]
            })
        
        conn.close()
        return trending
    
    def get_knowledge_base_stats(self) -> Dict:
        """Get statistics about the knowledge base"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        stats = {}
        
        # Total errors
        cursor.execute('SELECT COUNT(*) FROM errors')
        stats['total_errors'] = cursor.fetchone()[0]
        
        # Total resolutions
        cursor.execute('SELECT COUNT(*) FROM resolutions')
        stats['total_resolutions'] = cursor.fetchone()[0]
        
        # Total feedback entries
        cursor.execute('SELECT COUNT(*) FROM feedback')
        stats['total_feedback'] = cursor.fetchone()[0]
        
        # Average effectiveness score
        cursor.execute('SELECT AVG(effectiveness_score) FROM resolutions WHERE effectiveness_score > 0')
        result = cursor.fetchone()[0]
        stats['avg_effectiveness'] = result if result else 0.0
        
        # Most common categories
        cursor.execute('''
            SELECT error_category, COUNT(*) as count
            FROM errors
            WHERE error_category IS NOT NULL
            GROUP BY error_category
            ORDER BY count DESC
            LIMIT 5
        ''')
        stats['top_categories'] = [{'category': row[0], 'count': row[1]} for row in cursor.fetchall()]
        
        # Recent activity (last 7 days)
        since_date = datetime.now() - timedelta(days=7)
        cursor.execute('SELECT COUNT(*) FROM errors WHERE last_seen >= ?', (since_date,))
        stats['recent_errors'] = cursor.fetchone()[0]
        
        conn.close()
        return stats
