import re
from typing import List, Dict
import os
import openai
from dotenv import load_dotenv
load_dotenv()
import openai
import traceback
import requests
openai.api_type = "azure"
from openai import AzureOpenAI
# openai.api_version = "2023-03-15-preview"
openai.api_version = "2023-08-01-preview"
import base64


client_id = '0oaouu7nnwNigFUpD5d7'
client_secret = '6Osxm_2WGM9tAC--HwbG1-AkqKUAZKiIvaFI23nM7pwZbcNq_oR76nuOr4MdswZm'
app_key = 'egai-prd-networking-123023480-networking-1747906339651'
url = "https://id.cisco.com/oauth2/default/v1/token"

payload = "grant_type=client_credentials"
value = base64.b64encode(f'{client_id}:{client_secret}'.encode('utf-8')).decode('utf-8')
headers = {
    "Accept": "*/*",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": f"Basic {value}"
}

token_response = requests.request("POST", url, headers=headers, data=payload)

print(token_response.text)




client = AzureOpenAI(
  azure_endpoint = 'https://chat-ai.cisco.com', 
  api_key=token_response.json()["access_token"],  
  api_version="2023-08-01-preview"
)


class AgenticLogParser:
    def __init__(self, log_data: str, enable_gpt=True):
        self.log_data = log_data
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.suggestions: List[Dict] = []
        self.enable_gpt = enable_gpt

        # Static knowledge base
        self.known_fixes = {
            "Failed to connect to database": {
                "solution": "Check DB connectivity, credentials, and network.",
                "comment": "Database service might be down or unreachable.",
                "source": "rules"
            },
            "Service unavailable at endpoint": {
                "solution": "Verify the endpoint is active and accessible.",
                "comment": "The service might be overloaded or crashed.",
                "source": "rules"
            },
            "GitHub API rate limit exceeded": {
                "solution": "Use a different token or wait for rate limit reset.",
                "comment": "GitHub enforces request rate limits.",
                "source": "rules"
            },
            "Sonar Analysis failed": {
                "solution": "Check SonarQube connectivity and project config.",
                "comment": "Analysis may have failed due to rules or timeout.",
                "source": "rules"
            }
        }

        # Actionable errors mapped to handlers
        self.actionable_errors = {
            "Sonar Analysis failed": self.trigger_sonar_analysis
        }

    def parse_logs(self):
        """Extract error and warning lines from logs."""
        for line in self.log_data.splitlines():
            line = line.strip()
            if "[ERROR]" in line:
                self.errors.append(line)
            elif "[WARNING]" in line:
                self.warnings.append(line)

    def generate_suggestions(self):
        """Generate rule-based suggestions and trigger any actions."""
        matched_errors = set()
        for error_line in self.errors:
            matched = False
            for known_error, fix in self.known_fixes.items():
                if known_error in error_line:
                    self.suggestions.append({
                        "log": error_line,
                        "solution": fix["solution"],
                        "comment": fix.get("comment", ""),
                        "source": fix.get("source", "rules")
                    })
                    matched = True
                    matched_errors.add(error_line)

                    if known_error in self.actionable_errors:
                        self.actionable_errors[known_error](error_line)
                    if not matched:
                        matched_errors.add(error_line)

        # GPT fallback if no rule-based suggestions
        unmatched = [err for err in self.errors if err not in matched_errors]
        if unmatched : #and self.enable_gpt:
            self.gpt_fallback(unmatched)

    def trigger_sonar_analysis(self, error_log: str):
        """Placeholder to trigger Jenkins job."""
        print(f"🚀 [ACTION] Triggering Jenkins Sonar Analysis Job for: {error_log}")
        # Future: Replace with actual Jenkins API call

    def run(self) -> Dict[str, List[Dict]]:
        self.parse_logs()
        self.generate_suggestions()

        # Collect logs that do NOT have suggestions yet
        logs_with_suggestions = {s["log"] for s in self.suggestions}
        logs_without_suggestions = [log for log in self.errors if log not in logs_with_suggestions]

        if self.enable_gpt and app_key:
            # Call GPT fallback for unmatched errors
            self.gpt_fallback(logs_without_suggestions)

        return {
            "errors": self.errors,
            "warnings": self.warnings,
            "suggestions": self.suggestions
        }

    def query_gpt(self, log_line: str) -> str:
        prompt = f"Suggest a fix for the following log error:\n\n{log_line}"
        try:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You're a helpful DevOps assistant."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            return f"GPT Error: {e}"


    def gpt_fallback(self, unmatched_errors):
        try:
           # client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
           #app_key = "********************************************************************************************************************************************************************"
           app_key = "egai-prd-networking-123023480-networking-1747906339651"
           for log in unmatched_errors:
                prompt = f"Log Message: {log}\n\nSuggest a fix and comment."
                messages = [
                    {"role": "system", "content": "You are a DevOps assistant that suggests fixes for logs."},
                    {"role": "user", "content": prompt}
                ]
                response = client.chat.completions.create(
                    model="gpt-4o", # model = "deployment_name".
                    messages=messages,
                    user=f'{{"appkey": "{app_key}"}}'
                )
                suggestion = response.choices[0].message.content.strip()
                solution = suggestion.split("\n")[0]
                comment = "\n".join(suggestion.split("\n")[1:])

                self.suggestions.append({
                    "log": log,
                    "solution": solution,
                    "comment": comment,
                    "source": "gpt"
                })
        except Exception as e:
            print("❌ GPT Error:", e)
