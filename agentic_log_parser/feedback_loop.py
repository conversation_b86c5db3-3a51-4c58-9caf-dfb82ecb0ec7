import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, <PERSON>, Tuple, Optional
from collections import defaultdict, Counter
from historical_database import HistoricalDatabase
import pickle
import os

class FeedbackLoop:
    """
    Feedback loop system that learns from user interactions and improves recommendations
    """
    
    def __init__(self, db_path: str = "historical_logs.db"):
        self.db = HistoricalDatabase(db_path)
        self.learning_cache = {}
        self.model_path = "feedback_model.pkl"
        self.load_learning_model()
    
    def load_learning_model(self):
        """Load the learning model from disk"""
        if os.path.exists(self.model_path):
            try:
                with open(self.model_path, 'rb') as f:
                    self.learning_cache = pickle.load(f)
            except Exception as e:
                print(f"Warning: Could not load learning model: {e}")
                self.learning_cache = {}
        else:
            self.learning_cache = {
                'pattern_weights': defaultdict(float),
                'category_preferences': defaultdict(float),
                'resolution_effectiveness': defaultdict(float),
                'user_preferences': defaultdict(dict),
                'temporal_patterns': defaultdict(list)
            }
    
    def save_learning_model(self):
        """Save the learning model to disk"""
        try:
            with open(self.model_path, 'wb') as f:
                pickle.dump(self.learning_cache, f)
        except Exception as e:
            print(f"Warning: Could not save learning model: {e}")
    
    def process_error_with_history(self, error_text: str, category: str = None, 
                                 severity: str = "medium") -> Dict:
        """Process an error and get enhanced recommendations based on history"""
        # Store the error in the database
        error_hash = self.db.store_error(error_text, category, severity)
        
        # Get historical resolutions
        historical_resolutions = self.db.get_historical_resolutions(error_hash)
        
        # Get similar errors and their resolutions
        similar_errors = self.db.get_similar_errors(error_text)
        similar_resolutions = []
        
        for similar_error in similar_errors:
            similar_resolutions.extend(
                self.db.get_historical_resolutions(similar_error['error_hash'])
            )
        
        # Generate enhanced recommendations
        recommendations = self._generate_enhanced_recommendations(
            error_text, error_hash, historical_resolutions, similar_resolutions
        )
        
        return {
            'error_hash': error_hash,
            'historical_resolutions': historical_resolutions,
            'similar_errors': similar_errors,
            'enhanced_recommendations': recommendations,
            'confidence_scores': self._calculate_confidence_scores(recommendations)
        }
    
    def _generate_enhanced_recommendations(self, error_text: str, error_hash: str,
                                         historical_resolutions: List[Dict],
                                         similar_resolutions: List[Dict]) -> List[Dict]:
        """Generate enhanced recommendations using historical data and learning"""
        recommendations = []
        
        # Priority 1: Historical resolutions for this exact error
        for resolution in historical_resolutions:
            if resolution['effectiveness_score'] > 0.5:  # Only include effective solutions
                recommendations.append({
                    'text': resolution['resolution_text'],
                    'type': 'historical_exact',
                    'source': f"Historical solution (used {resolution['success_count']} times)",
                    'effectiveness': resolution['effectiveness_score'],
                    'confidence': min(0.95, 0.7 + resolution['effectiveness_score'] * 0.25),
                    'resolution_id': resolution['id']
                })
        
        # Priority 2: Resolutions from similar errors
        similar_resolution_scores = defaultdict(list)
        for resolution in similar_resolutions:
            if resolution['effectiveness_score'] > 0.3:
                similar_resolution_scores[resolution['resolution_text']].append(
                    resolution['effectiveness_score']
                )
        
        for resolution_text, scores in similar_resolution_scores.items():
            avg_effectiveness = np.mean(scores)
            if avg_effectiveness > 0.4:
                recommendations.append({
                    'text': resolution_text,
                    'type': 'historical_similar',
                    'source': f"Similar error solution (avg effectiveness: {avg_effectiveness:.2f})",
                    'effectiveness': avg_effectiveness,
                    'confidence': min(0.85, 0.5 + avg_effectiveness * 0.35),
                    'resolution_id': None
                })
        
        # Priority 3: Pattern-based recommendations using learning cache
        pattern_recommendations = self._get_pattern_based_recommendations(error_text)
        recommendations.extend(pattern_recommendations)
        
        # Priority 4: Category-based recommendations
        category_recommendations = self._get_category_based_recommendations(error_text)
        recommendations.extend(category_recommendations)
        
        # Sort by confidence and remove duplicates
        recommendations = self._deduplicate_recommendations(recommendations)
        recommendations.sort(key=lambda x: x['confidence'], reverse=True)
        
        return recommendations[:10]  # Return top 10 recommendations
    
    def _get_pattern_based_recommendations(self, error_text: str) -> List[Dict]:
        """Get recommendations based on learned patterns"""
        recommendations = []
        pattern_weights = self.learning_cache.get('pattern_weights', {})
        
        # Simple pattern matching for demonstration
        error_lower = error_text.lower()
        
        for pattern, weight in pattern_weights.items():
            if pattern in error_lower and weight > 0.3:
                recommendations.append({
                    'text': f"Based on pattern '{pattern}': Check system resources and restart affected services",
                    'type': 'pattern_learned',
                    'source': f"Learned pattern (weight: {weight:.2f})",
                    'effectiveness': weight,
                    'confidence': min(0.8, 0.4 + weight * 0.4),
                    'resolution_id': None
                })
        
        return recommendations
    
    def _get_category_based_recommendations(self, error_text: str) -> List[Dict]:
        """Get recommendations based on category preferences"""
        recommendations = []
        category_prefs = self.learning_cache.get('category_preferences', {})
        
        # Determine likely category
        error_lower = error_text.lower()
        likely_category = None
        
        if any(word in error_lower for word in ['database', 'sql', 'connection']):
            likely_category = 'database'
        elif any(word in error_lower for word in ['network', 'timeout', 'host']):
            likely_category = 'network'
        elif any(word in error_lower for word in ['memory', 'heap', 'space']):
            likely_category = 'memory'
        elif any(word in error_lower for word in ['auth', 'permission', 'access']):
            likely_category = 'authentication'
        
        if likely_category and likely_category in category_prefs:
            preference_score = category_prefs[likely_category]
            recommendations.append({
                'text': f"Category-specific recommendation for {likely_category} issues",
                'type': 'category_learned',
                'source': f"Category preference (score: {preference_score:.2f})",
                'effectiveness': preference_score,
                'confidence': min(0.7, 0.3 + preference_score * 0.4),
                'resolution_id': None
            })
        
        return recommendations
    
    def _deduplicate_recommendations(self, recommendations: List[Dict]) -> List[Dict]:
        """Remove duplicate recommendations based on text similarity"""
        unique_recommendations = []
        seen_texts = set()
        
        for rec in recommendations:
            # Simple deduplication based on first 50 characters
            text_key = rec['text'][:50].lower()
            if text_key not in seen_texts:
                seen_texts.add(text_key)
                unique_recommendations.append(rec)
        
        return unique_recommendations
    
    def _calculate_confidence_scores(self, recommendations: List[Dict]) -> Dict:
        """Calculate overall confidence metrics for recommendations"""
        if not recommendations:
            return {'overall_confidence': 0.0, 'recommendation_count': 0}
        
        confidences = [rec['confidence'] for rec in recommendations]
        
        return {
            'overall_confidence': np.mean(confidences),
            'max_confidence': max(confidences),
            'recommendation_count': len(recommendations),
            'high_confidence_count': sum(1 for c in confidences if c > 0.7)
        }
    
    def record_feedback(self, error_hash: str, resolution_id: Optional[int], 
                       feedback_type: str, rating: int = None, comment: str = None,
                       user_id: str = None, resolution_text: str = None) -> bool:
        """Record user feedback and update learning model"""
        try:
            # Store feedback in database
            self.db.store_feedback(
                error_hash=error_hash,
                feedback_type=feedback_type,
                rating=rating,
                comment=comment,
                resolution_id=resolution_id,
                user_id=user_id
            )
            
            # Update learning cache based on feedback
            self._update_learning_cache(error_hash, feedback_type, rating, 
                                      resolution_text, user_id)
            
            # Save updated model
            self.save_learning_model()
            
            return True
        except Exception as e:
            print(f"Error recording feedback: {e}")
            return False
    
    def _update_learning_cache(self, error_hash: str, feedback_type: str, 
                             rating: int, resolution_text: str = None, 
                             user_id: str = None):
        """Update the learning cache based on feedback"""
        # Update pattern weights
        if resolution_text and rating is not None:
            # Extract patterns from resolution text
            patterns = self._extract_patterns(resolution_text)
            weight_adjustment = (rating - 3) * 0.1  # -0.2 to +0.2 adjustment
            
            for pattern in patterns:
                current_weight = self.learning_cache['pattern_weights'].get(pattern, 0.5)
                new_weight = max(0.0, min(1.0, current_weight + weight_adjustment))
                self.learning_cache['pattern_weights'][pattern] = new_weight
        
        # Update user preferences
        if user_id and rating is not None:
            if user_id not in self.learning_cache['user_preferences']:
                self.learning_cache['user_preferences'][user_id] = {}
            
            user_prefs = self.learning_cache['user_preferences'][user_id]
            user_prefs[feedback_type] = user_prefs.get(feedback_type, []) + [rating]
            
            # Keep only last 10 ratings per feedback type
            user_prefs[feedback_type] = user_prefs[feedback_type][-10:]
        
        # Update temporal patterns
        current_time = datetime.now().isoformat()
        self.learning_cache['temporal_patterns'][feedback_type].append({
            'timestamp': current_time,
            'rating': rating,
            'error_hash': error_hash
        })
        
        # Keep only last 100 temporal entries per type
        self.learning_cache['temporal_patterns'][feedback_type] = \
            self.learning_cache['temporal_patterns'][feedback_type][-100:]
    
    def _extract_patterns(self, text: str) -> List[str]:
        """Extract meaningful patterns from text"""
        patterns = []
        words = text.lower().split()
        
        # Extract important keywords
        important_words = ['restart', 'check', 'update', 'configure', 'install', 
                          'verify', 'monitor', 'analyze', 'debug', 'fix']
        
        for word in words:
            if word in important_words:
                patterns.append(word)
        
        # Extract bi-grams of important words
        for i in range(len(words) - 1):
            if any(important in words[i:i+2] for important in important_words):
                patterns.append(f"{words[i]}_{words[i+1]}")
        
        return patterns
    
    def get_learning_insights(self) -> Dict:
        """Get insights about the learning system performance"""
        stats = self.db.get_knowledge_base_stats()
        
        # Add learning-specific insights
        pattern_weights = self.learning_cache.get('pattern_weights', {})
        top_patterns = sorted(pattern_weights.items(), key=lambda x: x[1], reverse=True)[:10]
        
        user_prefs = self.learning_cache.get('user_preferences', {})
        active_users = len(user_prefs)
        
        return {
            'knowledge_base_stats': stats,
            'learning_stats': {
                'top_patterns': top_patterns,
                'active_users': active_users,
                'total_patterns': len(pattern_weights),
                'avg_pattern_weight': np.mean(list(pattern_weights.values())) if pattern_weights else 0.0
            }
        }
    
    def suggest_new_resolution(self, error_text: str, user_id: str = None) -> str:
        """Suggest a new resolution based on learning and user preferences"""
        # Get user preferences if available
        user_prefs = {}
        if user_id and user_id in self.learning_cache.get('user_preferences', {}):
            user_prefs = self.learning_cache['user_preferences'][user_id]
        
        # Generate suggestion based on patterns and preferences
        suggestion = "Based on similar issues and user feedback:\n"
        
        # Add pattern-based suggestions
        error_lower = error_text.lower()
        pattern_weights = self.learning_cache.get('pattern_weights', {})
        
        relevant_patterns = [p for p in pattern_weights.keys() if p in error_lower]
        if relevant_patterns:
            best_pattern = max(relevant_patterns, key=lambda p: pattern_weights[p])
            suggestion += f"1. Focus on '{best_pattern}' related solutions\n"
        
        # Add category-specific suggestions
        if 'database' in error_lower:
            suggestion += "2. Check database connectivity and query performance\n"
        elif 'network' in error_lower:
            suggestion += "2. Verify network configuration and firewall settings\n"
        elif 'memory' in error_lower:
            suggestion += "2. Monitor memory usage and check for leaks\n"
        
        suggestion += "3. Document the resolution for future reference"
        
        return suggestion
