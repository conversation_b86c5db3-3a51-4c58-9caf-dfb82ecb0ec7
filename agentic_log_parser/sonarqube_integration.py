#!/usr/bin/env python3
"""
SonarQube Integration Module
Code quality analysis, static analysis, and technical debt management
"""

import streamlit as st
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import numpy as np
import base64

class SonarQubeIntegration:
    """SonarQube integration for code quality analysis"""
    
    def __init__(self):
        self.sonar_url = self._get_sonar_url()
        self.sonar_token = self._get_sonar_token()
        self.headers = self._get_headers()
        
        # Sample data for demonstration
        self.sample_data = self._generate_sample_data()
    
    def _get_sonar_url(self) -> str:
        """Get SonarQube URL from session state or default"""
        return st.session_state.get('sonar_url', 'http://localhost:9000')
    
    def _get_sonar_token(self) -> Optional[str]:
        """Get SonarQube token from session state"""
        return st.session_state.get('sonar_token')
    
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers for SonarQube API"""
        if self.sonar_token:
            auth_string = base64.b64encode(f"{self.sonar_token}:".encode()).decode()
            return {
                "Authorization": f"Basic {auth_string}",
                "Content-Type": "application/json"
            }
        return {}
    
    def render_sonarqube_dashboard(self):
        """Render the main SonarQube dashboard"""
        st.markdown("## 🔍 SonarQube Code Quality Analysis")
        
        # SonarQube connection
        if not self.sonar_token:
            self._render_connection_setup()
            return
        
        # Main dashboard tabs
        tabs = st.tabs([
            "📊 Overview",
            "🔍 Code Quality",
            "🐛 Issues",
            "📈 Metrics",
            "⚙️ Projects"
        ])
        
        with tabs[0]:
            self.render_overview_tab()
        
        with tabs[1]:
            self.render_quality_tab()
        
        with tabs[2]:
            self.render_issues_tab()
        
        with tabs[3]:
            self.render_metrics_tab()
        
        with tabs[4]:
            self.render_projects_tab()
    
    def _render_connection_setup(self):
        """Render SonarQube connection setup"""
        st.markdown("### 🔗 SonarQube Connection Setup")
        
        st.info("Configure your SonarQube connection to access code quality analysis features.")
        
        with st.form("sonar_connection"):
            col1, col2 = st.columns(2)
            
            with col1:
                sonar_url = st.text_input(
                    "SonarQube URL",
                    value="http://localhost:9000",
                    help="URL of your SonarQube instance"
                )
            
            with col2:
                sonar_token = st.text_input(
                    "Authentication Token",
                    type="password",
                    help="Generate a token in SonarQube: User > My Account > Security"
                )
            
            st.markdown("**Required Permissions:**")
            st.markdown("- Browse projects")
            st.markdown("- Execute analysis")
            st.markdown("- View issues")
            
            if st.form_submit_button("🔗 Connect to SonarQube"):
                if sonar_url and sonar_token:
                    if self._validate_connection(sonar_url, sonar_token):
                        st.session_state.sonar_url = sonar_url
                        st.session_state.sonar_token = sonar_token
                        st.success("✅ Successfully connected to SonarQube!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to connect to SonarQube. Please check your URL and token.")
                else:
                    st.error("Please provide both URL and token")
    
    def _validate_connection(self, url: str, token: str) -> bool:
        """Validate SonarQube connection"""
        try:
            auth_string = base64.b64encode(f"{token}:".encode()).decode()
            headers = {"Authorization": f"Basic {auth_string}"}
            
            response = requests.get(f"{url}/api/system/status", headers=headers, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def render_overview_tab(self):
        """Render SonarQube overview dashboard"""
        st.markdown("### 📊 Code Quality Overview")
        
        # Quality metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("🎯 Quality Gate", "Passed", "✅")
        
        with col2:
            st.metric("🐛 Bugs", "12", "-3")
        
        with col3:
            st.metric("🔒 Vulnerabilities", "5", "-1")
        
        with col4:
            st.metric("💨 Code Smells", "89", "+7")
        
        # Quality gate status
        st.markdown("### 🚦 Quality Gate Status")
        
        projects = self.sample_data['projects']
        
        for project in projects:
            quality_gate = project['quality_gate']
            status_icon = "✅" if quality_gate['status'] == 'PASSED' else "❌"
            status_color = "green" if quality_gate['status'] == 'PASSED' else "red"
            
            with st.expander(f"{status_icon} {project['name']} - {quality_gate['status']}"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Coverage:** {project['coverage']:.1f}%")
                    st.markdown(f"**Duplicated Lines:** {project['duplicated_lines']:.1f}%")
                    st.markdown(f"**Maintainability Rating:** {project['maintainability_rating']}")
                    st.markdown(f"**Reliability Rating:** {project['reliability_rating']}")
                    st.markdown(f"**Security Rating:** {project['security_rating']}")
                
                with col2:
                    if st.button("📊 Details", key=f"details_{project['key']}"):
                        self._show_project_details(project)
                    
                    if st.button("🔍 Issues", key=f"issues_{project['key']}"):
                        self._show_project_issues(project)
        
        # Recent analysis
        st.markdown("### 📈 Recent Analysis")
        
        analysis_data = []
        for project in projects:
            analysis_data.append({
                'Project': project['name'],
                'Last Analysis': project['last_analysis'],
                'Quality Gate': project['quality_gate']['status'],
                'Coverage': f"{project['coverage']:.1f}%",
                'Issues': project['bugs'] + project['vulnerabilities'] + project['code_smells']
            })
        
        df = pd.DataFrame(analysis_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    
    def render_quality_tab(self):
        """Render code quality analysis tab"""
        st.markdown("### 🔍 Code Quality Analysis")
        
        # Quality metrics overview
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_quality_metrics_chart()
        
        with col2:
            self._render_technical_debt_chart()
        
        # Detailed quality analysis
        st.markdown("### 📋 Quality Analysis by Project")
        
        projects = self.sample_data['projects']
        
        quality_data = []
        for project in projects:
            quality_data.append({
                'Project': project['name'],
                'Lines of Code': project['lines_of_code'],
                'Coverage': f"{project['coverage']:.1f}%",
                'Duplicated Lines': f"{project['duplicated_lines']:.1f}%",
                'Technical Debt': project['technical_debt'],
                'Maintainability': project['maintainability_rating'],
                'Reliability': project['reliability_rating'],
                'Security': project['security_rating']
            })
        
        df = pd.DataFrame(quality_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Code coverage trends
        st.markdown("### 📈 Code Coverage Trends")
        
        # Generate sample coverage trend data
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        coverage_trend = [75 + np.random.uniform(-5, 10) for _ in dates]
        coverage_trend = np.clip(coverage_trend, 0, 100)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=coverage_trend,
            mode='lines+markers',
            name='Coverage %',
            line=dict(color='#28a745', width=3)
        ))
        
        fig.add_hline(y=80, line_dash="dash", line_color="red", annotation_text="Target: 80%")
        
        fig.update_layout(
            title="Code Coverage Trend (Last 30 Days)",
            xaxis_title="Date",
            yaxis_title="Coverage %",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def render_issues_tab(self):
        """Render issues analysis tab"""
        st.markdown("### 🐛 Issues Analysis")
        
        # Issues overview
        col1, col2, col3 = st.columns(3)
        
        total_bugs = sum(p['bugs'] for p in self.sample_data['projects'])
        total_vulnerabilities = sum(p['vulnerabilities'] for p in self.sample_data['projects'])
        total_code_smells = sum(p['code_smells'] for p in self.sample_data['projects'])
        
        with col1:
            st.metric("🐛 Bugs", total_bugs, "-3")
        
        with col2:
            st.metric("🔒 Vulnerabilities", total_vulnerabilities, "-1")
        
        with col3:
            st.metric("💨 Code Smells", total_code_smells, "+7")
        
        # Issues by severity
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_issues_by_severity_chart()
        
        with col2:
            self._render_issues_by_type_chart()
        
        # Detailed issues list
        st.markdown("### 📋 Issues Details")
        
        issues = self._generate_sample_issues()
        
        # Issues filtering
        col1, col2, col3 = st.columns(3)
        
        with col1:
            severity_filter = st.selectbox("Severity", ["All", "Blocker", "Critical", "Major", "Minor", "Info"])
        
        with col2:
            type_filter = st.selectbox("Type", ["All", "Bug", "Vulnerability", "Code Smell"])
        
        with col3:
            status_filter = st.selectbox("Status", ["All", "Open", "Confirmed", "Resolved", "Closed"])
        
        # Filter issues
        filtered_issues = self._filter_issues(issues, severity_filter, type_filter, status_filter)
        
        if filtered_issues:
            for issue in filtered_issues[:10]:  # Show first 10
                severity_color = {
                    'BLOCKER': '#d73027',
                    'CRITICAL': '#f46d43',
                    'MAJOR': '#fdae61',
                    'MINOR': '#fee08b',
                    'INFO': '#e6f598'
                }.get(issue['severity'], '#cccccc')
                
                with st.expander(f"🔍 {issue['message']} ({issue['severity']})"):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.markdown(f"**Type:** {issue['type']}")
                        st.markdown(f"**Severity:** {issue['severity']}")
                        st.markdown(f"**Component:** {issue['component']}")
                        st.markdown(f"**Line:** {issue['line']}")
                        st.markdown(f"**Status:** {issue['status']}")
                        st.markdown(f"**Created:** {issue['creation_date']}")
                    
                    with col2:
                        if st.button("🔧 Fix", key=f"fix_{issue['key']}"):
                            st.info("Opening issue in IDE...")
                        
                        if st.button("✅ Resolve", key=f"resolve_{issue['key']}"):
                            st.success("Issue marked as resolved")
        else:
            st.info("No issues found for the selected filters")
    
    def render_metrics_tab(self):
        """Render metrics and trends tab"""
        st.markdown("### 📈 Quality Metrics & Trends")
        
        # Metrics overview
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_complexity_metrics()
        
        with col2:
            self._render_maintainability_trends()
        
        # Technical debt analysis
        st.markdown("### 💰 Technical Debt Analysis")
        
        projects = self.sample_data['projects']
        
        debt_data = []
        for project in projects:
            debt_data.append({
                'Project': project['name'],
                'Technical Debt': project['technical_debt'],
                'Debt Ratio': f"{project['debt_ratio']:.1f}%",
                'Effort (hours)': project['remediation_effort'],
                'Priority': project['debt_priority']
            })
        
        df = pd.DataFrame(debt_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Quality evolution
        st.markdown("### 📊 Quality Evolution")
        
        # Generate sample quality evolution data
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        bugs_trend = [15 + np.random.randint(-3, 5) for _ in dates]
        vulnerabilities_trend = [8 + np.random.randint(-2, 3) for _ in dates]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=bugs_trend,
            mode='lines+markers',
            name='Bugs',
            line=dict(color='#dc3545', width=2)
        ))
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=vulnerabilities_trend,
            mode='lines+markers',
            name='Vulnerabilities',
            line=dict(color='#fd7e14', width=2)
        ))
        
        fig.update_layout(
            title="Issues Trend (Last 30 Days)",
            xaxis_title="Date",
            yaxis_title="Count",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def render_projects_tab(self):
        """Render projects management tab"""
        st.markdown("### ⚙️ Projects Management")
        
        # Projects overview
        projects = self.sample_data['projects']
        
        for project in projects:
            with st.expander(f"📁 {project['name']}"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Key:** {project['key']}")
                    st.markdown(f"**Language:** {project['language']}")
                    st.markdown(f"**Lines of Code:** {project['lines_of_code']:,}")
                    st.markdown(f"**Last Analysis:** {project['last_analysis']}")
                    st.markdown(f"**Quality Gate:** {project['quality_gate']['status']}")
                
                with col2:
                    if st.button("🔍 Analyze", key=f"analyze_{project['key']}"):
                        self._trigger_analysis(project)
                    
                    if st.button("⚙️ Configure", key=f"configure_{project['key']}"):
                        self._configure_project(project)
                    
                    if st.button("📊 Report", key=f"report_{project['key']}"):
                        self._generate_project_report(project)
        
        # Add new project
        st.markdown("### ➕ Add New Project")
        
        with st.form("add_project"):
            col1, col2 = st.columns(2)
            
            with col1:
                project_key = st.text_input("Project Key", placeholder="my-project")
                project_name = st.text_input("Project Name", placeholder="My Project")
            
            with col2:
                project_language = st.selectbox("Main Language", ["Java", "Python", "JavaScript", "C#", "Go", "Rust"])
                source_path = st.text_input("Source Path", placeholder="src/")
            
            if st.form_submit_button("➕ Add Project"):
                if project_key and project_name:
                    st.success(f"Project '{project_name}' added successfully!")
                else:
                    st.error("Please fill in required fields")
    
    def _render_quality_metrics_chart(self):
        """Render quality metrics chart"""
        projects = self.sample_data['projects']
        
        project_names = [p['name'] for p in projects]
        coverage_values = [p['coverage'] for p in projects]
        
        fig = go.Figure(data=[go.Bar(
            x=project_names,
            y=coverage_values,
            marker_color=['green' if c >= 80 else 'orange' if c >= 60 else 'red' for c in coverage_values]
        )])
        
        fig.update_layout(
            title="Code Coverage by Project",
            xaxis_title="Project",
            yaxis_title="Coverage %",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_technical_debt_chart(self):
        """Render technical debt chart"""
        projects = self.sample_data['projects']
        
        debt_data = [p['remediation_effort'] for p in projects]
        project_names = [p['name'] for p in projects]
        
        fig = go.Figure(data=[go.Pie(
            labels=project_names,
            values=debt_data,
            hole=0.4
        )])
        
        fig.update_layout(
            title="Technical Debt Distribution",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_issues_by_severity_chart(self):
        """Render issues by severity chart"""
        severity_data = {
            'BLOCKER': 2,
            'CRITICAL': 5,
            'MAJOR': 23,
            'MINOR': 45,
            'INFO': 31
        }
        
        colors = ['#d73027', '#f46d43', '#fdae61', '#fee08b', '#e6f598']
        
        fig = go.Figure(data=[go.Bar(
            x=list(severity_data.keys()),
            y=list(severity_data.values()),
            marker_color=colors
        )])
        
        fig.update_layout(
            title="Issues by Severity",
            xaxis_title="Severity",
            yaxis_title="Count",
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_issues_by_type_chart(self):
        """Render issues by type chart"""
        type_data = {
            'Bug': 12,
            'Vulnerability': 5,
            'Code Smell': 89
        }
        
        fig = go.Figure(data=[go.Pie(
            labels=list(type_data.keys()),
            values=list(type_data.values()),
            hole=0.4
        )])
        
        fig.update_layout(
            title="Issues by Type",
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _render_complexity_metrics(self):
        """Render complexity metrics"""
        st.markdown("#### 🔄 Complexity Metrics")
        
        complexity_data = {
            'Cyclomatic Complexity': 8.5,
            'Cognitive Complexity': 12.3,
            'Function Complexity': 6.7
        }
        
        for metric, value in complexity_data.items():
            st.metric(metric, f"{value:.1f}")
    
    def _render_maintainability_trends(self):
        """Render maintainability trends"""
        st.markdown("#### 🔧 Maintainability Index")
        
        # Generate sample maintainability data
        dates = pd.date_range(start=datetime.now() - timedelta(days=7), end=datetime.now(), freq='D')
        maintainability = [75 + np.random.uniform(-5, 5) for _ in dates]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=maintainability,
            mode='lines+markers',
            name='Maintainability Index',
            line=dict(color='#007bff', width=3)
        ))
        
        fig.update_layout(
            title="Maintainability Trend",
            xaxis_title="Date",
            yaxis_title="Index",
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _generate_sample_data(self) -> Dict:
        """Generate sample SonarQube data"""
        return {
            'projects': [
                {
                    'key': 'web-app',
                    'name': 'Web Application',
                    'language': 'JavaScript',
                    'lines_of_code': 15420,
                    'coverage': 82.5,
                    'duplicated_lines': 3.2,
                    'bugs': 5,
                    'vulnerabilities': 2,
                    'code_smells': 34,
                    'technical_debt': '2d 4h',
                    'debt_ratio': 1.2,
                    'remediation_effort': 20,
                    'debt_priority': 'High',
                    'maintainability_rating': 'A',
                    'reliability_rating': 'A',
                    'security_rating': 'B',
                    'quality_gate': {'status': 'PASSED'},
                    'last_analysis': '2 hours ago'
                },
                {
                    'key': 'api-service',
                    'name': 'API Service',
                    'language': 'Python',
                    'lines_of_code': 8750,
                    'coverage': 91.3,
                    'duplicated_lines': 1.8,
                    'bugs': 3,
                    'vulnerabilities': 1,
                    'code_smells': 18,
                    'technical_debt': '1d 2h',
                    'debt_ratio': 0.8,
                    'remediation_effort': 10,
                    'debt_priority': 'Medium',
                    'maintainability_rating': 'A',
                    'reliability_rating': 'A',
                    'security_rating': 'A',
                    'quality_gate': {'status': 'PASSED'},
                    'last_analysis': '4 hours ago'
                },
                {
                    'key': 'mobile-app',
                    'name': 'Mobile App',
                    'language': 'Java',
                    'lines_of_code': 12300,
                    'coverage': 67.8,
                    'duplicated_lines': 5.1,
                    'bugs': 4,
                    'vulnerabilities': 2,
                    'code_smells': 37,
                    'technical_debt': '3d 1h',
                    'debt_ratio': 2.1,
                    'remediation_effort': 25,
                    'debt_priority': 'High',
                    'maintainability_rating': 'B',
                    'reliability_rating': 'B',
                    'security_rating': 'B',
                    'quality_gate': {'status': 'FAILED'},
                    'last_analysis': '6 hours ago'
                }
            ]
        }
    
    def _generate_sample_issues(self) -> List[Dict]:
        """Generate sample issues data"""
        return [
            {
                'key': 'issue_1',
                'type': 'Bug',
                'severity': 'MAJOR',
                'message': 'Null pointer dereference',
                'component': 'src/main/java/Service.java',
                'line': 45,
                'status': 'Open',
                'creation_date': '2024-01-15'
            },
            {
                'key': 'issue_2',
                'type': 'Vulnerability',
                'severity': 'CRITICAL',
                'message': 'SQL injection vulnerability',
                'component': 'src/main/java/Repository.java',
                'line': 78,
                'status': 'Open',
                'creation_date': '2024-01-14'
            },
            {
                'key': 'issue_3',
                'type': 'Code Smell',
                'severity': 'MINOR',
                'message': 'Method too complex',
                'component': 'src/main/java/Controller.java',
                'line': 123,
                'status': 'Confirmed',
                'creation_date': '2024-01-13'
            }
        ]
    
    def _filter_issues(self, issues: List[Dict], severity: str, issue_type: str, status: str) -> List[Dict]:
        """Filter issues based on criteria"""
        filtered = issues.copy()
        
        if severity != "All":
            filtered = [i for i in filtered if i['severity'] == severity.upper()]
        
        if issue_type != "All":
            filtered = [i for i in filtered if i['type'] == issue_type]
        
        if status != "All":
            filtered = [i for i in filtered if i['status'] == status]
        
        return filtered
    
    def _show_project_details(self, project: Dict):
        """Show project details"""
        st.info(f"Project details for: {project['name']}")
    
    def _show_project_issues(self, project: Dict):
        """Show project issues"""
        st.info(f"Issues for project: {project['name']}")
    
    def _trigger_analysis(self, project: Dict):
        """Trigger project analysis"""
        st.success(f"Analysis triggered for: {project['name']}")
    
    def _configure_project(self, project: Dict):
        """Configure project"""
        st.info(f"Configuring project: {project['name']}")
    
    def _generate_project_report(self, project: Dict):
        """Generate project report"""
        st.success(f"Report generated for: {project['name']}")
