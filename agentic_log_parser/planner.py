def generate_next_action(goal, history):
    if "extract_errors_and_warnings" not in history:
        return """TOOL: extract_errors_and_warnings
INPUT: logs
EXPLANATION: First, extract all error and warning lines from logs."""
    elif "suggest_fix_multiple" not in history:
        return """TOOL: suggest_fix_multiple
INPUT: extracted_messages
EXPLANATION: Suggest fixes for all extracted errors/warnings."""
    else:
        return "DONE: All issues analyzed and suggestions provided."
