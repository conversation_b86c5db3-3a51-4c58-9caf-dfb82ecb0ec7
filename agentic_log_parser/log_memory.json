{"errors": {"[ERROR] Failed to connect to database": 331, "[ERROR] Service unavailable at endpoint": 329, "[ERROR] GitHub API rate limit exceeded": 4, "[ERROR] Sonar Analysis failed": 331, "[ERROR] Unable to execute code coverage job": 327, "[error] failed to connect to database": 1, "[error] service unavailable at endpoint": 1, "[error] sonar analysis failed": 1, "[error] unable to execute code coverage job": 1, "[ERROR] 2025-05-22 10:17:45 - Unexpected exception: NullPointerException at line 54 in FileProcessor.java": 144, "[ERROR] 2025-05-22 10:17:45 -  Failed to connect to database": 6, "[ERROR] 2025-05-22 10:17:45 -  Service unavailable at endpoint": 6, "[ERROR] 2025-05-22 10:17:45 -  Sonar Analysis failed": 6, "[ERROR] 2025-05-22 10:17:45 -  Unable to execute code coverage job": 6, "[ERROR] 2025-05-22 10:18:00 - Service response time exceeded threshold: 12000 ms": 16, "[ERROR] 2025-05-22 10:19:10 - Multiple failed login attempts detected from IP": 16}, "warnings": {"[WARNING] Disk space running low": 280, "[warning] disk space running low": 1, "[WARNING] 2025-05-22 10:17:45 -  Disk space running low": 6}}