#!/usr/bin/env python3
"""
Test script for Historical Database and Feedback Loop features
"""

from historical_database import HistoricalDatabase
from feedback_loop import FeedbackLoop
import json
import time

def test_historical_database():
    """Test the historical database functionality"""
    print("🧪 Testing Historical Database")
    print("=" * 50)
    
    # Initialize database
    db = HistoricalDatabase("test_historical.db")
    print("✅ Database initialized")
    
    # Test storing errors
    print("\n1. Testing Error Storage...")
    
    sample_errors = [
        "Database connection failed: Connection timeout after 30 seconds",
        "Network timeout: Unable to reach host api.example.com",
        "Out of memory: Java heap space exceeded",
        "Authentication failed: Invalid credentials for user: jane.smith",
        "Database query failed: SQL syntax error in SELECT statement"
    ]
    
    error_hashes = []
    for i, error in enumerate(sample_errors):
        error_hash = db.store_error(
            error_text=error,
            category=["database", "network", "memory", "authentication", "database"][i],
            severity=["high", "high", "critical", "medium", "high"][i]
        )
        error_hashes.append(error_hash)
        print(f"  Stored error {i+1}: {error_hash[:8]}...")
    
    print(f"✅ Stored {len(sample_errors)} errors")
    
    # Test storing resolutions
    print("\n2. Testing Resolution Storage...")
    
    sample_resolutions = [
        "Check database connectivity and restart database service",
        "Verify network configuration and firewall settings",
        "Increase heap size and check for memory leaks",
        "Reset user credentials and verify permissions",
        "Review SQL query syntax and fix errors"
    ]
    
    resolution_ids = []
    for i, (error_hash, resolution) in enumerate(zip(error_hashes, sample_resolutions)):
        resolution_id = db.store_resolution(
            error_hash=error_hash,
            resolution_text=resolution,
            resolution_type="manual",
            source="test_user"
        )
        resolution_ids.append(resolution_id)
        print(f"  Stored resolution {i+1}: ID {resolution_id}")
    
    print(f"✅ Stored {len(sample_resolutions)} resolutions")
    
    # Test storing feedback
    print("\n3. Testing Feedback Storage...")
    
    for i, (error_hash, resolution_id) in enumerate(zip(error_hashes, resolution_ids)):
        rating = [5, 4, 3, 4, 5][i]  # Varied ratings
        db.store_feedback(
            error_hash=error_hash,
            resolution_id=resolution_id,
            feedback_type="resolution_effectiveness",
            rating=rating,
            comment=f"Test feedback {i+1}",
            user_id="test_user"
        )
        print(f"  Stored feedback {i+1}: Rating {rating}")
    
    print("✅ Stored feedback for all resolutions")
    
    # Test retrieving historical resolutions
    print("\n4. Testing Historical Resolution Retrieval...")
    
    for i, error_hash in enumerate(error_hashes[:2]):  # Test first 2
        resolutions = db.get_historical_resolutions(error_hash)
        print(f"  Error {i+1}: Found {len(resolutions)} resolutions")
        if resolutions:
            best_resolution = resolutions[0]
            print(f"    Best resolution effectiveness: {best_resolution['effectiveness_score']:.2f}")
    
    # Test similar errors
    print("\n5. Testing Similar Error Detection...")
    
    test_error = "Database connection timeout occurred"
    similar = db.get_similar_errors(test_error, limit=3)
    print(f"  Found {len(similar)} similar errors for: '{test_error}'")
    for sim in similar:
        print(f"    Similarity {sim['similarity']:.2f}: {sim['error_text'][:50]}...")
    
    # Test trending errors
    print("\n6. Testing Trending Error Analysis...")
    
    trending = db.get_trending_errors(days=7)
    print(f"  Found {len(trending)} trending errors")
    for trend in trending[:3]:
        print(f"    {trend['occurrence_count']} occurrences: {trend['error_text'][:50]}...")
    
    # Test knowledge base stats
    print("\n7. Testing Knowledge Base Statistics...")
    
    stats = db.get_knowledge_base_stats()
    print(f"  Total errors: {stats['total_errors']}")
    print(f"  Total resolutions: {stats['total_resolutions']}")
    print(f"  Total feedback: {stats['total_feedback']}")
    print(f"  Average effectiveness: {stats['avg_effectiveness']:.2f}")
    print(f"  Top categories: {[cat['category'] for cat in stats['top_categories']]}")
    
    print("\n✅ Historical Database tests completed!")
    return db, error_hashes, resolution_ids

def test_feedback_loop(db, error_hashes):
    """Test the feedback loop functionality"""
    print("\n🧪 Testing Feedback Loop")
    print("=" * 50)
    
    # Initialize feedback loop
    feedback_loop = FeedbackLoop(db.db_path)
    print("✅ Feedback loop initialized")
    
    # Test processing error with history
    print("\n1. Testing Error Processing with History...")
    
    test_error = "Database connection failed: timeout after 30 seconds"
    result = feedback_loop.process_error_with_history(
        error_text=test_error,
        category="database",
        severity="high"
    )
    
    print(f"  Error hash: {result['error_hash'][:8]}...")
    print(f"  Historical resolutions: {len(result['historical_resolutions'])}")
    print(f"  Similar errors: {len(result['similar_errors'])}")
    print(f"  Enhanced recommendations: {len(result['enhanced_recommendations'])}")
    
    # Show some recommendations
    for i, rec in enumerate(result['enhanced_recommendations'][:3]):
        print(f"    Rec {i+1}: {rec['text'][:60]}... (confidence: {rec['confidence']:.2f})")
    
    # Test recording feedback
    print("\n2. Testing Feedback Recording...")
    
    feedback_success = feedback_loop.record_feedback(
        error_hash=result['error_hash'],
        resolution_id=None,
        feedback_type="recommendation_quality",
        rating=4,
        comment="Good recommendations, helped resolve the issue",
        user_id="test_user",
        resolution_text="Restarted database service and increased timeout"
    )
    
    print(f"  Feedback recorded: {feedback_success}")
    
    # Test learning insights
    print("\n3. Testing Learning Insights...")
    
    insights = feedback_loop.get_learning_insights()
    kb_stats = insights['knowledge_base_stats']
    learning_stats = insights['learning_stats']
    
    print(f"  Knowledge base errors: {kb_stats['total_errors']}")
    print(f"  Active users: {learning_stats['active_users']}")
    print(f"  Total patterns: {learning_stats['total_patterns']}")
    print(f"  Top patterns: {learning_stats['top_patterns'][:3]}")
    
    # Test suggestion generation
    print("\n4. Testing Resolution Suggestions...")
    
    suggestion = feedback_loop.suggest_new_resolution(
        error_text="Memory leak detected in application",
        user_id="test_user"
    )
    
    print(f"  Generated suggestion:")
    print(f"    {suggestion}")
    
    print("\n✅ Feedback Loop tests completed!")
    return feedback_loop

def test_integration():
    """Test integration between components"""
    print("\n🧪 Testing Integration")
    print("=" * 50)
    
    # Test with enhanced analyzer
    try:
        from enhanced_agent import EnhancedLogAnalyzer
        
        sample_logs = """
        2024-06-09 10:15:23 [ERROR] Database connection failed: Connection timeout
        2024-06-09 10:16:45 [ERROR] Network timeout: Unable to reach host
        2024-06-09 10:17:12 [WARNING] High memory usage detected
        """
        
        analyzer = EnhancedLogAnalyzer(
            sample_logs, 
            enable_deep_learning=False,
            enable_historical_learning=True
        )
        
        results = analyzer.run_comprehensive_analysis()
        
        print("✅ Enhanced analyzer with historical learning works!")
        
        if 'historical_analysis' in results:
            hist_analysis = results['historical_analysis']
            print(f"  Enhanced recommendations: {len(hist_analysis.get('enhanced_recommendations', []))}")
            print(f"  Similar errors: {len(hist_analysis.get('similar_errors', []))}")
        
        if 'historical_insights' in results:
            hist_insights = results['historical_insights']
            kb_stats = hist_insights.get('knowledge_base_stats', {})
            print(f"  Knowledge base total errors: {kb_stats.get('total_errors', 0)}")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()

def cleanup_test_files():
    """Clean up test database files"""
    import os
    test_files = ["test_historical.db", "feedback_model.pkl"]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ Cleaned up {file}")

def main():
    """Run all tests"""
    print("🚀 Historical Database & Feedback Loop Test Suite")
    print("=" * 60)
    
    try:
        # Test historical database
        db, error_hashes, resolution_ids = test_historical_database()
        
        # Test feedback loop
        feedback_loop = test_feedback_loop(db, error_hashes)
        
        # Test integration
        test_integration()
        
        print("\n🎉 All tests completed successfully!")
        print("\nThe Historical Database & Feedback Loop system is ready with:")
        print("  ✅ Error storage and retrieval")
        print("  ✅ Resolution tracking with effectiveness scoring")
        print("  ✅ User feedback collection and learning")
        print("  ✅ Similar error detection")
        print("  ✅ Trending pattern analysis")
        print("  ✅ Enhanced recommendation generation")
        print("  ✅ Integration with Enhanced AI Analyzer")
        
        # Ask if user wants to keep test data
        keep_data = input("\nKeep test data for exploration? (y/n): ").lower().strip()
        if keep_data != 'y':
            cleanup_test_files()
        else:
            print("📁 Test data preserved in test_historical.db")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        cleanup_test_files()

if __name__ == "__main__":
    main()
