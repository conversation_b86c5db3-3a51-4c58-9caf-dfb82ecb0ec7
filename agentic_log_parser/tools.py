import re

def extract_errors_and_warnings(log_text):
    error_lines = re.findall(r'\[ERROR\](.*)', log_text)
    warning_lines = re.findall(r'\[WARNING\](.*)', log_text)
    return {
        "errors": [e.strip() for e in error_lines],
        "warnings": [w.strip() for w in warning_lines]
    }

def suggest_fix_multiple(messages):
    suggestions = []
    known_fixes = {
        "failed to connect to database": "Check DB credentials or network access.",
        "service unavailable": "Check if the service is up and reachable.",
        "rate limit": "Use authenticated API or retry after cooldown.",
        "disk space": "Free up space or extend disk capacity.",
    }
    for msg in messages:
        found = False
        for key, fix in known_fixes.items():
            if key in msg.lower():
                suggestions.append(f"{msg} => {fix}")
                found = True
                break
        if not found:
            suggestions.append(f"{msg} => No known fix found.")
    return "\n".join(suggestions)

TOOLS = {
    "extract_errors_and_warnings": extract_errors_and_warnings,
    "suggest_fix_multiple": suggest_fix_multiple,
}
