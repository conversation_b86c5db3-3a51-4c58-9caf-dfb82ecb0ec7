{"deploy_20250609_213848_0": {"id": "deploy_20250609_213848_0", "build_id": "build_20250609_213848_0", "environment": "development", "status": "success", "created_at": "2025-06-09T21:38:48.414958", "deployed_at": "2025-06-09T21:38:48.415178", "duration": 0, "version": "v1.4.4", "rollback_version": null, "approval_required": false, "approved_by": "auto-deploy", "deployment_logs": ["Deployment approved by auto-deploy", "Starting deployment...", "Deploying to environment...", "Application deployed successfully ✓", "Health checks passed ✓", "Deployment completed ✓"]}, "deploy_20250609_213848_1": {"id": "deploy_20250609_213848_1", "build_id": "build_20250609_213848_0", "environment": "staging", "status": "pending", "created_at": "2025-06-09T21:38:48.415498", "deployed_at": null, "duration": null, "version": "v1.2.9", "rollback_version": null, "approval_required": true, "approved_by": null, "deployment_logs": []}, "deploy_20250609_213848_2": {"id": "deploy_20250609_213848_2", "build_id": "build_20250609_213848_2", "environment": "development", "status": "success", "created_at": "2025-06-09T21:38:48.415684", "deployed_at": "2025-06-09T21:38:48.415892", "duration": 0, "version": "v1.7.8", "rollback_version": null, "approval_required": false, "approved_by": "auto-deploy", "deployment_logs": ["Deployment approved by auto-deploy", "Starting deployment...", "Deploying to environment...", "Application deployed successfully ✓", "Health checks passed ✓", "Deployment completed ✓"]}, "deploy_20250609_213848_3": {"id": "deploy_20250609_213848_3", "build_id": "build_20250609_213848_2", "environment": "staging", "status": "pending", "created_at": "2025-06-09T21:38:48.416321", "deployed_at": null, "duration": null, "version": "v1.4.1", "rollback_version": null, "approval_required": true, "approved_by": null, "deployment_logs": []}}