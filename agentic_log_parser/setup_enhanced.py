#!/usr/bin/env python3
"""
Setup script for Enhanced AI Log Analyzer
Installs dependencies and downloads required models
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install Python requirements"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )

def download_spacy_model():
    """Download spaCy English model"""
    return run_command(
        f"{sys.executable} -m spacy download en_core_web_sm",
        "Downloading spaCy English model"
    )

def download_nltk_data():
    """Download required NLTK data"""
    nltk_script = """
import nltk
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
    nltk.download('omw-1.4', quiet=True)
    print("NLTK data downloaded successfully")
except Exception as e:
    print(f"Error downloading NLTK data: {e}")
    exit(1)
"""
    
    return run_command(
        f"{sys.executable} -c \"{nltk_script}\"",
        "Downloading NLTK data"
    )

def test_imports():
    """Test if all required packages can be imported"""
    test_script = """
import sys
try:
    # Core packages
    import streamlit
    import pandas
    import numpy
    import sklearn
    import matplotlib
    import seaborn
    import plotly
    
    # NLP packages
    import nltk
    import spacy
    import textblob
    
    # ML packages
    import scipy
    import statsmodels
    
    # Optional deep learning packages
    try:
        import transformers
        import sentence_transformers
        import torch
        print("✅ All packages including deep learning libraries imported successfully")
    except ImportError as e:
        print(f"⚠️ Deep learning packages not available: {e}")
        print("✅ Core packages imported successfully (deep learning features will be disabled)")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
    
    return run_command(
        f"{sys.executable} -c \"{test_script}\"",
        "Testing package imports"
    )

def create_sample_config():
    """Create a sample configuration file"""
    config_content = """# Enhanced AI Log Analyzer Configuration

# Analysis Settings
ENABLE_DEEP_LEARNING = True
ANOMALY_CONTAMINATION_RATE = 0.1
MAX_TFIDF_FEATURES = 1000
CLUSTERING_EPS = 0.5
MIN_CLUSTER_SAMPLES = 5

# Performance Settings
BATCH_SIZE = 1000
MAX_LOG_LENGTH = 10000
ENABLE_CACHING = True

# Model Settings
SENTENCE_TRANSFORMER_MODEL = "all-MiniLM-L6-v2"
SPACY_MODEL = "en_core_web_sm"

# Visualization Settings
MAX_ANOMALIES_DISPLAY = 10
MAX_ROOT_CAUSES_DISPLAY = 5
CHART_HEIGHT = 400
"""
    
    config_file = Path("config.py")
    if not config_file.exists():
        with open(config_file, 'w') as f:
            f.write(config_content)
        print("✅ Sample configuration file created: config.py")
    else:
        print("ℹ️ Configuration file already exists")

def main():
    """Main setup function"""
    print("🚀 Enhanced AI Log Analyzer Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements. Please check your internet connection and try again.")
        sys.exit(1)
    
    # Download models
    print("\n📦 Downloading required models...")
    
    spacy_success = download_spacy_model()
    nltk_success = download_nltk_data()
    
    if not spacy_success:
        print("⚠️ spaCy model download failed. Some NLP features may not work.")
    
    if not nltk_success:
        print("⚠️ NLTK data download failed. Some text processing features may not work.")
    
    # Test imports
    if not test_imports():
        print("❌ Package import test failed. Please check the installation.")
        sys.exit(1)
    
    # Create sample config
    create_sample_config()
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run the application: streamlit run app.py")
    print("2. Enable 'AI Analysis' in the sidebar for enhanced features")
    print("3. Upload your log files and explore the advanced analytics")
    print("\nFor more information, see ENHANCED_FEATURES.md")

if __name__ == "__main__":
    main()
