import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import requests
import subprocess
import os
from typing import Dict, List, Optional
import time

# Import our enhanced components
from enhanced_agent import <PERSON>han<PERSON><PERSON>ogAnal<PERSON><PERSON>
from advanced_visualizations import AdvancedLogVisualizer
from historical_database import HistoricalDatabase
from feedback_loop import Feedback<PERSON>oop
from cicd_dashboard import CICDDashboard

class DevOpsAssistant:
    """
    Professional DevOps Assistant with comprehensive monitoring and automation capabilities
    """
    
    def __init__(self):
        self.initialize_session_state()
        self.setup_page_config()
        self.cicd_dashboard = CICDDashboard()
        
    def initialize_session_state(self):
        """Initialize session state variables"""
        if 'alerts' not in st.session_state:
            st.session_state.alerts = []
        if 'incidents' not in st.session_state:
            st.session_state.incidents = []
        if 'deployments' not in st.session_state:
            st.session_state.deployments = []
        if 'metrics_history' not in st.session_state:
            st.session_state.metrics_history = []
        if 'user_role' not in st.session_state:
            st.session_state.user_role = 'DevOps Engineer'
        if 'selected_environment' not in st.session_state:
            st.session_state.selected_environment = 'Production'
    
    def setup_page_config(self):
        """Configure the Streamlit page with professional settings"""
        st.set_page_config(
            page_title="DevOps Assistant - Enterprise Platform",
            page_icon="🚀",
            layout="wide",
            initial_sidebar_state="expanded",
            menu_items={
                'Get Help': 'https://docs.devops-assistant.com',
                'Report a bug': 'https://github.com/devops-assistant/issues',
                'About': "DevOps Assistant v2.0 - AI-Powered Infrastructure Management"
            }
        )
    
    def render_custom_css(self):
        """Render custom CSS for professional business UI"""
        st.markdown("""
        <style>
            /* Global Styles */
            .main {
                padding-top: 1rem;
            }

            /* Force white text in header */
            .main-header * {
                color: white !important;
            }
            
            /* Header Styles */
            .main-header {
                background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
                color: white !important;
                padding: 2rem;
                border-radius: 10px;
                margin-bottom: 2rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            .main-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 0;
                color: white !important;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }

            .main-subtitle {
                font-size: 1.2rem;
                opacity: 0.95;
                margin-top: 0.5rem;
                color: white !important;
            }
            
            /* Card Styles */
            .metric-card {
                background: white;
                padding: 1.5rem;
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border-left: 4px solid #2a5298;
                margin-bottom: 1rem;
                transition: transform 0.2s ease;
            }
            
            .metric-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }
            
            .metric-value {
                font-size: 2.5rem;
                font-weight: 700;
                color: #2a5298;
                margin: 0;
            }
            
            .metric-label {
                font-size: 0.9rem;
                color: #666;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-top: 0.5rem;
            }
            
            .metric-change {
                font-size: 0.8rem;
                margin-top: 0.25rem;
            }
            
            .metric-up {
                color: #28a745;
            }
            
            .metric-down {
                color: #dc3545;
            }
            
            /* Status Indicators */
            .status-healthy {
                color: #28a745;
                font-weight: 600;
            }
            
            .status-warning {
                color: #ffc107;
                font-weight: 600;
            }
            
            .status-critical {
                color: #dc3545;
                font-weight: 600;
            }
            
            /* Alert Styles */
            .alert-card {
                border-left: 4px solid #dc3545;
                background: #fff5f5;
                padding: 1rem;
                border-radius: 5px;
                margin-bottom: 0.5rem;
            }
            
            .alert-title {
                font-weight: 600;
                color: #dc3545;
                margin-bottom: 0.5rem;
            }
            
            .alert-time {
                font-size: 0.8rem;
                color: #666;
            }
            
            /* Button Styles */
            .action-button {
                background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 5px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .action-button:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            
            /* Sidebar Styles */
            .sidebar .sidebar-content {
                background: #f8f9fa;
            }
            
            /* Tab Styles */
            .stTabs [data-baseweb="tab-list"] {
                gap: 2px;
            }
            
            .stTabs [data-baseweb="tab"] {
                height: 50px;
                padding-left: 20px;
                padding-right: 20px;
                background-color: #f1f3f4;
                border-radius: 5px 5px 0 0;
                color: #5f6368;
                font-weight: 500;
            }
            
            .stTabs [aria-selected="true"] {
                background-color: #2a5298;
                color: white;
            }
            
            /* Environment Badge */
            .env-badge {
                display: inline-block;
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            
            .env-production {
                background: #dc3545;
                color: white;
            }
            
            .env-staging {
                background: #ffc107;
                color: #212529;
            }
            
            .env-development {
                background: #28a745;
                color: white;
            }
            
            /* Loading Spinner */
            .loading-spinner {
                border: 3px solid #f3f3f3;
                border-top: 3px solid #2a5298;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin: 0 auto;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* Responsive Design */
            @media (max-width: 768px) {
                .main-title {
                    font-size: 2rem;
                }
                
                .metric-value {
                    font-size: 2rem;
                }
            }
        </style>
        """, unsafe_allow_html=True)
    
    def render_header(self):
        """Render the professional header"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        
        st.markdown(f"""
        <div class="main-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h1 class="main-title" style="color: white !important;">🚀 DevOps Assistant</h1>
                    <p class="main-subtitle" style="color: white !important;">AI-Powered Infrastructure Management & Monitoring Platform</p>
                </div>
                <div style="text-align: right; color: white;">
                    <div style="font-size: 0.9rem; opacity: 0.9; color: white;">{current_time}</div>
                    <div style="margin-top: 0.5rem;">
                        <span class="env-badge env-{st.session_state.selected_environment.lower()}">
                            {st.session_state.selected_environment}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render the professional sidebar with navigation and controls"""
        with st.sidebar:
            st.markdown("### 🎛️ Control Center")
            
            # Environment Selection
            st.session_state.selected_environment = st.selectbox(
                "🌍 Environment",
                ["Production", "Staging", "Development", "Testing"],
                index=0
            )
            
            # User Role
            st.session_state.user_role = st.selectbox(
                "👤 Role",
                ["DevOps Engineer", "Site Reliability Engineer", "Platform Engineer", 
                 "Infrastructure Engineer", "System Administrator", "Team Lead"],
                index=0
            )
            
            st.markdown("---")
            
            # Quick Actions
            st.markdown("### ⚡ Quick Actions")
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 Refresh", use_container_width=True):
                    st.rerun()
            with col2:
                if st.button("🚨 Alerts", use_container_width=True):
                    st.session_state.show_alerts = True
            
            if st.button("📊 Generate Report", use_container_width=True):
                self.generate_system_report()
            
            if st.button("🔧 Run Diagnostics", use_container_width=True):
                self.run_system_diagnostics()
            
            st.markdown("---")
            
            # System Status
            st.markdown("### 📡 System Status")
            
            # Mock system metrics
            cpu_usage = np.random.uniform(20, 80)
            memory_usage = np.random.uniform(30, 90)
            disk_usage = np.random.uniform(40, 85)
            
            # CPU Status
            cpu_color = "🟢" if cpu_usage < 70 else "🟡" if cpu_usage < 85 else "🔴"
            st.markdown(f"{cpu_color} **CPU Usage:** {cpu_usage:.1f}%")
            st.progress(cpu_usage / 100)
            
            # Memory Status
            mem_color = "🟢" if memory_usage < 80 else "🟡" if memory_usage < 90 else "🔴"
            st.markdown(f"{mem_color} **Memory Usage:** {memory_usage:.1f}%")
            st.progress(memory_usage / 100)
            
            # Disk Status
            disk_color = "🟢" if disk_usage < 80 else "🟡" if disk_usage < 90 else "🔴"
            st.markdown(f"{disk_color} **Disk Usage:** {disk_usage:.1f}%")
            st.progress(disk_usage / 100)
            
            st.markdown("---")
            
            # Recent Activity
            st.markdown("### 📝 Recent Activity")
            
            activities = [
                "✅ Deployment completed",
                "🔍 Log analysis finished",
                "⚠️ Alert resolved",
                "🔄 Service restarted",
                "📊 Report generated"
            ]
            
            for activity in activities[-3:]:
                st.markdown(f"• {activity}")
                st.caption(f"{np.random.randint(1, 60)} minutes ago")
            
            st.markdown("---")
            
            # Settings
            st.markdown("### ⚙️ Settings")
            
            auto_refresh = st.checkbox("🔄 Auto-refresh", value=True)
            show_notifications = st.checkbox("🔔 Notifications", value=True)
            dark_mode = st.checkbox("🌙 Dark Mode", value=False)
            
            if auto_refresh:
                st.markdown("*Refreshing every 30 seconds*")
    
    def generate_system_report(self):
        """Generate a comprehensive system report"""
        with st.spinner("Generating system report..."):
            time.sleep(2)  # Simulate processing
            st.success("📊 System report generated successfully!")
            st.info("Report saved to: `/reports/system_report_${datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf`")
    
    def run_system_diagnostics(self):
        """Run system diagnostics"""
        with st.spinner("Running system diagnostics..."):
            time.sleep(3)  # Simulate processing
            st.success("🔧 System diagnostics completed!")
            st.info("All systems operational. No issues detected.")
    
    def render_main_dashboard(self):
        """Render the main dashboard with key metrics"""
        st.markdown("## 📊 System Overview")
        
        # Key Metrics Row
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            self.render_metric_card("🖥️ Services", "47", "+2", "up", "Active services")
        
        with col2:
            self.render_metric_card("⚠️ Alerts", "3", "-1", "down", "Active alerts")
        
        with col3:
            self.render_metric_card("📈 Uptime", "99.9%", "+0.1%", "up", "Last 30 days")
        
        with col4:
            self.render_metric_card("🚀 Deployments", "12", "+4", "up", "This week")
        
        with col5:
            self.render_metric_card("🔍 Incidents", "1", "-2", "down", "Open incidents")
        
        # Charts Row
        col1, col2 = st.columns(2)
        
        with col1:
            self.render_performance_chart()
        
        with col2:
            self.render_service_health_chart()
        
        # Recent Events
        st.markdown("## 📋 Recent Events")
        self.render_recent_events()
    
    def render_metric_card(self, icon_title: str, value: str, change: str, 
                          direction: str, subtitle: str):
        """Render a professional metric card"""
        change_class = "metric-up" if direction == "up" else "metric-down"
        change_icon = "↗️" if direction == "up" else "↘️"
        
        st.markdown(f"""
        <div class="metric-card">
            <div class="metric-value">{value}</div>
            <div class="metric-label">{icon_title}</div>
            <div class="metric-change {change_class}">
                {change_icon} {change} {subtitle}
            </div>
        </div>
        """, unsafe_allow_html=True)
    
    def render_performance_chart(self):
        """Render system performance chart"""
        st.markdown("### 📈 System Performance (24h)")
        
        # Generate sample data
        hours = pd.date_range(start=datetime.now() - timedelta(hours=24), 
                             end=datetime.now(), freq='H')
        
        cpu_data = np.random.uniform(20, 80, len(hours))
        memory_data = np.random.uniform(30, 90, len(hours))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=hours, y=cpu_data,
            mode='lines+markers',
            name='CPU Usage (%)',
            line=dict(color='#2a5298', width=2),
            marker=dict(size=4)
        ))
        
        fig.add_trace(go.Scatter(
            x=hours, y=memory_data,
            mode='lines+markers',
            name='Memory Usage (%)',
            line=dict(color='#28a745', width=2),
            marker=dict(size=4)
        ))
        
        fig.update_layout(
            height=300,
            margin=dict(l=0, r=0, t=30, b=0),
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            xaxis_title="Time",
            yaxis_title="Usage (%)"
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def render_service_health_chart(self):
        """Render service health distribution chart"""
        st.markdown("### 🏥 Service Health Status")
        
        # Sample service health data
        health_data = {
            'Healthy': 42,
            'Warning': 3,
            'Critical': 2
        }
        
        colors = ['#28a745', '#ffc107', '#dc3545']
        
        fig = go.Figure(data=[go.Pie(
            labels=list(health_data.keys()),
            values=list(health_data.values()),
            hole=0.4,
            marker_colors=colors
        )])
        
        fig.update_layout(
            height=300,
            margin=dict(l=0, r=0, t=30, b=0),
            showlegend=True,
            legend=dict(orientation="h", yanchor="bottom", y=-0.1, xanchor="center", x=0.5)
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def render_recent_events(self):
        """Render recent events timeline"""
        events_data = [
            {
                'time': '2 minutes ago',
                'event': 'Deployment completed successfully',
                'service': 'user-service',
                'status': 'success',
                'icon': '✅'
            },
            {
                'time': '15 minutes ago',
                'event': 'High memory usage detected',
                'service': 'payment-service',
                'status': 'warning',
                'icon': '⚠️'
            },
            {
                'time': '1 hour ago',
                'event': 'Service restart completed',
                'service': 'notification-service',
                'status': 'info',
                'icon': '🔄'
            },
            {
                'time': '2 hours ago',
                'event': 'Alert resolved automatically',
                'service': 'database-cluster',
                'status': 'success',
                'icon': '🔧'
            }
        ]
        
        for event in events_data:
            col1, col2, col3, col4 = st.columns([1, 6, 2, 1])
            
            with col1:
                st.markdown(f"<div style='font-size: 1.5rem;'>{event['icon']}</div>", 
                           unsafe_allow_html=True)
            
            with col2:
                st.markdown(f"**{event['event']}**")
                st.caption(f"Service: {event['service']}")
            
            with col3:
                st.caption(event['time'])
            
            with col4:
                if event['status'] == 'success':
                    st.success("✓")
                elif event['status'] == 'warning':
                    st.warning("!")
                else:
                    st.info("i")
            
            st.markdown("---")

    def render_infrastructure_monitoring(self):
        """Render infrastructure monitoring dashboard"""
        st.markdown("## 🏗️ Infrastructure Monitoring")

        # Server Status Grid
        st.markdown("### 🖥️ Server Status")

        servers = [
            {'name': 'web-server-01', 'status': 'healthy', 'cpu': 45, 'memory': 67, 'uptime': '15d 4h'},
            {'name': 'web-server-02', 'status': 'healthy', 'cpu': 52, 'memory': 71, 'uptime': '15d 4h'},
            {'name': 'api-server-01', 'status': 'warning', 'cpu': 78, 'memory': 85, 'uptime': '12d 2h'},
            {'name': 'db-server-01', 'status': 'healthy', 'cpu': 34, 'memory': 56, 'uptime': '30d 1h'},
            {'name': 'cache-server-01', 'status': 'critical', 'cpu': 92, 'memory': 95, 'uptime': '2d 6h'},
            {'name': 'worker-server-01', 'status': 'healthy', 'cpu': 41, 'memory': 63, 'uptime': '8d 12h'}
        ]

        cols = st.columns(3)
        for i, server in enumerate(servers):
            with cols[i % 3]:
                self.render_server_card(server)

        # Network Topology
        st.markdown("### 🌐 Network Topology")
        self.render_network_topology()

        # Resource Utilization
        col1, col2 = st.columns(2)

        with col1:
            self.render_resource_utilization()

        with col2:
            self.render_storage_overview()

    def render_server_card(self, server: Dict):
        """Render individual server status card"""
        status_colors = {
            'healthy': '#28a745',
            'warning': '#ffc107',
            'critical': '#dc3545'
        }

        status_icons = {
            'healthy': '🟢',
            'warning': '🟡',
            'critical': '🔴'
        }

        color = status_colors[server['status']]
        icon = status_icons[server['status']]

        st.markdown(f"""
        <div style="border: 2px solid {color}; border-radius: 10px; padding: 1rem; margin-bottom: 1rem; background: white;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                <h4 style="margin: 0; color: #333;">{server['name']}</h4>
                <span style="font-size: 1.2rem;">{icon}</span>
            </div>
            <div style="font-size: 0.9rem; color: #666;">
                <div>CPU: {server['cpu']}% | Memory: {server['memory']}%</div>
                <div>Uptime: {server['uptime']}</div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button(f"Manage {server['name']}", key=f"manage_{server['name']}"):
            st.info(f"Opening management console for {server['name']}...")

    def render_network_topology(self):
        """Render network topology visualization"""
        # Create a simple network diagram using plotly
        import plotly.graph_objects as go
        import numpy as np

        # Define nodes
        nodes = [
            {'name': 'Load Balancer', 'x': 0.5, 'y': 0.9, 'color': '#2a5298'},
            {'name': 'Web Server 1', 'x': 0.2, 'y': 0.7, 'color': '#28a745'},
            {'name': 'Web Server 2', 'x': 0.8, 'y': 0.7, 'color': '#28a745'},
            {'name': 'API Gateway', 'x': 0.5, 'y': 0.5, 'color': '#2a5298'},
            {'name': 'Database', 'x': 0.3, 'y': 0.3, 'color': '#28a745'},
            {'name': 'Cache', 'x': 0.7, 'y': 0.3, 'color': '#dc3545'},
            {'name': 'Message Queue', 'x': 0.5, 'y': 0.1, 'color': '#ffc107'}
        ]

        # Define connections
        connections = [
            (0, 1), (0, 2),  # Load balancer to web servers
            (1, 3), (2, 3),  # Web servers to API gateway
            (3, 4), (3, 5),  # API gateway to database and cache
            (3, 6)           # API gateway to message queue
        ]

        fig = go.Figure()

        # Add connections
        for start, end in connections:
            fig.add_trace(go.Scatter(
                x=[nodes[start]['x'], nodes[end]['x']],
                y=[nodes[start]['y'], nodes[end]['y']],
                mode='lines',
                line=dict(color='#ccc', width=2),
                showlegend=False,
                hoverinfo='none'
            ))

        # Add nodes
        for node in nodes:
            fig.add_trace(go.Scatter(
                x=[node['x']],
                y=[node['y']],
                mode='markers+text',
                marker=dict(size=20, color=node['color']),
                text=node['name'],
                textposition='bottom center',
                showlegend=False,
                hovertemplate=f"<b>{node['name']}</b><br>Status: Online<extra></extra>"
            ))

        fig.update_layout(
            height=400,
            showlegend=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            margin=dict(l=0, r=0, t=30, b=0),
            title="Network Topology"
        )

        st.plotly_chart(fig, use_container_width=True)

    def render_resource_utilization(self):
        """Render resource utilization charts"""
        st.markdown("### 📊 Resource Utilization")

        # Generate sample data for the last 24 hours
        hours = list(range(24))
        cpu_data = [np.random.uniform(20, 80) for _ in hours]
        memory_data = [np.random.uniform(30, 90) for _ in hours]
        network_data = [np.random.uniform(10, 60) for _ in hours]

        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('CPU Usage (%)', 'Memory Usage (%)', 'Network I/O (MB/s)'),
            vertical_spacing=0.1
        )

        fig.add_trace(
            go.Scatter(x=hours, y=cpu_data, name='CPU', line=dict(color='#2a5298')),
            row=1, col=1
        )

        fig.add_trace(
            go.Scatter(x=hours, y=memory_data, name='Memory', line=dict(color='#28a745')),
            row=2, col=1
        )

        fig.add_trace(
            go.Scatter(x=hours, y=network_data, name='Network', line=dict(color='#ffc107')),
            row=3, col=1
        )

        fig.update_layout(height=500, showlegend=False)
        fig.update_xaxes(title_text="Hours Ago", row=3, col=1)

        st.plotly_chart(fig, use_container_width=True)

    def render_storage_overview(self):
        """Render storage overview"""
        st.markdown("### 💾 Storage Overview")

        storage_data = [
            {'name': 'Database Storage', 'used': 750, 'total': 1000, 'type': 'SSD'},
            {'name': 'Log Storage', 'used': 450, 'total': 500, 'type': 'HDD'},
            {'name': 'Backup Storage', 'used': 1200, 'total': 2000, 'type': 'Cloud'},
            {'name': 'Cache Storage', 'used': 80, 'total': 100, 'type': 'Memory'}
        ]

        for storage in storage_data:
            usage_percent = (storage['used'] / storage['total']) * 100

            # Color based on usage
            if usage_percent < 70:
                color = '#28a745'
            elif usage_percent < 85:
                color = '#ffc107'
            else:
                color = '#dc3545'

            st.markdown(f"""
            <div style="margin-bottom: 1rem;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.25rem;">
                    <span><strong>{storage['name']}</strong> ({storage['type']})</span>
                    <span>{storage['used']}GB / {storage['total']}GB</span>
                </div>
            </div>
            """, unsafe_allow_html=True)

            st.progress(usage_percent / 100)

    def render_deployment_pipeline(self):
        """Render comprehensive CI/CD pipeline dashboard"""
        self.cicd_dashboard.render_cicd_dashboard()

    def render_incident_management(self):
        """Render incident management dashboard"""
        st.markdown("## 🚨 Incident Management")

        # Incident Metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            self.render_metric_card("🔥 Open", "2", "-1", "down", "Incidents")

        with col2:
            self.render_metric_card("⏱️ MTTR", "45m", "-15m", "down", "Mean time to resolve")

        with col3:
            self.render_metric_card("📊 This Month", "8", "+2", "up", "Total incidents")

        with col4:
            self.render_metric_card("🎯 SLA", "99.2%", "+0.3%", "up", "Compliance")

        # Active Incidents
        st.markdown("### 🔥 Active Incidents")

        incidents = [
            {
                'id': 'INC-2024-001',
                'title': 'High CPU usage on payment service',
                'severity': 'High',
                'status': 'Investigating',
                'assignee': 'John Doe',
                'created': '2 hours ago',
                'affected_services': ['payment-service', 'user-service']
            },
            {
                'id': 'INC-2024-002',
                'title': 'Database connection timeouts',
                'severity': 'Critical',
                'status': 'In Progress',
                'assignee': 'Jane Smith',
                'created': '30 minutes ago',
                'affected_services': ['database-cluster']
            }
        ]

        for incident in incidents:
            severity_colors = {
                'Critical': '#dc3545',
                'High': '#fd7e14',
                'Medium': '#ffc107',
                'Low': '#28a745'
            }

            with st.expander(f"🚨 {incident['id']}: {incident['title']}"):
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown(f"**Severity:** <span style='color: {severity_colors[incident['severity']]}'>{incident['severity']}</span>", unsafe_allow_html=True)
                    st.markdown(f"**Status:** {incident['status']}")
                    st.markdown(f"**Assignee:** {incident['assignee']}")

                with col2:
                    st.markdown(f"**Created:** {incident['created']}")
                    st.markdown(f"**Affected Services:** {', '.join(incident['affected_services'])}")

                # Action buttons
                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button("📝 Update", key=f"update_{incident['id']}"):
                        st.info("Opening incident update form...")
                with col2:
                    if st.button("✅ Resolve", key=f"resolve_{incident['id']}"):
                        st.success("Incident marked as resolved!")
                with col3:
                    if st.button("👥 Escalate", key=f"escalate_{incident['id']}"):
                        st.warning("Incident escalated to senior team!")

        # Create New Incident
        st.markdown("### ➕ Create New Incident")

        with st.form("new_incident"):
            col1, col2 = st.columns(2)

            with col1:
                title = st.text_input("Incident Title")
                severity = st.selectbox("Severity", ["Low", "Medium", "High", "Critical"])

            with col2:
                assignee = st.selectbox("Assignee", ["John Doe", "Jane Smith", "Mike Wilson", "Sarah Johnson"])
                affected_services = st.multiselect("Affected Services",
                                                 ["user-service", "payment-service", "notification-service", "database-cluster"])

            description = st.text_area("Description")

            if st.form_submit_button("🚨 Create Incident"):
                st.success(f"✅ Incident created: {title}")
                st.info("Incident ID: INC-2024-003")
