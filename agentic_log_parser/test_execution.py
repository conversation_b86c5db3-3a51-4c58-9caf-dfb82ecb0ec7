#!/usr/bin/env python3
"""
Test Execution and Monitoring Module
Comprehensive test management, execution, and result monitoring
"""

import streamlit as st
import subprocess
import json
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import numpy as np
import os
import time

class TestExecutionManager:
    """Test execution and monitoring management"""
    
    def __init__(self):
        self.test_results_file = "test_results.json"
        self.test_history = self.load_test_history()
        
        # Initialize with sample data if empty
        if not self.test_history:
            self._create_sample_test_data()
    
    def render_test_dashboard(self):
        """Render the main test execution dashboard"""
        st.markdown("## 🧪 Test Execution & Monitoring")
        
        # Quick stats
        self.render_test_stats()
        
        # Main dashboard tabs
        tabs = st.tabs([
            "📊 Overview",
            "🧪 Test Execution",
            "📈 Results",
            "📋 Test Suites",
            "🔍 Coverage"
        ])
        
        with tabs[0]:
            self.render_overview_tab()
        
        with tabs[1]:
            self.render_execution_tab()
        
        with tabs[2]:
            self.render_results_tab()
        
        with tabs[3]:
            self.render_suites_tab()
        
        with tabs[4]:
            self.render_coverage_tab()
    
    def render_test_stats(self):
        """Render quick test statistics"""
        col1, col2, col3, col4 = st.columns(4)
        
        recent_results = self.get_recent_test_results(30)
        
        total_tests = sum(result.get('total_tests', 0) for result in recent_results)
        passed_tests = sum(result.get('passed_tests', 0) for result in recent_results)
        failed_tests = sum(result.get('failed_tests', 0) for result in recent_results)
        
        success_rate = (passed_tests / max(total_tests, 1)) * 100
        
        with col1:
            st.metric("🧪 Total Tests", total_tests)
        
        with col2:
            st.metric("✅ Success Rate", f"{success_rate:.1f}%")
        
        with col3:
            st.metric("❌ Failed Tests", failed_tests)
        
        with col4:
            running_tests = len([r for r in recent_results if r.get('status') == 'running'])
            st.metric("🔄 Running", running_tests)
    
    def render_overview_tab(self):
        """Render test overview dashboard"""
        st.markdown("### 🧪 Test Overview")
        
        # Test execution status
        col1, col2 = st.columns(2)
        
        with col1:
            self.render_test_status_chart()
        
        with col2:
            self.render_test_trend_chart()
        
        # Recent test runs
        st.markdown("### 📈 Recent Test Runs")
        
        recent_results = self.get_recent_test_results(10)
        
        if recent_results:
            for result in recent_results:
                status_icon = {
                    'passed': '✅',
                    'failed': '❌',
                    'running': '🔄',
                    'pending': '⏳'
                }.get(result.get('status', 'unknown'), '❓')
                
                status_color = {
                    'passed': 'green',
                    'failed': 'red',
                    'running': 'blue',
                    'pending': 'orange'
                }.get(result.get('status', 'unknown'), 'gray')
                
                with st.expander(f"{status_icon} {result.get('suite_name', 'Unknown')} - {result.get('timestamp', 'Unknown')}"):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.markdown(f"**Total Tests:** {result.get('total_tests', 0)}")
                        st.markdown(f"**Passed:** {result.get('passed_tests', 0)}")
                        st.markdown(f"**Failed:** {result.get('failed_tests', 0)}")
                        st.markdown(f"**Duration:** {result.get('duration', 0):.2f}s")
                        
                        if result.get('coverage'):
                            st.markdown(f"**Coverage:** {result.get('coverage', 0):.1f}%")
                    
                    with col2:
                        if st.button("📊 Details", key=f"details_{result.get('id')}"):
                            self._show_test_details(result)
                        
                        if result.get('status') == 'failed':
                            if st.button("🔄 Retry", key=f"retry_{result.get('id')}"):
                                self._retry_test_suite(result.get('suite_name'))
        else:
            st.info("No recent test results found")
    
    def render_execution_tab(self):
        """Render test execution tab"""
        st.markdown("### 🧪 Test Execution")
        
        # Test execution controls
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.markdown("#### 🚀 Run Tests")
        
        with col2:
            if st.button("▶️ Run All Tests", use_container_width=True):
                self._run_all_tests()
        
        with col3:
            if st.button("🔄 Refresh", use_container_width=True, key="test_refresh"):
                st.rerun()
        
        # Test suite selection
        st.markdown("#### 📋 Test Suites")
        
        test_suites = self._get_available_test_suites()
        
        for suite in test_suites:
            with st.expander(f"📁 {suite['name']} ({suite['test_count']} tests)"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Description:** {suite['description']}")
                    st.markdown(f"**Framework:** {suite['framework']}")
                    st.markdown(f"**Test Count:** {suite['test_count']}")
                    st.markdown(f"**Last Run:** {suite['last_run']}")
                
                with col2:
                    if st.button("▶️ Run Suite", key=f"run_{suite['id']}"):
                        self._run_test_suite(suite['name'])
                    
                    if st.button("⚙️ Configure", key=f"config_{suite['id']}"):
                        self._configure_test_suite(suite)
        
        # Custom test execution
        st.markdown("#### 🛠️ Custom Test Execution")
        
        with st.form("custom_test"):
            col1, col2 = st.columns(2)
            
            with col1:
                test_command = st.text_input("Test Command", placeholder="pytest tests/")
                test_path = st.text_input("Test Path", placeholder="tests/")
            
            with col2:
                test_framework = st.selectbox("Framework", ["pytest", "unittest", "nose2", "jest", "mocha"])
                parallel_execution = st.checkbox("Parallel Execution")
            
            test_args = st.text_input("Additional Arguments", placeholder="--verbose --coverage")
            
            if st.form_submit_button("🚀 Execute Tests"):
                if test_command:
                    self._execute_custom_test(test_command, test_path, test_framework, parallel_execution, test_args)
                else:
                    st.error("Please enter a test command")
    
    def render_results_tab(self):
        """Render test results tab"""
        st.markdown("### 📈 Test Results")
        
        # Results filtering
        col1, col2, col3 = st.columns(3)
        
        with col1:
            date_range = st.selectbox("Time Range", ["Last 24 hours", "Last 7 days", "Last 30 days", "All time"])
        
        with col2:
            status_filter = st.selectbox("Status", ["All", "Passed", "Failed", "Running"])
        
        with col3:
            suite_filter = st.selectbox("Test Suite", ["All"] + [suite['name'] for suite in self._get_available_test_suites()])
        
        # Test results table
        filtered_results = self._filter_test_results(date_range, status_filter, suite_filter)
        
        if filtered_results:
            results_data = []
            for result in filtered_results:
                results_data.append({
                    'Suite': result.get('suite_name', 'Unknown'),
                    'Status': result.get('status', 'Unknown'),
                    'Total': result.get('total_tests', 0),
                    'Passed': result.get('passed_tests', 0),
                    'Failed': result.get('failed_tests', 0),
                    'Duration': f"{result.get('duration', 0):.2f}s",
                    'Coverage': f"{result.get('coverage', 0):.1f}%",
                    'Timestamp': result.get('timestamp', 'Unknown')
                })
            
            df = pd.DataFrame(results_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
        else:
            st.info("No test results found for the selected filters")
        
        # Test failure analysis
        if filtered_results:
            failed_results = [r for r in filtered_results if r.get('status') == 'failed']
            
            if failed_results:
                st.markdown("### 🔍 Failure Analysis")
                
                failure_reasons = {}
                for result in failed_results:
                    for failure in result.get('failures', []):
                        reason = failure.get('type', 'Unknown')
                        failure_reasons[reason] = failure_reasons.get(reason, 0) + 1
                
                if failure_reasons:
                    fig = go.Figure(data=[go.Bar(
                        x=list(failure_reasons.keys()),
                        y=list(failure_reasons.values()),
                        marker_color='red'
                    )])
                    
                    fig.update_layout(
                        title="Test Failure Types",
                        xaxis_title="Failure Type",
                        yaxis_title="Count",
                        height=400
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
    
    def render_suites_tab(self):
        """Render test suites management tab"""
        st.markdown("### 📋 Test Suite Management")
        
        # Test suite overview
        test_suites = self._get_available_test_suites()
        
        for suite in test_suites:
            with st.expander(f"📁 {suite['name']}"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**Description:** {suite['description']}")
                    st.markdown(f"**Framework:** {suite['framework']}")
                    st.markdown(f"**Test Files:** {suite['test_count']}")
                    st.markdown(f"**Last Success Rate:** {suite.get('success_rate', 0):.1f}%")
                    st.markdown(f"**Average Duration:** {suite.get('avg_duration', 0):.2f}s")
                
                with col2:
                    if st.button("📊 Analytics", key=f"analytics_{suite['id']}"):
                        self._show_suite_analytics(suite)
                    
                    if st.button("⚙️ Edit", key=f"edit_{suite['id']}"):
                        self._edit_test_suite(suite)
                    
                    if st.button("🗑️ Delete", key=f"delete_{suite['id']}"):
                        self._delete_test_suite(suite)
        
        # Create new test suite
        st.markdown("### ➕ Create New Test Suite")
        
        with st.form("new_test_suite"):
            col1, col2 = st.columns(2)
            
            with col1:
                suite_name = st.text_input("Suite Name")
                description = st.text_area("Description")
            
            with col2:
                framework = st.selectbox("Test Framework", ["pytest", "unittest", "nose2", "jest", "mocha"])
                test_path = st.text_input("Test Path", placeholder="tests/")
            
            if st.form_submit_button("🚀 Create Suite"):
                if suite_name and test_path:
                    self._create_test_suite(suite_name, description, framework, test_path)
                    st.success(f"Test suite '{suite_name}' created successfully!")
                    st.rerun()
                else:
                    st.error("Please fill in required fields")
    
    def render_coverage_tab(self):
        """Render code coverage tab"""
        st.markdown("### 🔍 Code Coverage Analysis")
        
        # Coverage overview
        coverage_data = self._get_coverage_data()
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("📊 Overall Coverage", f"{coverage_data.get('overall', 0):.1f}%")
        
        with col2:
            st.metric("📁 Files Covered", coverage_data.get('files_covered', 0))
        
        with col3:
            st.metric("📝 Lines Covered", coverage_data.get('lines_covered', 0))
        
        with col4:
            st.metric("🎯 Target Coverage", "80%")
        
        # Coverage by module
        if coverage_data.get('modules'):
            st.markdown("### 📁 Coverage by Module")
            
            modules_data = []
            for module, data in coverage_data['modules'].items():
                modules_data.append({
                    'Module': module,
                    'Coverage': data.get('coverage', 0),
                    'Lines': data.get('lines', 0),
                    'Covered': data.get('covered', 0),
                    'Missing': data.get('missing', 0)
                })
            
            df = pd.DataFrame(modules_data)
            
            # Coverage chart
            fig = go.Figure(data=[go.Bar(
                x=df['Module'],
                y=df['Coverage'],
                marker_color=df['Coverage'].apply(lambda x: 'green' if x >= 80 else 'orange' if x >= 60 else 'red')
            )])
            
            fig.update_layout(
                title="Code Coverage by Module",
                xaxis_title="Module",
                yaxis_title="Coverage %",
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Coverage table
            st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Coverage trends
        st.markdown("### 📈 Coverage Trends")
        
        # Generate sample coverage trend data
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        coverage_trend = [75 + np.random.uniform(-5, 10) for _ in dates]
        coverage_trend = np.clip(coverage_trend, 0, 100)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=coverage_trend,
            mode='lines+markers',
            name='Coverage %',
            line=dict(color='#28a745', width=3)
        ))
        
        # Add target line
        fig.add_hline(y=80, line_dash="dash", line_color="red", annotation_text="Target: 80%")
        
        fig.update_layout(
            title="Code Coverage Trend (Last 30 Days)",
            xaxis_title="Date",
            yaxis_title="Coverage %",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def render_test_status_chart(self):
        """Render test status distribution chart"""
        recent_results = self.get_recent_test_results(10)
        
        if recent_results:
            status_counts = {}
            for result in recent_results:
                status = result.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            fig = go.Figure(data=[go.Pie(
                labels=list(status_counts.keys()),
                values=list(status_counts.values()),
                hole=0.4
            )])
            
            fig.update_layout(
                title="Test Status Distribution",
                height=300
            )
            
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No test data available")
    
    def render_test_trend_chart(self):
        """Render test success rate trend"""
        # Generate sample trend data
        dates = pd.date_range(start=datetime.now() - timedelta(days=7), end=datetime.now(), freq='D')
        success_rates = [85 + np.random.uniform(-10, 15) for _ in dates]
        success_rates = np.clip(success_rates, 0, 100)
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=success_rates,
            mode='lines+markers',
            name='Success Rate',
            line=dict(color='#007bff', width=3)
        ))
        
        fig.update_layout(
            title="Test Success Rate Trend",
            xaxis_title="Date",
            yaxis_title="Success Rate %",
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def load_test_history(self) -> List[Dict]:
        """Load test history from file"""
        try:
            if os.path.exists(self.test_results_file):
                with open(self.test_results_file, 'r') as f:
                    data = json.load(f)
                    # Ensure we return a list of dictionaries
                    if isinstance(data, list):
                        return [item for item in data if isinstance(item, dict)]
                    return []
        except Exception as e:
            st.error(f"Error loading test history: {e}")
        return []
    
    def save_test_history(self):
        """Save test history to file"""
        try:
            with open(self.test_results_file, 'w') as f:
                json.dump(self.test_history, f, indent=2, default=str)
        except Exception as e:
            st.error(f"Error saving test history: {e}")
    
    def get_recent_test_results(self, limit: int = 10) -> List[Dict]:
        """Get recent test results"""
        if not self.test_history:
            return []

        # Ensure test_history contains dictionaries
        valid_results = [result for result in self.test_history if isinstance(result, dict)]
        return sorted(valid_results, key=lambda x: x.get('timestamp', ''), reverse=True)[:limit]
    
    def _create_sample_test_data(self):
        """Create sample test data"""
        sample_results = [
            {
                'id': f'test_{i}',
                'suite_name': f'Test Suite {i % 3 + 1}',
                'status': np.random.choice(['passed', 'failed', 'running'], p=[0.7, 0.2, 0.1]),
                'total_tests': np.random.randint(10, 100),
                'passed_tests': 0,
                'failed_tests': 0,
                'duration': np.random.uniform(10, 300),
                'coverage': np.random.uniform(60, 95),
                'timestamp': (datetime.now() - timedelta(hours=i)).isoformat(),
                'failures': []
            }
            for i in range(20)
        ]
        
        # Calculate passed/failed based on status
        for result in sample_results:
            if result['status'] == 'passed':
                result['passed_tests'] = result['total_tests']
                result['failed_tests'] = 0
            elif result['status'] == 'failed':
                result['failed_tests'] = np.random.randint(1, result['total_tests'] // 2)
                result['passed_tests'] = result['total_tests'] - result['failed_tests']
                result['failures'] = [
                    {'type': 'AssertionError', 'message': 'Test assertion failed'},
                    {'type': 'ValueError', 'message': 'Invalid value provided'}
                ]
        
        self.test_history = sample_results
        self.save_test_history()
    
    def _get_available_test_suites(self) -> List[Dict]:
        """Get available test suites"""
        return [
            {
                'id': 'suite_1',
                'name': 'Unit Tests',
                'description': 'Core unit tests for application logic',
                'framework': 'pytest',
                'test_count': 45,
                'last_run': '2 hours ago',
                'success_rate': 95.5,
                'avg_duration': 12.3
            },
            {
                'id': 'suite_2',
                'name': 'Integration Tests',
                'description': 'Integration tests for API endpoints',
                'framework': 'pytest',
                'test_count': 23,
                'last_run': '4 hours ago',
                'success_rate': 87.2,
                'avg_duration': 45.7
            },
            {
                'id': 'suite_3',
                'name': 'E2E Tests',
                'description': 'End-to-end browser tests',
                'framework': 'selenium',
                'test_count': 12,
                'last_run': '6 hours ago',
                'success_rate': 78.9,
                'avg_duration': 120.5
            }
        ]
    
    def _get_coverage_data(self) -> Dict:
        """Get code coverage data"""
        return {
            'overall': 82.5,
            'files_covered': 156,
            'lines_covered': 2847,
            'modules': {
                'auth': {'coverage': 95.2, 'lines': 234, 'covered': 223, 'missing': 11},
                'api': {'coverage': 87.8, 'lines': 456, 'covered': 400, 'missing': 56},
                'utils': {'coverage': 76.3, 'lines': 189, 'covered': 144, 'missing': 45},
                'models': {'coverage': 91.4, 'lines': 345, 'covered': 315, 'missing': 30}
            }
        }
    
    def _run_all_tests(self):
        """Run all test suites"""
        with st.spinner("Running all tests..."):
            time.sleep(3)  # Simulate test execution
            st.success("✅ All tests completed successfully!")
    
    def _run_test_suite(self, suite_name: str):
        """Run a specific test suite"""
        with st.spinner(f"Running {suite_name}..."):
            time.sleep(2)  # Simulate test execution
            st.success(f"✅ {suite_name} completed successfully!")
    
    def _execute_custom_test(self, command: str, path: str, framework: str, parallel: bool, args: str):
        """Execute custom test command"""
        with st.spinner(f"Executing: {command}"):
            time.sleep(2)  # Simulate test execution
            st.success(f"✅ Custom test execution completed!")
    
    def _filter_test_results(self, date_range: str, status: str, suite: str) -> List[Dict]:
        """Filter test results based on criteria"""
        if not self.test_history:
            return []

        # Ensure we only work with valid dictionaries
        results = [r for r in self.test_history if isinstance(r, dict)]

        # Filter by status
        if status != "All":
            results = [r for r in results if r.get('status', '').lower() == status.lower()]

        # Filter by suite
        if suite != "All":
            results = [r for r in results if r.get('suite_name') == suite]

        return results
    
    def _show_test_details(self, result: Dict):
        """Show detailed test information"""
        st.info(f"Test details for: {result.get('suite_name')}")
    
    def _retry_test_suite(self, suite_name: str):
        """Retry failed test suite"""
        st.info(f"Retrying test suite: {suite_name}")
    
    def _configure_test_suite(self, suite: Dict):
        """Configure test suite"""
        st.info(f"Configuring test suite: {suite['name']}")
    
    def _show_suite_analytics(self, suite: Dict):
        """Show test suite analytics"""
        st.info(f"Analytics for test suite: {suite['name']}")
    
    def _edit_test_suite(self, suite: Dict):
        """Edit test suite"""
        st.info(f"Editing test suite: {suite['name']}")
    
    def _delete_test_suite(self, suite: Dict):
        """Delete test suite"""
        st.warning(f"Delete test suite: {suite['name']}")
    
    def _create_test_suite(self, name: str, description: str, framework: str, path: str):
        """Create new test suite"""
        st.success(f"Created test suite: {name}")
