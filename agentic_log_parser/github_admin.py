#!/usr/bin/env python3
"""
GitHub Administration Module
Comprehensive GitHub repository and organization management
"""

import streamlit as st
import requests
import json
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px

class GitHubAdmin:
    """GitHub Administration and Management"""
    
    def __init__(self):
        self.base_url = "https://api.github.com"
        self.token = self._get_github_token()
        self.headers = {
            "Authorization": f"token {self.token}",
            "Accept": "application/vnd.github.v3+json"
        } if self.token else {}
    
    def _get_github_token(self) -> Optional[str]:
        """Get GitHub token from session state or environment"""
        if 'github_token' in st.session_state:
            return st.session_state.github_token
        return None
    
    def render_github_admin_dashboard(self):
        """Render the main GitHub administration dashboard"""
        st.markdown("## 🐙 GitHub Administration")
        
        # GitHub Authentication
        if not self.token:
            self._render_authentication()
            return
        
        # Main dashboard tabs
        tabs = st.tabs([
            "📊 Overview",
            "📁 Repositories",
            "👥 Organizations",
            "🔧 Settings",
            "📈 Analytics"
        ])
        
        with tabs[0]:
            self.render_overview_tab()
        
        with tabs[1]:
            self.render_repositories_tab()
        
        with tabs[2]:
            self.render_organizations_tab()
        
        with tabs[3]:
            self.render_settings_tab()
        
        with tabs[4]:
            self.render_analytics_tab()
    
    def _render_authentication(self):
        """Render GitHub authentication form"""
        st.markdown("### 🔐 GitHub Authentication")
        
        st.info("Please provide your GitHub Personal Access Token to access GitHub administration features.")
        
        with st.form("github_auth"):
            token = st.text_input(
                "GitHub Personal Access Token",
                type="password",
                help="Generate a token at https://github.com/settings/tokens"
            )
            
            st.markdown("**Required Permissions:**")
            st.markdown("- `repo` - Full repository access")
            st.markdown("- `admin:org` - Organization administration")
            st.markdown("- `user` - User information access")
            st.markdown("- `workflow` - GitHub Actions workflow access")
            
            if st.form_submit_button("🔗 Connect to GitHub"):
                if token:
                    # Validate token
                    if self._validate_token(token):
                        st.session_state.github_token = token
                        self.token = token
                        self.headers = {
                            "Authorization": f"token {self.token}",
                            "Accept": "application/vnd.github.v3+json"
                        }
                        st.success("✅ Successfully connected to GitHub!")
                        st.rerun()
                    else:
                        st.error("❌ Invalid GitHub token. Please check your token and permissions.")
                else:
                    st.error("Please enter a GitHub token")
    
    def _validate_token(self, token: str) -> bool:
        """Validate GitHub token"""
        try:
            headers = {
                "Authorization": f"token {token}",
                "Accept": "application/vnd.github.v3+json"
            }
            response = requests.get(f"{self.base_url}/user", headers=headers)
            return response.status_code == 200
        except:
            return False
    
    def render_overview_tab(self):
        """Render GitHub overview dashboard"""
        st.markdown("### 📊 GitHub Overview")
        
        # User information
        user_info = self._get_user_info()
        if user_info:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("👤 User", user_info.get('login', 'N/A'))
            
            with col2:
                st.metric("📁 Public Repos", user_info.get('public_repos', 0))
            
            with col3:
                st.metric("👥 Followers", user_info.get('followers', 0))
            
            with col4:
                st.metric("👤 Following", user_info.get('following', 0))
        
        # Recent activity
        st.markdown("### 📈 Recent Activity")
        
        col1, col2 = st.columns(2)
        
        with col1:
            self._render_recent_repositories()
        
        with col2:
            self._render_recent_commits()
    
    def render_repositories_tab(self):
        """Render repositories management tab"""
        st.markdown("### 📁 Repository Management")
        
        # Repository controls
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            search_query = st.text_input("🔍 Search Repositories", placeholder="Enter repository name...")
        
        with col2:
            repo_type = st.selectbox("Type", ["All", "Public", "Private", "Forks"])
        
        with col3:
            sort_by = st.selectbox("Sort by", ["Updated", "Created", "Name", "Stars"])
        
        # Repository list
        repositories = self._get_repositories(search_query, repo_type, sort_by)
        
        if repositories:
            st.markdown("### 📋 Repositories")
            
            for repo in repositories[:10]:  # Show first 10
                with st.expander(f"📁 {repo['name']} - {repo.get('language', 'Unknown')}"):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.markdown(f"**Description:** {repo.get('description', 'No description')}")
                        st.markdown(f"**Language:** {repo.get('language', 'Unknown')}")
                        st.markdown(f"**Stars:** ⭐ {repo.get('stargazers_count', 0)}")
                        st.markdown(f"**Forks:** 🍴 {repo.get('forks_count', 0)}")
                        st.markdown(f"**Updated:** {repo.get('updated_at', 'Unknown')}")
                        
                        if repo.get('private'):
                            st.markdown("🔒 **Private Repository**")
                        else:
                            st.markdown("🌍 **Public Repository**")
                    
                    with col2:
                        if st.button("🔗 Open", key=f"open_{repo['id']}"):
                            st.markdown(f"[Open Repository]({repo['html_url']})")
                        
                        if st.button("⚙️ Settings", key=f"settings_{repo['id']}"):
                            self._show_repo_settings(repo)
                        
                        if st.button("📊 Analytics", key=f"analytics_{repo['id']}"):
                            self._show_repo_analytics(repo)
        else:
            st.info("No repositories found or unable to fetch repositories.")
        
        # Create new repository
        st.markdown("### ➕ Create New Repository")
        
        with st.form("create_repo"):
            col1, col2 = st.columns(2)
            
            with col1:
                repo_name = st.text_input("Repository Name")
                description = st.text_area("Description")
                
            with col2:
                is_private = st.checkbox("Private Repository")
                init_readme = st.checkbox("Initialize with README", value=True)
                gitignore_template = st.selectbox(
                    "Gitignore Template",
                    ["None", "Python", "Node", "Java", "Go", "Rust", "C++"]
                )
            
            if st.form_submit_button("🚀 Create Repository"):
                if repo_name:
                    success = self._create_repository(repo_name, description, is_private, init_readme, gitignore_template)
                    if success:
                        st.success(f"✅ Repository '{repo_name}' created successfully!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to create repository")
                else:
                    st.error("Please enter a repository name")
    
    def render_organizations_tab(self):
        """Render organizations management tab"""
        st.markdown("### 👥 Organization Management")
        
        # Get user organizations
        organizations = self._get_organizations()
        
        if organizations:
            for org in organizations:
                with st.expander(f"🏢 {org['login']}"):
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        st.markdown(f"**Description:** {org.get('description', 'No description')}")
                        st.markdown(f"**Public Repos:** {org.get('public_repos', 0)}")
                        st.markdown(f"**Location:** {org.get('location', 'Not specified')}")
                        st.markdown(f"**Created:** {org.get('created_at', 'Unknown')}")
                    
                    with col2:
                        if st.button("👥 Members", key=f"members_{org['id']}"):
                            self._show_org_members(org)
                        
                        if st.button("📁 Repositories", key=f"org_repos_{org['id']}"):
                            self._show_org_repositories(org)
                        
                        if st.button("⚙️ Settings", key=f"org_settings_{org['id']}"):
                            self._show_org_settings(org)
        else:
            st.info("No organizations found or you don't have access to any organizations.")
    
    def render_settings_tab(self):
        """Render GitHub settings and configuration"""
        st.markdown("### ⚙️ GitHub Settings")
        
        # Webhook management
        st.markdown("#### 🔗 Webhook Management")
        
        with st.expander("Configure Webhooks"):
            webhook_url = st.text_input("Webhook URL", placeholder="https://your-domain.com/webhook")
            
            events = st.multiselect(
                "Events",
                ["push", "pull_request", "issues", "release", "deployment"],
                default=["push", "pull_request"]
            )
            
            if st.button("➕ Add Webhook"):
                if webhook_url:
                    st.success(f"Webhook configured for: {', '.join(events)}")
                else:
                    st.error("Please enter a webhook URL")
        
        # Branch protection
        st.markdown("#### 🛡️ Branch Protection")
        
        with st.expander("Branch Protection Rules"):
            branch_name = st.text_input("Branch Name", value="main")
            
            col1, col2 = st.columns(2)
            
            with col1:
                require_reviews = st.checkbox("Require Pull Request Reviews", value=True)
                dismiss_stale_reviews = st.checkbox("Dismiss Stale Reviews")
                require_code_owner_reviews = st.checkbox("Require Code Owner Reviews")
            
            with col2:
                require_status_checks = st.checkbox("Require Status Checks", value=True)
                require_branches_up_to_date = st.checkbox("Require Branches Up to Date")
                restrict_pushes = st.checkbox("Restrict Pushes")
            
            if st.button("🛡️ Apply Protection Rules"):
                st.success(f"Branch protection rules applied to '{branch_name}'")
    
    def render_analytics_tab(self):
        """Render GitHub analytics and insights"""
        st.markdown("### 📈 GitHub Analytics")
        
        # Repository statistics
        repositories = self._get_repositories()
        
        if repositories:
            # Language distribution
            languages = {}
            for repo in repositories:
                lang = repo.get('language', 'Unknown')
                languages[lang] = languages.get(lang, 0) + 1
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Language pie chart
                if languages:
                    fig = go.Figure(data=[go.Pie(
                        labels=list(languages.keys()),
                        values=list(languages.values()),
                        hole=0.4
                    )])
                    
                    fig.update_layout(
                        title="Repository Languages",
                        height=400
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # Repository activity
                repo_data = []
                for repo in repositories[:10]:
                    repo_data.append({
                        'Repository': repo['name'],
                        'Stars': repo.get('stargazers_count', 0),
                        'Forks': repo.get('forks_count', 0),
                        'Issues': repo.get('open_issues_count', 0)
                    })
                
                if repo_data:
                    df = pd.DataFrame(repo_data)
                    
                    fig = go.Figure()
                    fig.add_trace(go.Bar(
                        name='Stars',
                        x=df['Repository'],
                        y=df['Stars'],
                        marker_color='gold'
                    ))
                    fig.add_trace(go.Bar(
                        name='Forks',
                        x=df['Repository'],
                        y=df['Forks'],
                        marker_color='lightblue'
                    ))
                    
                    fig.update_layout(
                        title="Repository Metrics",
                        xaxis_title="Repository",
                        yaxis_title="Count",
                        height=400,
                        barmode='group'
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
        
        # Commit activity
        st.markdown("#### 📊 Commit Activity")
        
        # Generate sample commit data
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        commits = [np.random.randint(0, 20) for _ in dates]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=commits,
            mode='lines+markers',
            name='Daily Commits',
            line=dict(color='#28a745', width=3)
        ))
        
        fig.update_layout(
            title="Commit Activity (Last 30 Days)",
            xaxis_title="Date",
            yaxis_title="Commits",
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _get_user_info(self) -> Optional[Dict]:
        """Get GitHub user information"""
        try:
            response = requests.get(f"{self.base_url}/user", headers=self.headers)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return None
    
    def _get_repositories(self, search: str = "", repo_type: str = "All", sort: str = "Updated") -> List[Dict]:
        """Get user repositories"""
        try:
            params = {
                'sort': sort.lower(),
                'per_page': 50
            }
            
            if repo_type != "All":
                params['type'] = repo_type.lower()
            
            response = requests.get(f"{self.base_url}/user/repos", headers=self.headers, params=params)
            
            if response.status_code == 200:
                repos = response.json()
                
                if search:
                    repos = [repo for repo in repos if search.lower() in repo['name'].lower()]
                
                return repos
        except:
            pass
        return []
    
    def _get_organizations(self) -> List[Dict]:
        """Get user organizations"""
        try:
            response = requests.get(f"{self.base_url}/user/orgs", headers=self.headers)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return []
    
    def _create_repository(self, name: str, description: str, private: bool, init_readme: bool, gitignore: str) -> bool:
        """Create a new repository"""
        try:
            data = {
                'name': name,
                'description': description,
                'private': private,
                'auto_init': init_readme
            }
            
            if gitignore != "None":
                data['gitignore_template'] = gitignore
            
            response = requests.post(f"{self.base_url}/user/repos", headers=self.headers, json=data)
            return response.status_code == 201
        except:
            return False
    
    def _render_recent_repositories(self):
        """Render recent repositories"""
        st.markdown("#### 📁 Recent Repositories")
        
        repositories = self._get_repositories(sort="Updated")[:5]
        
        for repo in repositories:
            st.markdown(f"• **{repo['name']}** - {repo.get('language', 'Unknown')}")
            st.caption(f"Updated: {repo.get('updated_at', 'Unknown')}")
    
    def _render_recent_commits(self):
        """Render recent commits (mock data)"""
        st.markdown("#### 📝 Recent Commits")
        
        # Mock recent commits
        commits = [
            {"message": "Fix authentication bug", "author": "john.doe", "time": "2 hours ago"},
            {"message": "Add new feature", "author": "jane.smith", "time": "4 hours ago"},
            {"message": "Update documentation", "author": "mike.wilson", "time": "6 hours ago"},
            {"message": "Refactor code", "author": "sarah.johnson", "time": "8 hours ago"},
            {"message": "Fix tests", "author": "alex.brown", "time": "10 hours ago"}
        ]
        
        for commit in commits:
            st.markdown(f"• **{commit['message']}**")
            st.caption(f"by {commit['author']} - {commit['time']}")
    
    def _show_repo_settings(self, repo: Dict):
        """Show repository settings"""
        st.info(f"Repository settings for: {repo['name']}")
    
    def _show_repo_analytics(self, repo: Dict):
        """Show repository analytics"""
        st.info(f"Repository analytics for: {repo['name']}")
    
    def _show_org_members(self, org: Dict):
        """Show organization members"""
        st.info(f"Organization members for: {org['login']}")
    
    def _show_org_repositories(self, org: Dict):
        """Show organization repositories"""
        st.info(f"Organization repositories for: {org['login']}")
    
    def _show_org_settings(self, org: Dict):
        """Show organization settings"""
        st.info(f"Organization settings for: {org['login']}")

# Import numpy for analytics
import numpy as np
