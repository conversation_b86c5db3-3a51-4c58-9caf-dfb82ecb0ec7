import streamlit as st
from agent import AgenticLogParser
from collections import Counter, defaultdict
import json
import os
import re
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from sklearn.ensemble import IsolationForest
import pandas as pd
from dotenv import load_dotenv
load_dotenv()


st.set_page_config(page_title="Agentic AI Log Analyzer", layout="wide")
st.title("🤖 Agentic AI Demo")


MEMORY_FILE = "log_memory.json"
INCIDENT_KB_FILE = "incident_kb.json"
ACTION_HISTORY_FILE = "action_history.json"

# Helper to parse timestamps - example regex for ISO-like timestamps
timestamp_regex = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})')

def extract_timestamps(log_lines):
    timestamps = []
    for line in log_lines:
        match = timestamp_regex.search(line)
        if match:
            try:
                dt = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
                timestamps.append((line, dt))
            except Exception:
                pass
    return timestamps

def update_memory(errors, warnings):
    memory = {"errors": {}, "warnings": {}}
    if os.path.exists(MEMORY_FILE):
        with open(MEMORY_FILE, "r") as f:
            memory = json.load(f)
    for err in errors:
        memory["errors"][err] = memory["errors"].get(err, 0) + 1
    for warn in warnings:
        memory["warnings"][warn] = memory["warnings"].get(warn, 0) + 1
    with open(MEMORY_FILE, "w") as f:
        json.dump(memory, f, indent=2)

def show_memory_summary():
    if not os.path.exists(MEMORY_FILE):
        st.info("No memory file found.")
        return
    with open(MEMORY_FILE, "r") as f:
        memory = json.load(f)
    st.markdown("**Top Frequent Errors:**")
    for err, count in Counter(memory["errors"]).most_common(5):
        st.markdown(f"- `{err}` - {count} times")
    st.markdown("**Top Frequent Warnings:**")
    for warn, count in Counter(memory["warnings"]).most_common(5):
        st.markdown(f"- `{warn}` - {count} times")

def prepare_download_data(result):
    export = {
        "errors": result["errors"],
        "warnings": result["warnings"],
        "suggestions": result["suggestions"]
    }
    return json.dumps(export, indent=2)

def load_incident_kb():
    if os.path.exists(INCIDENT_KB_FILE):
        with open(INCIDENT_KB_FILE) as f:
            return json.load(f)
    return {}

def load_action_history():
    if os.path.exists(ACTION_HISTORY_FILE):
        with open(ACTION_HISTORY_FILE) as f:
            return json.load(f)
    return []

def save_action_history(history):
    with open(ACTION_HISTORY_FILE, "w") as f:
        json.dump(history, f, indent=2)

def detect_anomalies(log_lines):
    # Simple numeric features: length of lines, count of error words, etc.
    features = []
    for line in log_lines:
        length = len(line)
        error_count = line.lower().count("error")
        warning_count = line.lower().count("warning")
        features.append([length, error_count, warning_count])
    clf = IsolationForest(contamination=0.05, random_state=42)
    preds = clf.fit_predict(features)
    anomalies = [log_lines[i] for i, p in enumerate(preds) if p == -1]
    return anomalies

def plot_severity_stats(errors, warnings, anomalies, infos):
    labels = ['Errors', 'Warnings', 'Anomalies', 'Info']
    counts = [len(errors), len(warnings), len(anomalies), len(infos)]
    colors = ['#ff6f61', '#6b5b95', '#88b04b', '#f7cac9'] 
    fig, ax = plt.subplots(figsize=(4, 4), dpi=150)  # higher dpi for crisp image
    ax.pie(
        counts,
        labels=labels,
        autopct='%1.1f%%',     # 1 decimal place for percentages
        startangle=140,
        colors=colors,
        textprops={'fontsize': 5}   # readable font size
    )
    ax.axis('equal')
    st.pyplot(fig)

def plot_error_timeline(timestamps, errors, warnings):
    if not timestamps:
        st.info("No timestamps found for timeline.")
        return
    df = pd.DataFrame(timestamps, columns=['log','timestamp'])
    df['type'] = df['log'].apply(lambda x: 'Error' if any(e in x for e in errors) else ('Warning' if any(w in x for w in warnings) else 'Info'))
    df_grouped = df.groupby(['timestamp', 'type']).size().unstack(fill_value=0)
    df_grouped.plot(kind='line', figsize=(10, 4))
    plt.xlabel("Timestamp")
    plt.ylabel("Count")
    plt.title("Error & Warning Timeline")
    plt.grid(True)
    st.pyplot(plt.gcf())

def filter_logs(log_lines, keyword=None, severity=None, date_range=None):
    filtered = []
    for line in log_lines:
        if keyword and keyword.lower() not in line.lower():
            continue
        if severity:
            sev_check = False
            if severity == "Error" and "[ERROR]" in line:
                sev_check = True
            elif severity == "Warning" and "[WARNING]" in line:
                sev_check = True
            elif severity == "Info" and "[INFO]" in line:
                sev_check = True
            if not sev_check:
                continue
        if date_range:
            match = timestamp_regex.search(line)
            if not match:
                continue
            try:
                dt = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
                if not (date_range[0] <= dt <= date_range[1]):
                    continue
            except:
                continue
        filtered.append(line)
    return filtered

def parse_natural_query(query, log_lines):
    # Very simple keyword + time window parsing
    query = query.lower()
    filtered_logs = log_lines
    if "error" in query:
        filtered_logs = [l for l in filtered_logs if "[error]" in l.lower()]
    elif "warning" in query:
        filtered_logs = [l for l in filtered_logs if "[warning]" in l.lower()]
    elif "info" in query:
        filtered_logs = [l for l in filtered_logs if "[info]" in l.lower()]
    # time frame parsing (last week, today)
    now = datetime.now()
    if "last week" in query:
        start = now - timedelta(days=7)
        filtered_logs = [l for l in filtered_logs if timestamp_regex.search(l) and start <= datetime.strptime(timestamp_regex.search(l).group(1), '%Y-%m-%d %H:%M:%S') <= now]
    elif "today" in query:
        start = datetime(now.year, now.month, now.day)
        filtered_logs = [l for l in filtered_logs if timestamp_regex.search(l) and datetime.strptime(timestamp_regex.search(l).group(1), '%Y-%m-%d %H:%M:%S') >= start]
    return filtered_logs

def trigger_jenkins_job(log_line, action_history):
    # Mock action: append to history and show UI success
    action = {
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "action": "Trigger Jenkins Sonar Analysis Job",
        "trigger_log": log_line
    }
    action_history.append(action)
    save_action_history(action_history)
    st.success("Action: 🚀 Triggering Jenkins Sonar Analysis Job...")

# --- Main app ---

# OpenAI GPT toggle and key
use_gpt = st.sidebar.checkbox("Use GPT fallback", value=True)
if use_gpt:

    if os.getenv("OPENAI_API_KEY"):
        openai_key = os.getenv("OPENAI_API_KEY")
        st.success("✅ GPT fallback enabled using key from .env")
    else:
        st.warning("⚠️ No OpenAI API key found in .env file.") 
        openai_key = st.sidebar.text_input("🔑 Enter OpenAI API Key", type="password")
        st.warning("OpenAI API key required for GPT functionality.")

#uploaded_file = st.file_uploader("Upload a .log file", type=["log", "txt"])
log_file_path = "jenkins.log" 
if os.path.exists(log_file_path):
    with open(log_file_path, "r") as file:
        log_text = file.read()
    st.success(f"📄 Loaded log file: `{log_file_path}`")
    log_lines = log_text.splitlines()

    parser = AgenticLogParser(log_text, enable_gpt=True)
    result = parser.run()

    # Detect anomalies separately for the tab
    anomalies = detect_anomalies(log_lines)

    timestamps = extract_timestamps(log_lines)
    errors = result["errors"]
    warnings = result["warnings"]
    infos = [line for line in log_lines if "[info]" in line.lower()]

    # Update persistent memory counts
    update_memory(errors, warnings)

    # Load incident knowledge base and action history
    incident_kb = load_incident_kb()
    action_history = load_action_history()

    # Streamlit Tabs
    tabs = st.tabs([
        "Errors", "Warnings", "Suggestions",
        "Log Severity Stats", "Anomaly Detection", "Error Timelines",
        "Search & Filter Logs", "Natural Language Query",
        "Resolved Incident KB", "Action History & Automation",
        "Raw Logs", "Download Results"
    ])

    # Errors tab
    with tabs[0]:
        st.header("❌ Errors")
        if errors:
            for err in errors:
                st.markdown(f"- {err}")
        else:
            st.info("No errors found.")

    # Warnings tab
    with tabs[1]:
        st.header("⚠️ Warnings")
        if warnings:
            for warn in warnings:
                st.markdown(f"- {warn}")
        else:
            st.info("No warnings found.")

    # Suggestions tab
    with tabs[2]:
        st.subheader("🛠️ Suggested Fixes")
        if result["suggestions"]:
            for sug in result["suggestions"]:
                st.markdown(f"### Log Entry")
                st.code(sug['log'], language="text")
                
                st.markdown(f"**🔧 Suggested Solution:**")
                st.write(sug['solution'])
                
                if sug.get("comment"):
                    st.markdown(f"**💬 Comment:** {sug['comment']}")
                
                #st.markdown(f"**📚 Knowledge base:** `{sug['Releng KB']}`")
                st.markdown(f"**📚 Knowledge base:** Releng")
                if "sonar" in sug['log'].lower():
                    st.success("Action: 🚀 Triggering Jenkins Sonar Analysis Job...")
                
                st.markdown("---")
        else:
            st.info("No suggestions found.")

    # Log Severity Stats tab
    with tabs[3]:
        st.header("📊 Log Severity Statistics")
        show_memory_summary()
        st.markdown("### Severity Distribution Pie Chart")
        plot_severity_stats(errors, warnings, anomalies, infos)

        st.markdown("### Severity Counts Bar Chart")
        severity_counts = {"Errors": len(errors), "Warnings": len(warnings), "Anomalies": len(anomalies), "Info": len(infos)}
        st.bar_chart(severity_counts)

        st.markdown("### Trend Over Time")
        plot_error_timeline(timestamps, errors, warnings)

    # Anomaly Detection tab
    with tabs[4]:
        st.header("🚨 Anomaly Detection")
        if anomalies:
            st.markdown("Detected unusual log lines (potential anomalies):")
            for a in anomalies:
                st.code(a)
        else:
            st.info("No anomalies detected.")

    # Error Timelines tab
    with tabs[5]:
        st.header("🕒 Error Timelines")
        plot_error_timeline(timestamps, errors, warnings)

    # Search & Filter Logs tab
    with tabs[6]:
        st.header("🔍 Search & Filter Logs")
        keyword = st.text_input("Enter keyword to search")
        severity_filter = st.selectbox("Select severity level", ["All", "Error", "Warning", "Info"])
        date_start = st.date_input("Start date")
        date_end = st.date_input("End date")
        if date_start > date_end:
            st.error("Start date must be before end date")
        else:
            date_range = (datetime.combine(date_start, datetime.min.time()), datetime.combine(date_end, datetime.max.time()))
            filtered_logs = filter_logs(log_lines, keyword if keyword else None, severity_filter if severity_filter != "All" else None, date_range)
            st.write(f"Found {len(filtered_logs)} matching logs:")
            for line in filtered_logs:
                st.code(line)

    # Natural Language Query tab
    with tabs[7]:
        st.header("🗣️ Natural Language Query")
        query = st.text_area("Ask a question about logs (e.g., 'Show me all errors last week')")
        if st.button("Run Query"):
            if not query.strip():
                st.warning("Please enter a query.")
            else:
                nl_filtered = parse_natural_query(query, log_lines)
                st.write(f"Query returned {len(nl_filtered)} logs:")
                for l in nl_filtered:
                    st.code(l)

    # Resolved Incident KB tab
    with tabs[8]:
        st.header("📚 Resolved Incident Knowledge Base")
        if incident_kb:
            for err, details in incident_kb.items():
                st.markdown(f"**Error:** {err}")
                st.markdown(f"- **Solution:** {details.get('solution','N/A')}")
                st.markdown(f"- **Reference:** {details.get('link','No link available')}")
                st.markdown("---")
        else:
            st.info("No incident knowledge base data found.")

    # Action History & Automation tab
    with tabs[9]:
        st.header("⚙️ Action History & Automation")
        if action_history:
            for act in reversed(action_history[-10:]):  # show last 10 actions
                st.markdown(f"- **{act['timestamp']}**: {act['action']}")
                st.markdown(f"  - Triggered by: {act['trigger_log']}")
        else:
            st.info("No action history available.")

    # Raw Logs tab
    with tabs[10]:
        st.header("📄 Raw Logs")
        st.text_area("Full Logs", log_text, height=400)

    # Download Results tab
    with tabs[11]:
        st.header("📥 Download Results")
        export_data = prepare_download_data(result)
        st.download_button(label="Download JSON Results", data=export_data, file_name="log_analysis_results.json", mime="application/json")

    # Memory summary at the end for user reference
    with st.expander("Memory Summary (Frequent Errors and Warnings)"):
        show_memory_summary()

else:
    st.info("Upload a `.log` or `.txt` file to start analyzing.")
