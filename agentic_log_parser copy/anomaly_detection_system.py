#!/usr/bin/env python3
"""
Anomaly Detection System
Statistical models for detecting anomalies using time series analysis and outlier detection
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Statistical and ML libraries
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN

# Time series analysis
try:
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.tsa.stattools import adfuller
    from statsmodels.tsa.arima.model import ARIMA
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    print("Statsmodels not available. Install with: pip install statsmodels")

# Import our components
from log_ingestion_system import LogEntry, LogSource
from nlp_preprocessing import ProcessedLogEntry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnomalyType(Enum):
    """Types of anomalies that can be detected"""
    STATISTICAL_OUTLIER = "statistical_outlier"
    VOLUME_ANOMALY = "volume_anomaly"
    PATTERN_ANOMALY = "pattern_anomaly"
    TEMPORAL_ANOMALY = "temporal_anomaly"
    BEHAVIORAL_ANOMALY = "behavioral_anomaly"
    THRESHOLD_BREACH = "threshold_breach"
    TREND_ANOMALY = "trend_anomaly"

@dataclass
class AnomalyDetectionResult:
    """Result of anomaly detection"""
    anomaly_type: AnomalyType
    anomaly_score: float
    confidence: float
    description: str
    affected_metrics: List[str]
    timestamp: datetime
    context_data: Dict[str, Any]
    severity: str
    suggested_actions: List[str]

class StatisticalAnomalyDetector:
    """Statistical methods for anomaly detection"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.z_score_threshold = self.config.get('z_score_threshold', 3.0)
        self.iqr_multiplier = self.config.get('iqr_multiplier', 1.5)
        self.percentile_threshold = self.config.get('percentile_threshold', 95)
        
    def detect_z_score_outliers(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Detect outliers using Z-score method"""
        if len(data) < 3:
            return np.array([]), np.array([])
        
        z_scores = np.abs(stats.zscore(data, nan_policy='omit'))
        outlier_indices = np.where(z_scores > self.z_score_threshold)[0]
        outlier_scores = z_scores[outlier_indices]
        
        return outlier_indices, outlier_scores
    
    def detect_iqr_outliers(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Detect outliers using Interquartile Range (IQR) method"""
        if len(data) < 4:
            return np.array([]), np.array([])
        
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - self.iqr_multiplier * IQR
        upper_bound = Q3 + self.iqr_multiplier * IQR
        
        outlier_mask = (data < lower_bound) | (data > upper_bound)
        outlier_indices = np.where(outlier_mask)[0]
        
        # Calculate outlier scores based on distance from bounds
        outlier_scores = np.zeros(len(outlier_indices))
        for i, idx in enumerate(outlier_indices):
            if data[idx] < lower_bound:
                outlier_scores[i] = (lower_bound - data[idx]) / IQR
            else:
                outlier_scores[i] = (data[idx] - upper_bound) / IQR
        
        return outlier_indices, outlier_scores
    
    def detect_percentile_outliers(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Detect outliers using percentile method"""
        if len(data) < 10:
            return np.array([]), np.array([])
        
        threshold = np.percentile(data, self.percentile_threshold)
        outlier_mask = data > threshold
        outlier_indices = np.where(outlier_mask)[0]
        
        # Normalize scores to 0-1 range
        if len(outlier_indices) > 0:
            max_val = np.max(data)
            outlier_scores = (data[outlier_indices] - threshold) / (max_val - threshold)
        else:
            outlier_scores = np.array([])
        
        return outlier_indices, outlier_scores

class TimeSeriesAnomalyDetector:
    """Time series-based anomaly detection"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.window_size = self.config.get('window_size', 24)  # hours
        self.seasonal_period = self.config.get('seasonal_period', 24)  # daily seasonality
        
    def detect_volume_anomalies(self, timestamps: List[datetime], 
                               window_hours: int = 1) -> List[AnomalyDetectionResult]:
        """Detect anomalies in log volume over time"""
        anomalies = []
        
        if len(timestamps) < 10:
            return anomalies
        
        # Create time series of log counts
        df = pd.DataFrame({'timestamp': timestamps})
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        # Resample to hourly counts
        hourly_counts = df.resample(f'{window_hours}H').size()
        
        if len(hourly_counts) < 5:
            return anomalies
        
        # Detect outliers in volume
        detector = StatisticalAnomalyDetector()
        outlier_indices, outlier_scores = detector.detect_z_score_outliers(hourly_counts.values)
        
        for idx, score in zip(outlier_indices, outlier_scores):
            timestamp = hourly_counts.index[idx]
            volume = hourly_counts.iloc[idx]
            
            anomaly = AnomalyDetectionResult(
                anomaly_type=AnomalyType.VOLUME_ANOMALY,
                anomaly_score=float(score),
                confidence=min(score / 3.0, 1.0),  # Normalize to 0-1
                description=f"Unusual log volume: {volume} logs in {window_hours}h window",
                affected_metrics=['log_volume'],
                timestamp=timestamp,
                context_data={'volume': int(volume), 'window_hours': window_hours},
                severity='high' if score > 4 else 'medium',
                suggested_actions=[
                    "Investigate cause of volume spike",
                    "Check system resources",
                    "Review application behavior"
                ]
            )
            anomalies.append(anomaly)
        
        return anomalies
    
    def detect_temporal_patterns(self, timestamps: List[datetime]) -> List[AnomalyDetectionResult]:
        """Detect anomalies in temporal patterns"""
        anomalies = []
        
        if len(timestamps) < 48:  # Need at least 2 days of data
            return anomalies
        
        # Create time series
        df = pd.DataFrame({'timestamp': timestamps})
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        
        # Analyze hourly patterns
        hourly_counts = df.groupby('hour').size()
        expected_pattern = hourly_counts.mean()
        
        # Detect deviations from expected hourly pattern
        for hour, count in hourly_counts.items():
            deviation = abs(count - expected_pattern) / expected_pattern
            if deviation > 0.5:  # 50% deviation threshold
                anomaly = AnomalyDetectionResult(
                    anomaly_type=AnomalyType.TEMPORAL_ANOMALY,
                    anomaly_score=float(deviation),
                    confidence=min(deviation, 1.0),
                    description=f"Unusual activity pattern at hour {hour}: {count} vs expected {expected_pattern:.1f}",
                    affected_metrics=['temporal_pattern'],
                    timestamp=datetime.now().replace(hour=hour, minute=0, second=0, microsecond=0),
                    context_data={'hour': hour, 'count': int(count), 'expected': float(expected_pattern)},
                    severity='medium',
                    suggested_actions=[
                        "Review scheduled jobs and processes",
                        "Check for unusual user activity",
                        "Investigate system events"
                    ]
                )
                anomalies.append(anomaly)
        
        return anomalies

class MLAnomalyDetector:
    """Machine learning-based anomaly detection"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.contamination = self.config.get('contamination', 0.1)
        self.models = {}
        self.scalers = {}
        
    def train_isolation_forest(self, features: np.ndarray, model_name: str = 'default'):
        """Train Isolation Forest model"""
        model = IsolationForest(
            contamination=self.contamination,
            random_state=42,
            n_estimators=100
        )
        
        # Scale features
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)
        
        # Train model
        model.fit(scaled_features)
        
        # Store model and scaler
        self.models[model_name] = model
        self.scalers[model_name] = scaler
        
        logger.info(f"Isolation Forest model '{model_name}' trained with {len(features)} samples")
    
    def train_one_class_svm(self, features: np.ndarray, model_name: str = 'svm'):
        """Train One-Class SVM model"""
        model = OneClassSVM(
            kernel='rbf',
            gamma='scale',
            nu=self.contamination
        )
        
        # Scale features
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(features)
        
        # Train model
        model.fit(scaled_features)
        
        # Store model and scaler
        self.models[model_name] = model
        self.scalers[model_name] = scaler
        
        logger.info(f"One-Class SVM model '{model_name}' trained with {len(features)} samples")
    
    def detect_anomalies(self, features: np.ndarray, 
                        model_name: str = 'default') -> Tuple[np.ndarray, np.ndarray]:
        """Detect anomalies using trained model"""
        if model_name not in self.models:
            raise ValueError(f"Model '{model_name}' not found. Train the model first.")
        
        model = self.models[model_name]
        scaler = self.scalers[model_name]
        
        # Scale features
        scaled_features = scaler.transform(features)
        
        # Predict anomalies
        predictions = model.predict(scaled_features)
        anomaly_scores = model.decision_function(scaled_features)
        
        # Convert predictions to boolean mask (1 = normal, -1 = anomaly)
        anomaly_mask = predictions == -1
        anomaly_indices = np.where(anomaly_mask)[0]
        
        # Normalize scores to positive values
        normalized_scores = (anomaly_scores[anomaly_indices] - anomaly_scores.min()) / (anomaly_scores.max() - anomaly_scores.min())
        
        return anomaly_indices, normalized_scores

class ThresholdAnomalyDetector:
    """Threshold-based anomaly detection for metrics"""
    
    def __init__(self, thresholds: Dict[str, Dict[str, float]] = None):
        self.thresholds = thresholds or {
            'error_rate': {'warning': 5.0, 'critical': 10.0},
            'response_time': {'warning': 1000.0, 'critical': 5000.0},  # ms
            'cpu_usage': {'warning': 80.0, 'critical': 95.0},  # %
            'memory_usage': {'warning': 85.0, 'critical': 95.0},  # %
            'disk_usage': {'warning': 90.0, 'critical': 98.0},  # %
            'log_volume': {'warning': 1000, 'critical': 5000}  # logs per hour
        }
    
    def check_thresholds(self, metrics: Dict[str, float]) -> List[AnomalyDetectionResult]:
        """Check metrics against defined thresholds"""
        anomalies = []
        
        for metric_name, value in metrics.items():
            if metric_name in self.thresholds:
                thresholds = self.thresholds[metric_name]
                
                severity = None
                if value >= thresholds.get('critical', float('inf')):
                    severity = 'critical'
                elif value >= thresholds.get('warning', float('inf')):
                    severity = 'warning'
                
                if severity:
                    # Calculate anomaly score based on how much threshold is exceeded
                    threshold_value = thresholds[severity]
                    anomaly_score = min(value / threshold_value, 5.0)  # Cap at 5x threshold
                    
                    anomaly = AnomalyDetectionResult(
                        anomaly_type=AnomalyType.THRESHOLD_BREACH,
                        anomaly_score=anomaly_score,
                        confidence=1.0,  # High confidence for threshold breaches
                        description=f"{metric_name} exceeded {severity} threshold: {value} > {threshold_value}",
                        affected_metrics=[metric_name],
                        timestamp=datetime.now(),
                        context_data={
                            'metric': metric_name,
                            'value': value,
                            'threshold': threshold_value,
                            'severity': severity
                        },
                        severity=severity,
                        suggested_actions=self._get_threshold_actions(metric_name, severity)
                    )
                    anomalies.append(anomaly)
        
        return anomalies
    
    def _get_threshold_actions(self, metric_name: str, severity: str) -> List[str]:
        """Get suggested actions for threshold breaches"""
        actions = {
            'error_rate': [
                "Investigate recent deployments",
                "Check application logs for errors",
                "Review system health metrics"
            ],
            'response_time': [
                "Check system resources",
                "Review database performance",
                "Analyze network latency"
            ],
            'cpu_usage': [
                "Scale up compute resources",
                "Identify CPU-intensive processes",
                "Optimize application performance"
            ],
            'memory_usage': [
                "Scale up memory resources",
                "Check for memory leaks",
                "Optimize memory usage"
            ],
            'disk_usage': [
                "Clean up disk space",
                "Archive old logs",
                "Scale up storage"
            ],
            'log_volume': [
                "Investigate cause of log volume spike",
                "Check for error cascades",
                "Review log retention policies"
            ]
        }
        
        return actions.get(metric_name, ["Investigate the issue", "Check system health"])

class AnomalyDetectionEngine:
    """Main anomaly detection engine combining multiple detection methods"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize detectors
        self.statistical_detector = StatisticalAnomalyDetector(self.config.get('statistical', {}))
        self.timeseries_detector = TimeSeriesAnomalyDetector(self.config.get('timeseries', {}))
        self.ml_detector = MLAnomalyDetector(self.config.get('ml', {}))
        self.threshold_detector = ThresholdAnomalyDetector(self.config.get('thresholds', {}))
        
        # Configuration
        self.enable_statistical = self.config.get('enable_statistical', True)
        self.enable_timeseries = self.config.get('enable_timeseries', True)
        self.enable_ml = self.config.get('enable_ml', False)  # Requires training
        self.enable_threshold = self.config.get('enable_threshold', True)
        
        # Storage for historical data
        self.historical_features = []
        self.is_ml_trained = False
    
    def detect_anomalies(self, 
                        log_entries: List[LogEntry],
                        system_metrics: Dict[str, float] = None) -> List[AnomalyDetectionResult]:
        """Detect anomalies using all enabled methods"""
        all_anomalies = []
        
        if not log_entries:
            return all_anomalies
        
        # Extract timestamps for time series analysis
        timestamps = [entry.timestamp for entry in log_entries]
        
        # 1. Time series anomaly detection
        if self.enable_timeseries:
            volume_anomalies = self.timeseries_detector.detect_volume_anomalies(timestamps)
            temporal_anomalies = self.timeseries_detector.detect_temporal_patterns(timestamps)
            all_anomalies.extend(volume_anomalies)
            all_anomalies.extend(temporal_anomalies)
        
        # 2. Statistical anomaly detection on log features
        if self.enable_statistical and len(log_entries) > 10:
            # Extract numerical features
            features = self._extract_log_features(log_entries)
            if features.size > 0:
                statistical_anomalies = self._detect_statistical_anomalies(features, timestamps)
                all_anomalies.extend(statistical_anomalies)
        
        # 3. ML-based anomaly detection
        if self.enable_ml and self.is_ml_trained and len(log_entries) > 5:
            features = self._extract_log_features(log_entries)
            if features.size > 0:
                ml_anomalies = self._detect_ml_anomalies(features, timestamps)
                all_anomalies.extend(ml_anomalies)
        
        # 4. Threshold-based detection on system metrics
        if self.enable_threshold and system_metrics:
            threshold_anomalies = self.threshold_detector.check_thresholds(system_metrics)
            all_anomalies.extend(threshold_anomalies)
        
        # Sort anomalies by severity and score
        severity_order = {'critical': 3, 'high': 2, 'medium': 1, 'low': 0}
        all_anomalies.sort(
            key=lambda x: (severity_order.get(x.severity, 0), x.anomaly_score),
            reverse=True
        )
        
        return all_anomalies
    
    def _extract_log_features(self, log_entries: List[LogEntry]) -> np.ndarray:
        """Extract numerical features from log entries"""
        features = []
        
        for entry in log_entries:
            feature_vector = [
                len(entry.message),  # Message length
                len(entry.raw_content),  # Raw content length
                1 if entry.level == 'ERROR' else 0,  # Is error
                1 if entry.level == 'WARNING' else 0,  # Is warning
                entry.timestamp.hour,  # Hour of day
                entry.timestamp.weekday(),  # Day of week
            ]
            features.append(feature_vector)
        
        return np.array(features)
    
    def _detect_statistical_anomalies(self, features: np.ndarray, 
                                    timestamps: List[datetime]) -> List[AnomalyDetectionResult]:
        """Detect statistical anomalies in log features"""
        anomalies = []
        
        # Detect outliers in each feature dimension
        for i, feature_name in enumerate(['msg_length', 'content_length', 'is_error', 'is_warning', 'hour', 'weekday']):
            feature_values = features[:, i]
            
            if np.std(feature_values) > 0:  # Skip constant features
                outlier_indices, outlier_scores = self.statistical_detector.detect_z_score_outliers(feature_values)
                
                for idx, score in zip(outlier_indices, outlier_scores):
                    if score > 2.0:  # Only report significant outliers
                        anomaly = AnomalyDetectionResult(
                            anomaly_type=AnomalyType.STATISTICAL_OUTLIER,
                            anomaly_score=float(score),
                            confidence=min(score / 3.0, 1.0),
                            description=f"Statistical outlier in {feature_name}: {feature_values[idx]}",
                            affected_metrics=[feature_name],
                            timestamp=timestamps[idx],
                            context_data={
                                'feature': feature_name,
                                'value': float(feature_values[idx]),
                                'z_score': float(score)
                            },
                            severity='medium' if score > 3 else 'low',
                            suggested_actions=[
                                f"Investigate unusual {feature_name} value",
                                "Check log entry context",
                                "Review system behavior"
                            ]
                        )
                        anomalies.append(anomaly)
        
        return anomalies
    
    def _detect_ml_anomalies(self, features: np.ndarray, 
                           timestamps: List[datetime]) -> List[AnomalyDetectionResult]:
        """Detect anomalies using ML models"""
        anomalies = []
        
        try:
            anomaly_indices, anomaly_scores = self.ml_detector.detect_anomalies(features)
            
            for idx, score in zip(anomaly_indices, anomaly_scores):
                anomaly = AnomalyDetectionResult(
                    anomaly_type=AnomalyType.BEHAVIORAL_ANOMALY,
                    anomaly_score=float(score),
                    confidence=float(score),
                    description=f"ML model detected behavioral anomaly",
                    affected_metrics=['log_behavior'],
                    timestamp=timestamps[idx],
                    context_data={'ml_score': float(score)},
                    severity='high' if score > 0.8 else 'medium',
                    suggested_actions=[
                        "Investigate unusual log behavior",
                        "Check for system changes",
                        "Review application performance"
                    ]
                )
                anomalies.append(anomaly)
                
        except Exception as e:
            logger.warning(f"ML anomaly detection failed: {e}")
        
        return anomalies
    
    def train_ml_models(self, historical_log_entries: List[LogEntry]):
        """Train ML models on historical data"""
        if len(historical_log_entries) < 100:
            logger.warning("Insufficient data for ML training (need at least 100 samples)")
            return
        
        # Extract features
        features = self._extract_log_features(historical_log_entries)
        
        # Train models
        self.ml_detector.train_isolation_forest(features, 'isolation_forest')
        self.ml_detector.train_one_class_svm(features, 'one_class_svm')
        
        self.is_ml_trained = True
        logger.info(f"ML models trained on {len(historical_log_entries)} log entries")

# Example usage
if __name__ == "__main__":
    # Initialize anomaly detection engine
    config = {
        'enable_statistical': True,
        'enable_timeseries': True,
        'enable_ml': False,  # Will train later
        'enable_threshold': True,
        'statistical': {'z_score_threshold': 3.0},
        'timeseries': {'window_size': 24},
        'thresholds': {
            'error_rate': {'warning': 5.0, 'critical': 10.0}
        }
    }
    
    engine = AnomalyDetectionEngine(config)
    
    # Create sample log entries
    sample_logs = []
    base_time = datetime.now() - timedelta(hours=24)
    
    for i in range(100):
        timestamp = base_time + timedelta(minutes=i*15)
        level = "ERROR" if i % 20 == 0 else "INFO"  # 5% error rate
        
        log_entry = LogEntry(
            timestamp=timestamp,
            source=LogSource.APPLICATION,
            level=level,
            message=f"Sample log message {i}",
            metadata={},
            raw_content=f"Full log content for entry {i}",
            source_identifier="test_app"
        )
        sample_logs.append(log_entry)
    
    # Add some anomalous entries
    anomalous_time = datetime.now() - timedelta(hours=2)
    for i in range(10):  # Volume spike
        log_entry = LogEntry(
            timestamp=anomalous_time + timedelta(seconds=i*10),
            source=LogSource.APPLICATION,
            level="ERROR",
            message=f"Anomalous error {i}",
            metadata={},
            raw_content=f"Anomalous content {i}",
            source_identifier="test_app"
        )
        sample_logs.append(log_entry)
    
    # Sample system metrics
    system_metrics = {
        'error_rate': 12.0,  # Above critical threshold
        'cpu_usage': 85.0,   # Above warning threshold
        'memory_usage': 70.0  # Normal
    }
    
    # Detect anomalies
    anomalies = engine.detect_anomalies(sample_logs, system_metrics)
    
    print(f"Detected {len(anomalies)} anomalies:")
    for i, anomaly in enumerate(anomalies[:5], 1):  # Show top 5
        print(f"\n--- Anomaly {i} ---")
        print(f"Type: {anomaly.anomaly_type.value}")
        print(f"Description: {anomaly.description}")
        print(f"Score: {anomaly.anomaly_score:.3f}")
        print(f"Severity: {anomaly.severity}")
        print(f"Timestamp: {anomaly.timestamp}")
        print(f"Suggested Actions: {anomaly.suggested_actions[:2]}")  # First 2 actions
