#!/usr/bin/env python3
"""
ML-based Trend Analysis System
Machine learning models for trend detection using LSTM networks, seasonal decomposition, and pattern recognition
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor

# Deep Learning (optional)
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available. Install with: pip install torch")

# Time series analysis
try:
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.tsa.holtwinters import ExponentialSmoothing
    from statsmodels.tsa.arima.model import ARIMA
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    print("Statsmodels not available. Install with: pip install statsmodels")

# Import our components
from log_ingestion_system import LogEntry, LogSource
from anomaly_detection_system import AnomalyDetectionResult, AnomalyType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrendType(Enum):
    """Types of trends that can be detected"""
    INCREASING = "increasing"
    DECREASING = "decreasing"
    SEASONAL = "seasonal"
    CYCLICAL = "cyclical"
    VOLATILE = "volatile"
    STABLE = "stable"
    ANOMALOUS = "anomalous"

@dataclass
class TrendAnalysisResult:
    """Result of trend analysis"""
    trend_type: TrendType
    confidence: float
    trend_strength: float
    description: str
    time_period: str
    forecast_values: List[float]
    forecast_timestamps: List[datetime]
    seasonality_detected: bool
    trend_direction: str
    change_rate: float
    context_data: Dict[str, Any]

class LSTMTrendPredictor:
    """LSTM neural network for trend prediction"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.sequence_length = self.config.get('sequence_length', 24)
        self.hidden_size = self.config.get('hidden_size', 50)
        self.num_layers = self.config.get('num_layers', 2)
        self.learning_rate = self.config.get('learning_rate', 0.001)
        self.epochs = self.config.get('epochs', 100)
        
        self.model = None
        self.scaler = MinMaxScaler()
        self.is_trained = False
        
        if TORCH_AVAILABLE:
            self._build_model()
    
    def _build_model(self):
        """Build LSTM model architecture"""
        class LSTMModel(nn.Module):
            def __init__(self, input_size, hidden_size, num_layers, output_size):
                super(LSTMModel, self).__init__()
                self.hidden_size = hidden_size
                self.num_layers = num_layers
                
                self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
                self.fc = nn.Linear(hidden_size, output_size)
                self.dropout = nn.Dropout(0.2)
                
            def forward(self, x):
                h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
                c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
                
                out, _ = self.lstm(x, (h0, c0))
                out = self.dropout(out[:, -1, :])
                out = self.fc(out)
                return out
        
        self.model = LSTMModel(
            input_size=1,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            output_size=1
        )
    
    def prepare_sequences(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for LSTM training"""
        X, y = [], []
        
        for i in range(len(data) - self.sequence_length):
            X.append(data[i:(i + self.sequence_length)])
            y.append(data[i + self.sequence_length])
        
        return np.array(X), np.array(y)
    
    def train(self, time_series_data: np.ndarray) -> Dict[str, Any]:
        """Train LSTM model on time series data"""
        if not TORCH_AVAILABLE:
            logger.warning("PyTorch not available. Cannot train LSTM model.")
            return {'error': 'PyTorch not available'}
        
        if len(time_series_data) < self.sequence_length * 2:
            logger.warning("Insufficient data for LSTM training")
            return {'error': 'Insufficient data'}
        
        # Normalize data
        scaled_data = self.scaler.fit_transform(time_series_data.reshape(-1, 1)).flatten()
        
        # Prepare sequences
        X, y = self.prepare_sequences(scaled_data)
        
        # Convert to tensors
        X_tensor = torch.FloatTensor(X).unsqueeze(-1)
        y_tensor = torch.FloatTensor(y)
        
        # Create data loader
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=32, shuffle=True)
        
        # Training setup
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
        
        # Training loop
        losses = []
        for epoch in range(self.epochs):
            epoch_loss = 0
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(dataloader)
            losses.append(avg_loss)
            
            if epoch % 20 == 0:
                logger.info(f"Epoch {epoch}, Loss: {avg_loss:.6f}")
        
        self.is_trained = True
        
        return {
            'final_loss': losses[-1],
            'training_epochs': self.epochs,
            'data_points': len(time_series_data)
        }
    
    def predict(self, recent_data: np.ndarray, forecast_steps: int = 12) -> np.ndarray:
        """Predict future values"""
        if not self.is_trained or not TORCH_AVAILABLE:
            return np.array([])
        
        # Normalize recent data
        scaled_data = self.scaler.transform(recent_data.reshape(-1, 1)).flatten()
        
        # Use last sequence_length points for prediction
        if len(scaled_data) < self.sequence_length:
            # Pad with zeros if insufficient data
            padded_data = np.zeros(self.sequence_length)
            padded_data[-len(scaled_data):] = scaled_data
            scaled_data = padded_data
        
        input_sequence = scaled_data[-self.sequence_length:]
        predictions = []
        
        self.model.eval()
        with torch.no_grad():
            for _ in range(forecast_steps):
                # Prepare input
                input_tensor = torch.FloatTensor(input_sequence).unsqueeze(0).unsqueeze(-1)
                
                # Predict next value
                prediction = self.model(input_tensor)
                pred_value = prediction.item()
                predictions.append(pred_value)
                
                # Update sequence for next prediction
                input_sequence = np.append(input_sequence[1:], pred_value)
        
        # Denormalize predictions
        predictions_array = np.array(predictions).reshape(-1, 1)
        denormalized_predictions = self.scaler.inverse_transform(predictions_array).flatten()
        
        return denormalized_predictions

class SeasonalTrendAnalyzer:
    """Seasonal decomposition and trend analysis"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.seasonal_period = self.config.get('seasonal_period', 24)  # Daily seasonality
        
    def decompose_time_series(self, timestamps: List[datetime], 
                             values: List[float]) -> Dict[str, Any]:
        """Decompose time series into trend, seasonal, and residual components"""
        if not STATSMODELS_AVAILABLE or len(values) < self.seasonal_period * 2:
            return {}
        
        try:
            # Create time series
            df = pd.DataFrame({
                'timestamp': timestamps,
                'value': values
            })
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # Resample to regular intervals if needed
            df_resampled = df.resample('H').mean().fillna(method='forward')
            
            if len(df_resampled) < self.seasonal_period * 2:
                return {}
            
            # Perform seasonal decomposition
            decomposition = seasonal_decompose(
                df_resampled['value'],
                model='additive',
                period=self.seasonal_period
            )
            
            return {
                'trend': decomposition.trend.dropna().tolist(),
                'seasonal': decomposition.seasonal.dropna().tolist(),
                'residual': decomposition.resid.dropna().tolist(),
                'original': df_resampled['value'].tolist(),
                'timestamps': df_resampled.index.tolist()
            }
            
        except Exception as e:
            logger.warning(f"Seasonal decomposition failed: {e}")
            return {}
    
    def detect_seasonality(self, values: List[float]) -> Tuple[bool, float]:
        """Detect if time series has seasonal patterns"""
        if len(values) < self.seasonal_period * 2:
            return False, 0.0
        
        try:
            # Calculate autocorrelation at seasonal lag
            values_array = np.array(values)
            
            # Remove trend
            detrended = values_array - np.mean(values_array)
            
            # Calculate autocorrelation
            correlation = np.corrcoef(
                detrended[:-self.seasonal_period],
                detrended[self.seasonal_period:]
            )[0, 1]
            
            # Consider seasonal if correlation > 0.3
            is_seasonal = abs(correlation) > 0.3
            
            return is_seasonal, abs(correlation)
            
        except Exception as e:
            logger.warning(f"Seasonality detection failed: {e}")
            return False, 0.0
    
    def analyze_trend_direction(self, values: List[float]) -> Tuple[str, float]:
        """Analyze overall trend direction and strength"""
        if len(values) < 10:
            return "stable", 0.0
        
        # Linear regression to determine trend
        X = np.arange(len(values)).reshape(-1, 1)
        y = np.array(values)
        
        model = LinearRegression()
        model.fit(X, y)
        
        slope = model.coef_[0]
        r_squared = model.score(X, y)
        
        # Determine trend direction
        if abs(slope) < 0.01:
            direction = "stable"
        elif slope > 0:
            direction = "increasing"
        else:
            direction = "decreasing"
        
        # Trend strength based on R-squared and slope magnitude
        strength = min(r_squared * abs(slope) * 100, 1.0)
        
        return direction, strength

class PatternRecognitionEngine:
    """Pattern recognition for identifying recurring trends"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.pattern_length = self.config.get('pattern_length', 12)
        self.similarity_threshold = self.config.get('similarity_threshold', 0.8)
        
    def find_recurring_patterns(self, values: List[float]) -> List[Dict[str, Any]]:
        """Find recurring patterns in time series"""
        patterns = []
        
        if len(values) < self.pattern_length * 3:
            return patterns
        
        values_array = np.array(values)
        
        # Normalize values for pattern matching
        normalized_values = (values_array - np.mean(values_array)) / (np.std(values_array) + 1e-8)
        
        # Extract all possible patterns
        for i in range(len(normalized_values) - self.pattern_length + 1):
            pattern = normalized_values[i:i + self.pattern_length]
            
            # Find similar patterns
            similar_patterns = []
            for j in range(i + self.pattern_length, len(normalized_values) - self.pattern_length + 1):
                candidate = normalized_values[j:j + self.pattern_length]
                
                # Calculate similarity (correlation)
                correlation = np.corrcoef(pattern, candidate)[0, 1]
                
                if correlation > self.similarity_threshold:
                    similar_patterns.append({
                        'start_index': j,
                        'correlation': correlation,
                        'pattern': candidate.tolist()
                    })
            
            # If pattern repeats, record it
            if len(similar_patterns) >= 2:
                patterns.append({
                    'original_start': i,
                    'pattern': pattern.tolist(),
                    'repetitions': similar_patterns,
                    'frequency': len(similar_patterns) + 1,
                    'avg_correlation': np.mean([p['correlation'] for p in similar_patterns])
                })
        
        # Sort patterns by frequency and correlation
        patterns.sort(key=lambda x: (x['frequency'], x['avg_correlation']), reverse=True)
        
        return patterns[:5]  # Return top 5 patterns

class TrendAnalysisEngine:
    """Main trend analysis engine combining multiple approaches"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize components
        self.lstm_predictor = LSTMTrendPredictor(self.config.get('lstm', {}))
        self.seasonal_analyzer = SeasonalTrendAnalyzer(self.config.get('seasonal', {}))
        self.pattern_engine = PatternRecognitionEngine(self.config.get('patterns', {}))
        
        # Configuration
        self.enable_lstm = self.config.get('enable_lstm', TORCH_AVAILABLE)
        self.enable_seasonal = self.config.get('enable_seasonal', STATSMODELS_AVAILABLE)
        self.enable_patterns = self.config.get('enable_patterns', True)
        self.forecast_horizon = self.config.get('forecast_horizon', 12)
        
    def analyze_trends(self, 
                      timestamps: List[datetime],
                      values: List[float],
                      metric_name: str = "metric") -> TrendAnalysisResult:
        """Perform comprehensive trend analysis"""
        
        if len(values) < 10:
            return TrendAnalysisResult(
                trend_type=TrendType.STABLE,
                confidence=0.1,
                trend_strength=0.0,
                description="Insufficient data for trend analysis",
                time_period="unknown",
                forecast_values=[],
                forecast_timestamps=[],
                seasonality_detected=False,
                trend_direction="unknown",
                change_rate=0.0,
                context_data={}
            )
        
        # 1. Seasonal decomposition and trend analysis
        decomposition = {}
        seasonality_detected = False
        seasonal_strength = 0.0
        
        if self.enable_seasonal:
            decomposition = self.seasonal_analyzer.decompose_time_series(timestamps, values)
            seasonality_detected, seasonal_strength = self.seasonal_analyzer.detect_seasonality(values)
        
        # 2. Trend direction analysis
        trend_direction, trend_strength = self.seasonal_analyzer.analyze_trend_direction(values)
        
        # 3. Pattern recognition
        patterns = []
        if self.enable_patterns:
            patterns = self.pattern_engine.find_recurring_patterns(values)
        
        # 4. LSTM forecasting
        forecast_values = []
        forecast_timestamps = []
        
        if self.enable_lstm and len(values) >= 50:  # Need sufficient data for LSTM
            try:
                # Train LSTM if not already trained
                if not self.lstm_predictor.is_trained:
                    training_result = self.lstm_predictor.train(np.array(values))
                    logger.info(f"LSTM training result: {training_result}")
                
                # Generate forecast
                if self.lstm_predictor.is_trained:
                    forecast_values = self.lstm_predictor.predict(
                        np.array(values), 
                        self.forecast_horizon
                    ).tolist()
                    
                    # Generate forecast timestamps
                    last_timestamp = timestamps[-1]
                    time_delta = timestamps[-1] - timestamps[-2] if len(timestamps) > 1 else timedelta(hours=1)
                    
                    for i in range(1, self.forecast_horizon + 1):
                        forecast_timestamps.append(last_timestamp + time_delta * i)
                        
            except Exception as e:
                logger.warning(f"LSTM forecasting failed: {e}")
        
        # 5. Determine overall trend type
        trend_type = self._determine_trend_type(
            trend_direction, trend_strength, seasonality_detected, 
            seasonal_strength, patterns, values
        )
        
        # 6. Calculate change rate
        change_rate = self._calculate_change_rate(values)
        
        # 7. Calculate confidence
        confidence = self._calculate_confidence(
            trend_strength, seasonal_strength, len(values), len(patterns)
        )
        
        # 8. Generate description
        description = self._generate_description(
            trend_type, trend_direction, seasonality_detected, patterns, metric_name
        )
        
        # 9. Determine time period
        time_period = self._determine_time_period(timestamps)
        
        return TrendAnalysisResult(
            trend_type=trend_type,
            confidence=confidence,
            trend_strength=trend_strength,
            description=description,
            time_period=time_period,
            forecast_values=forecast_values,
            forecast_timestamps=forecast_timestamps,
            seasonality_detected=seasonality_detected,
            trend_direction=trend_direction,
            change_rate=change_rate,
            context_data={
                'seasonal_strength': seasonal_strength,
                'patterns_found': len(patterns),
                'decomposition_available': bool(decomposition),
                'lstm_forecast_available': len(forecast_values) > 0,
                'data_points': len(values)
            }
        )
    
    def _determine_trend_type(self, trend_direction: str, trend_strength: float,
                             seasonality_detected: bool, seasonal_strength: float,
                             patterns: List[Dict], values: List[float]) -> TrendType:
        """Determine the overall trend type"""
        
        # Check for volatility
        if len(values) > 1:
            volatility = np.std(values) / (np.mean(values) + 1e-8)
            if volatility > 0.5:
                return TrendType.VOLATILE
        
        # Check for seasonality
        if seasonality_detected and seasonal_strength > 0.5:
            return TrendType.SEASONAL
        
        # Check for patterns
        if patterns and len(patterns) > 0 and patterns[0]['frequency'] > 2:
            return TrendType.CYCLICAL
        
        # Check trend direction
        if trend_strength > 0.3:
            if trend_direction == "increasing":
                return TrendType.INCREASING
            elif trend_direction == "decreasing":
                return TrendType.DECREASING
        
        return TrendType.STABLE
    
    def _calculate_change_rate(self, values: List[float]) -> float:
        """Calculate the rate of change"""
        if len(values) < 2:
            return 0.0
        
        # Calculate percentage change from first to last value
        first_val = values[0]
        last_val = values[-1]
        
        if first_val == 0:
            return 0.0
        
        change_rate = ((last_val - first_val) / first_val) * 100
        return change_rate
    
    def _calculate_confidence(self, trend_strength: float, seasonal_strength: float,
                             data_points: int, pattern_count: int) -> float:
        """Calculate confidence in trend analysis"""
        
        # Base confidence on data quantity
        data_confidence = min(data_points / 100.0, 1.0)
        
        # Trend strength contribution
        trend_confidence = trend_strength
        
        # Seasonal strength contribution
        seasonal_confidence = seasonal_strength if seasonal_strength > 0 else 0.1
        
        # Pattern confidence
        pattern_confidence = min(pattern_count / 5.0, 1.0)
        
        # Weighted average
        confidence = (
            data_confidence * 0.3 +
            trend_confidence * 0.4 +
            seasonal_confidence * 0.2 +
            pattern_confidence * 0.1
        )
        
        return min(confidence, 1.0)
    
    def _generate_description(self, trend_type: TrendType, trend_direction: str,
                             seasonality_detected: bool, patterns: List[Dict],
                             metric_name: str) -> str:
        """Generate human-readable description"""
        
        descriptions = {
            TrendType.INCREASING: f"{metric_name} shows a consistent upward trend",
            TrendType.DECREASING: f"{metric_name} shows a consistent downward trend",
            TrendType.SEASONAL: f"{metric_name} exhibits seasonal patterns",
            TrendType.CYCLICAL: f"{metric_name} shows cyclical behavior with recurring patterns",
            TrendType.VOLATILE: f"{metric_name} is highly volatile with irregular fluctuations",
            TrendType.STABLE: f"{metric_name} remains relatively stable",
            TrendType.ANOMALOUS: f"{metric_name} shows anomalous behavior"
        }
        
        base_description = descriptions.get(trend_type, f"{metric_name} trend analysis")
        
        # Add additional context
        if seasonality_detected:
            base_description += " with seasonal components"
        
        if patterns and len(patterns) > 0:
            base_description += f" and {len(patterns)} recurring patterns"
        
        return base_description
    
    def _determine_time_period(self, timestamps: List[datetime]) -> str:
        """Determine the time period of analysis"""
        if len(timestamps) < 2:
            return "unknown"
        
        duration = timestamps[-1] - timestamps[0]
        
        if duration.days >= 30:
            return f"{duration.days} days"
        elif duration.days >= 1:
            return f"{duration.days} days"
        else:
            hours = duration.seconds // 3600
            return f"{hours} hours"

# Example usage
if __name__ == "__main__":
    # Initialize trend analysis engine
    config = {
        'enable_lstm': TORCH_AVAILABLE,
        'enable_seasonal': STATSMODELS_AVAILABLE,
        'enable_patterns': True,
        'forecast_horizon': 12,
        'lstm': {'sequence_length': 24, 'epochs': 50},
        'seasonal': {'seasonal_period': 24}
    }
    
    engine = TrendAnalysisEngine(config)
    
    # Generate sample time series data with trend and seasonality
    base_time = datetime.now() - timedelta(days=7)
    timestamps = []
    values = []
    
    for i in range(168):  # 7 days of hourly data
        timestamp = base_time + timedelta(hours=i)
        
        # Trend component
        trend = i * 0.1
        
        # Seasonal component (daily pattern)
        seasonal = 10 * np.sin(2 * np.pi * i / 24)
        
        # Random noise
        noise = np.random.normal(0, 2)
        
        value = 50 + trend + seasonal + noise
        
        timestamps.append(timestamp)
        values.append(max(0, value))  # Ensure non-negative values
    
    # Perform trend analysis
    result = engine.analyze_trends(timestamps, values, "error_rate")
    
    print("=== Trend Analysis Results ===")
    print(f"Trend Type: {result.trend_type.value}")
    print(f"Confidence: {result.confidence:.3f}")
    print(f"Trend Strength: {result.trend_strength:.3f}")
    print(f"Direction: {result.trend_direction}")
    print(f"Change Rate: {result.change_rate:.2f}%")
    print(f"Seasonality Detected: {result.seasonality_detected}")
    print(f"Description: {result.description}")
    print(f"Time Period: {result.time_period}")
    print(f"Forecast Points: {len(result.forecast_values)}")
    
    if result.forecast_values:
        print(f"Next 3 Forecast Values: {result.forecast_values[:3]}")
    
    print(f"Context Data: {result.context_data}")
