#!/usr/bin/env python3
"""
Error Classification System
Machine learning models for automatic error categorization and classification
"""

import os
import json
import pickle
import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer

# Deep Learning (optional)
try:
    import torch
    import torch.nn as nn
    from transformers import AutoTokenizer, AutoModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("Transformers not available. Install with: pip install torch transformers")

# Import our components
from nlp_preprocessing import ProcessedLogEntry, NLPPreprocessingPipeline

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorCategory(Enum):
    """Predefined error categories for DevOps logs"""
    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    AUTHENTICATION_ERROR = "authentication_error"
    PERMISSION_ERROR = "permission_error"
    CONFIGURATION_ERROR = "configuration_error"
    RESOURCE_ERROR = "resource_error"
    BUILD_ERROR = "build_error"
    DEPLOYMENT_ERROR = "deployment_error"
    SERVICE_ERROR = "service_error"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    DEPENDENCY_ERROR = "dependency_error"
    UNKNOWN_ERROR = "unknown_error"

@dataclass
class ErrorClassificationResult:
    """Result of error classification"""
    predicted_category: ErrorCategory
    confidence_score: float
    probability_distribution: Dict[str, float]
    features_used: List[str]
    model_name: str
    timestamp: datetime

class ErrorPatternMatcher:
    """Rule-based error pattern matching for initial classification"""
    
    def __init__(self):
        self.error_patterns = {
            ErrorCategory.NETWORK_ERROR: [
                r'connection\s+(?:refused|timeout|failed)',
                r'network\s+(?:unreachable|error)',
                r'host\s+(?:not\s+found|unreachable)',
                r'dns\s+(?:resolution\s+)?(?:failed|error)',
                r'socket\s+(?:timeout|error)',
                r'http\s+(?:error\s+)?(?:404|500|502|503|504)',
                r'ssl\s+(?:handshake\s+)?(?:failed|error)'
            ],
            ErrorCategory.DATABASE_ERROR: [
                r'database\s+(?:connection\s+)?(?:failed|error)',
                r'sql\s+(?:syntax\s+)?error',
                r'table\s+(?:not\s+found|doesn\'t\s+exist)',
                r'duplicate\s+(?:key|entry)',
                r'foreign\s+key\s+constraint',
                r'deadlock\s+detected',
                r'connection\s+pool\s+exhausted'
            ],
            ErrorCategory.AUTHENTICATION_ERROR: [
                r'authentication\s+(?:failed|error)',
                r'invalid\s+(?:credentials|password|username)',
                r'access\s+denied',
                r'unauthorized\s+access',
                r'login\s+failed',
                r'token\s+(?:expired|invalid)',
                r'certificate\s+(?:expired|invalid)'
            ],
            ErrorCategory.PERMISSION_ERROR: [
                r'permission\s+denied',
                r'access\s+(?:forbidden|denied)',
                r'insufficient\s+(?:privileges|permissions)',
                r'operation\s+not\s+permitted',
                r'file\s+not\s+accessible',
                r'directory\s+not\s+writable'
            ],
            ErrorCategory.CONFIGURATION_ERROR: [
                r'configuration\s+(?:error|invalid)',
                r'config\s+(?:file\s+)?(?:not\s+found|missing)',
                r'invalid\s+(?:parameter|setting)',
                r'missing\s+(?:required\s+)?(?:property|configuration)',
                r'malformed\s+(?:config|configuration)',
                r'environment\s+variable\s+not\s+set'
            ],
            ErrorCategory.RESOURCE_ERROR: [
                r'out\s+of\s+memory',
                r'disk\s+(?:space\s+)?(?:full|exhausted)',
                r'cpu\s+(?:usage\s+)?(?:high|exceeded)',
                r'resource\s+(?:limit\s+)?(?:exceeded|exhausted)',
                r'memory\s+(?:leak|exhausted)',
                r'file\s+(?:handle|descriptor)\s+(?:limit|exhausted)'
            ],
            ErrorCategory.BUILD_ERROR: [
                r'build\s+(?:failed|error)',
                r'compilation\s+(?:failed|error)',
                r'dependency\s+(?:not\s+found|resolution\s+failed)',
                r'package\s+(?:not\s+found|missing)',
                r'syntax\s+error',
                r'import\s+(?:error|failed)',
                r'module\s+not\s+found'
            ],
            ErrorCategory.DEPLOYMENT_ERROR: [
                r'deployment\s+(?:failed|error)',
                r'container\s+(?:failed\s+to\s+start|crashed)',
                r'service\s+(?:failed\s+to\s+start|unavailable)',
                r'port\s+(?:already\s+in\s+use|binding\s+failed)',
                r'image\s+(?:not\s+found|pull\s+failed)',
                r'health\s+check\s+failed'
            ],
            ErrorCategory.TIMEOUT_ERROR: [
                r'timeout\s+(?:occurred|exceeded)',
                r'request\s+timeout',
                r'connection\s+timeout',
                r'read\s+timeout',
                r'operation\s+timed\s+out',
                r'deadline\s+exceeded'
            ]
        }
        
        # Compile patterns for efficiency
        import re
        self.compiled_patterns = {}
        for category, patterns in self.error_patterns.items():
            self.compiled_patterns[category] = [
                re.compile(pattern, re.IGNORECASE) for pattern in patterns
            ]
    
    def classify_by_patterns(self, text: str) -> Optional[Tuple[ErrorCategory, float]]:
        """Classify error using pattern matching"""
        matches = {}
        
        for category, patterns in self.compiled_patterns.items():
            match_count = 0
            for pattern in patterns:
                if pattern.search(text):
                    match_count += 1
            
            if match_count > 0:
                # Confidence based on number of matching patterns
                confidence = min(match_count / len(patterns), 1.0)
                matches[category] = confidence
        
        if matches:
            # Return category with highest confidence
            best_category = max(matches.keys(), key=lambda k: matches[k])
            return best_category, matches[best_category]
        
        return None

class MLErrorClassifier:
    """Machine learning-based error classifier"""
    
    def __init__(self, model_type: str = "random_forest"):
        self.model_type = model_type
        self.model = None
        self.vectorizer = None
        self.label_encoder = None
        self.scaler = None
        self.feature_names = []
        self.is_trained = False
        
        # Initialize model based on type
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the ML model"""
        if self.model_type == "random_forest":
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            )
        elif self.model_type == "gradient_boosting":
            self.model = GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
        elif self.model_type == "svm":
            self.model = SVC(
                kernel='rbf',
                probability=True,
                class_weight='balanced',
                random_state=42
            )
        elif self.model_type == "naive_bayes":
            self.model = MultinomialNB(alpha=1.0)
        elif self.model_type == "logistic_regression":
            self.model = LogisticRegression(
                max_iter=1000,
                class_weight='balanced',
                random_state=42
            )
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
        
        # Initialize text vectorizer
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 3),
            min_df=2,
            max_df=0.95,
            stop_words='english'
        )
        
        # Initialize label encoder
        self.label_encoder = LabelEncoder()
        
        # Initialize scaler for numerical features
        self.scaler = StandardScaler()
    
    def prepare_features(self, processed_logs: List[ProcessedLogEntry]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features for training/prediction"""
        # Extract text features
        texts = [log.cleaned_text for log in processed_logs]
        text_features = self.vectorizer.fit_transform(texts).toarray()
        
        # Extract numerical features
        numerical_features = []
        for log in processed_logs:
            features = log.features
            num_features = [
                features.get('text_length', 0),
                features.get('token_count', 0),
                features.get('avg_token_length', 0),
                features.get('unique_token_ratio', 0),
                features.get('uppercase_ratio', 0),
                features.get('digit_ratio', 0),
                features.get('punctuation_ratio', 0),
                len(features.get('pattern_counts', {}))
            ]
            numerical_features.append(num_features)
        
        numerical_features = np.array(numerical_features)
        if numerical_features.size > 0:
            numerical_features = self.scaler.fit_transform(numerical_features)
        
        # Combine features
        if numerical_features.size > 0:
            combined_features = np.hstack([text_features, numerical_features])
        else:
            combined_features = text_features
        
        # Store feature names
        text_feature_names = [f"text_feature_{i}" for i in range(text_features.shape[1])]
        numerical_feature_names = [
            'text_length', 'token_count', 'avg_token_length', 'unique_token_ratio',
            'uppercase_ratio', 'digit_ratio', 'punctuation_ratio', 'pattern_count'
        ]
        self.feature_names = text_feature_names + numerical_feature_names
        
        return combined_features, texts
    
    def train(self, processed_logs: List[ProcessedLogEntry], labels: List[str]) -> Dict[str, Any]:
        """Train the classifier"""
        logger.info(f"Training {self.model_type} classifier with {len(processed_logs)} samples")
        
        # Prepare features
        X, texts = self.prepare_features(processed_logs)
        
        # Encode labels
        y = self.label_encoder.fit_transform(labels)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train model
        self.model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        # Cross-validation
        cv_scores = cross_val_score(self.model, X_train, y_train, cv=5)
        
        self.is_trained = True
        
        # Return training results
        results = {
            'accuracy': accuracy,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'feature_count': X.shape[1],
            'sample_count': len(processed_logs),
            'classes': list(self.label_encoder.classes_)
        }
        
        logger.info(f"Training completed. Accuracy: {accuracy:.3f}, CV: {cv_scores.mean():.3f}±{cv_scores.std():.3f}")
        
        return results
    
    def predict(self, processed_log: ProcessedLogEntry) -> ErrorClassificationResult:
        """Predict error category for a single log entry"""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        # Prepare features for single sample
        text_features = self.vectorizer.transform([processed_log.cleaned_text]).toarray()
        
        # Extract numerical features
        features = processed_log.features
        num_features = np.array([[
            features.get('text_length', 0),
            features.get('token_count', 0),
            features.get('avg_token_length', 0),
            features.get('unique_token_ratio', 0),
            features.get('uppercase_ratio', 0),
            features.get('digit_ratio', 0),
            features.get('punctuation_ratio', 0),
            len(features.get('pattern_counts', {}))
        ]])
        
        if num_features.size > 0:
            num_features = self.scaler.transform(num_features)
            combined_features = np.hstack([text_features, num_features])
        else:
            combined_features = text_features
        
        # Make prediction
        prediction = self.model.predict(combined_features)[0]
        probabilities = self.model.predict_proba(combined_features)[0]
        
        # Convert to category
        predicted_category_str = self.label_encoder.inverse_transform([prediction])[0]
        predicted_category = ErrorCategory(predicted_category_str)
        
        # Get probability distribution
        prob_dist = {}
        for i, class_name in enumerate(self.label_encoder.classes_):
            prob_dist[class_name] = float(probabilities[i])
        
        confidence_score = float(max(probabilities))
        
        return ErrorClassificationResult(
            predicted_category=predicted_category,
            confidence_score=confidence_score,
            probability_distribution=prob_dist,
            features_used=self.feature_names,
            model_name=self.model_type,
            timestamp=datetime.now()
        )
    
    def save_model(self, filepath: str):
        """Save trained model to file"""
        if not self.is_trained:
            raise ValueError("Cannot save untrained model")
        
        model_data = {
            'model': self.model,
            'vectorizer': self.vectorizer,
            'label_encoder': self.label_encoder,
            'scaler': self.scaler,
            'feature_names': self.feature_names,
            'model_type': self.model_type,
            'is_trained': self.is_trained
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load trained model from file"""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.model = model_data['model']
        self.vectorizer = model_data['vectorizer']
        self.label_encoder = model_data['label_encoder']
        self.scaler = model_data['scaler']
        self.feature_names = model_data['feature_names']
        self.model_type = model_data['model_type']
        self.is_trained = model_data['is_trained']
        
        logger.info(f"Model loaded from {filepath}")

class ErrorClassificationSystem:
    """Main error classification system combining rule-based and ML approaches"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize components
        self.pattern_matcher = ErrorPatternMatcher()
        self.ml_classifier = None
        self.nlp_pipeline = NLPPreprocessingPipeline()
        
        # Configuration
        self.use_ml = self.config.get('use_ml', True)
        self.ml_model_type = self.config.get('ml_model_type', 'random_forest')
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)
        self.model_path = self.config.get('model_path', 'error_classifier.pkl')
        
        # Initialize ML classifier if enabled
        if self.use_ml:
            self.ml_classifier = MLErrorClassifier(self.ml_model_type)
            
            # Try to load existing model
            if os.path.exists(self.model_path):
                try:
                    self.ml_classifier.load_model(self.model_path)
                    logger.info("Loaded existing ML model")
                except Exception as e:
                    logger.warning(f"Failed to load existing model: {e}")
    
    def classify_error(self, log_text: str, metadata: Dict[str, Any] = None) -> ErrorClassificationResult:
        """Classify an error log entry"""
        # First try pattern matching
        pattern_result = self.pattern_matcher.classify_by_patterns(log_text)
        
        # If ML is available and trained, use it
        if self.use_ml and self.ml_classifier and self.ml_classifier.is_trained:
            # Process log for ML
            processed_log = self.nlp_pipeline.process_log_entry(log_text, metadata or {})
            ml_result = self.ml_classifier.predict(processed_log)
            
            # Combine results (prefer ML if confidence is high)
            if ml_result.confidence_score >= self.confidence_threshold:
                return ml_result
            elif pattern_result and pattern_result[1] >= self.confidence_threshold:
                return ErrorClassificationResult(
                    predicted_category=pattern_result[0],
                    confidence_score=pattern_result[1],
                    probability_distribution={pattern_result[0].value: pattern_result[1]},
                    features_used=['pattern_matching'],
                    model_name='pattern_matcher',
                    timestamp=datetime.now()
                )
            else:
                # Return ML result even if confidence is low
                return ml_result
        
        # Fallback to pattern matching
        elif pattern_result:
            return ErrorClassificationResult(
                predicted_category=pattern_result[0],
                confidence_score=pattern_result[1],
                probability_distribution={pattern_result[0].value: pattern_result[1]},
                features_used=['pattern_matching'],
                model_name='pattern_matcher',
                timestamp=datetime.now()
            )
        
        # Default to unknown error
        return ErrorClassificationResult(
            predicted_category=ErrorCategory.UNKNOWN_ERROR,
            confidence_score=0.1,
            probability_distribution={ErrorCategory.UNKNOWN_ERROR.value: 1.0},
            features_used=[],
            model_name='default',
            timestamp=datetime.now()
        )
    
    def train_ml_classifier(self, training_data: List[Tuple[str, str, Dict[str, Any]]]) -> Dict[str, Any]:
        """Train the ML classifier with labeled data"""
        if not self.use_ml:
            raise ValueError("ML is disabled in configuration")
        
        # Process training data
        processed_logs = []
        labels = []
        
        for log_text, label, metadata in training_data:
            processed_log = self.nlp_pipeline.process_log_entry(log_text, metadata)
            processed_logs.append(processed_log)
            labels.append(label)
        
        # Train classifier
        results = self.ml_classifier.train(processed_logs, labels)
        
        # Save model
        self.ml_classifier.save_model(self.model_path)
        
        return results

# Example usage and testing
if __name__ == "__main__":
    # Initialize classification system
    config = {
        'use_ml': True,
        'ml_model_type': 'random_forest',
        'confidence_threshold': 0.7
    }
    
    classifier = ErrorClassificationSystem(config)
    
    # Test error logs
    test_errors = [
        "Failed to connect to database at localhost:5432",
        "Authentication failed for user admin",
        "Permission denied: cannot write to /var/log/app.log",
        "Build failed: module 'requests' not found",
        "Container exited with code 1: out of memory",
        "HTTP 404 error: endpoint not found",
        "SSL handshake failed: certificate expired"
    ]
    
    # Classify errors
    for error_text in test_errors:
        result = classifier.classify_error(error_text)
        print(f"\nError: {error_text}")
        print(f"Category: {result.predicted_category.value}")
        print(f"Confidence: {result.confidence_score:.3f}")
        print(f"Model: {result.model_name}")
