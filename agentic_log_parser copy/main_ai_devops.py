#!/usr/bin/env python3
"""
Main AI DevOps Integration Script
Comprehensive integration of all AI DevOps modules
"""

import os
import sys
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Any

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import all our AI DevOps components
from log_ingestion_system import LogIngestionManager, LogEntry, LogSource
from nlp_preprocessing import NLPPreprocessingPipeline
from error_classification_system import ErrorClassificationSystem
from root_cause_analysis import RootCauseAnalysisEngine
from knowledge_base_integration import KnowledgeBaseManager
from anomaly_detection_system import AnomalyDetectionEngine
from trend_analysis_system import TrendAnalysisEngine
from log_storage_system import LogStorageManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_devops.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AIDevOpsOrchestrator:
    """Main orchestrator for AI DevOps workflow"""
    
    def __init__(self, config_file: str = "ai_devops_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        
        # Initialize all systems
        self.systems = {}
        self.initialize_systems()
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        import json
        
        default_config = {
            "log_ingestion": {
                "enabled": True,
                "sources": {
                    "jenkins": {"enabled": False},
                    "docker": {"enabled": True},
                    "git": {"enabled": True},
                    "system": {"enabled": False}
                },
                "collection_interval": 30
            },
            "nlp_preprocessing": {
                "enabled": True,
                "normalize_text": True,
                "extract_entities": True,
                "extract_features": True
            },
            "error_classification": {
                "enabled": True,
                "use_ml": True,
                "ml_model_type": "random_forest",
                "confidence_threshold": 0.7
            },
            "root_cause_analysis": {
                "enabled": True,
                "time_window_minutes": 30,
                "confidence_threshold": 0.6
            },
            "knowledge_base": {
                "enabled": True,
                "auto_learn": True,
                "db_path": "knowledge_base.db"
            },
            "anomaly_detection": {
                "enabled": True,
                "enable_statistical": True,
                "enable_timeseries": True,
                "enable_ml": False,
                "enable_threshold": True
            },
            "trend_analysis": {
                "enabled": True,
                "enable_lstm": False,  # Requires PyTorch
                "enable_seasonal": True,
                "enable_patterns": True,
                "forecast_horizon": 12
            },
            "storage": {
                "type": "sqlite",
                "db_path": "ai_devops_logs.db",
                "retention_days": 90
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                # Merge with defaults
                for key, value in loaded_config.items():
                    if key in default_config:
                        default_config[key].update(value)
                    else:
                        default_config[key] = value
            else:
                # Save default config
                with open(self.config_file, 'w') as f:
                    json.dump(default_config, f, indent=2)
                logger.info(f"Created default configuration file: {self.config_file}")
        except Exception as e:
            logger.error(f"Error loading config: {e}")
        
        return default_config
    
    def initialize_systems(self):
        """Initialize all AI DevOps systems"""
        logger.info("Initializing AI DevOps systems...")
        
        try:
            # 1. Storage System
            if self.config["storage"]["enabled"]:
                self.systems["storage"] = LogStorageManager(self.config["storage"])
                if self.systems["storage"].initialize():
                    logger.info("✅ Storage system initialized")
                else:
                    logger.error("❌ Storage system initialization failed")
            
            # 2. Log Ingestion
            if self.config["log_ingestion"]["enabled"]:
                self.systems["ingestion"] = LogIngestionManager()
                self.systems["ingestion"].initialize_connectors()
                logger.info("✅ Log ingestion system initialized")
            
            # 3. NLP Preprocessing
            if self.config["nlp_preprocessing"]["enabled"]:
                self.systems["nlp"] = NLPPreprocessingPipeline(self.config["nlp_preprocessing"])
                logger.info("✅ NLP preprocessing pipeline initialized")
            
            # 4. Error Classification
            if self.config["error_classification"]["enabled"]:
                self.systems["classification"] = ErrorClassificationSystem(self.config["error_classification"])
                logger.info("✅ Error classification system initialized")
            
            # 5. Root Cause Analysis
            if self.config["root_cause_analysis"]["enabled"]:
                self.systems["rca"] = RootCauseAnalysisEngine(self.config["root_cause_analysis"])
                logger.info("✅ Root cause analysis engine initialized")
            
            # 6. Knowledge Base
            if self.config["knowledge_base"]["enabled"]:
                self.systems["knowledge_base"] = KnowledgeBaseManager(self.config["knowledge_base"])
                if self.systems["knowledge_base"].initialize():
                    logger.info("✅ Knowledge base system initialized")
                else:
                    logger.error("❌ Knowledge base initialization failed")
            
            # 7. Anomaly Detection
            if self.config["anomaly_detection"]["enabled"]:
                self.systems["anomaly"] = AnomalyDetectionEngine(self.config["anomaly_detection"])
                logger.info("✅ Anomaly detection system initialized")
            
            # 8. Trend Analysis
            if self.config["trend_analysis"]["enabled"]:
                self.systems["trends"] = TrendAnalysisEngine(self.config["trend_analysis"])
                logger.info("✅ Trend analysis system initialized")
            
            logger.info(f"Successfully initialized {len(self.systems)} AI DevOps systems")
            
        except Exception as e:
            logger.error(f"Error initializing systems: {e}")
            raise
    
    def start_monitoring(self):
        """Start the AI DevOps monitoring workflow"""
        logger.info("Starting AI DevOps monitoring workflow...")
        
        try:
            # Start log collection
            if "ingestion" in self.systems:
                self.systems["ingestion"].start_collection()
                logger.info("📡 Log collection started")
            
            # Main monitoring loop
            import time
            while True:
                try:
                    self.process_logs()
                    time.sleep(self.config["log_ingestion"]["collection_interval"])
                except KeyboardInterrupt:
                    logger.info("Monitoring stopped by user")
                    break
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    time.sleep(10)  # Wait before retrying
                    
        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")
        finally:
            self.shutdown()
    
    def process_logs(self):
        """Process collected logs through the AI pipeline"""
        try:
            # Get recent logs
            if "ingestion" not in self.systems:
                return
            
            recent_logs = self.systems["ingestion"].get_recent_logs(50)
            
            if not recent_logs:
                return
            
            logger.info(f"Processing {len(recent_logs)} recent logs")
            
            for log_entry in recent_logs:
                try:
                    # Store raw log
                    if "storage" in self.systems:
                        self.systems["storage"].store_log(log_entry)
                    
                    # Skip non-error logs for detailed processing
                    if log_entry.level not in ["ERROR", "WARNING"]:
                        continue
                    
                    # NLP Preprocessing
                    processed_log = None
                    if "nlp" in self.systems:
                        processed_log = self.systems["nlp"].process_log_entry(
                            log_entry.raw_content,
                            {
                                "source": log_entry.source.value,
                                "level": log_entry.level,
                                "timestamp": log_entry.timestamp
                            }
                        )
                        
                        if "storage" in self.systems:
                            self.systems["storage"].store_processed_log(processed_log)
                    
                    # Error Classification
                    classification_result = None
                    if "classification" in self.systems and log_entry.level == "ERROR":
                        classification_result = self.systems["classification"].classify_error(
                            log_entry.message,
                            {"source": log_entry.source.value, "level": log_entry.level}
                        )
                        
                        logger.info(f"Classified error as: {classification_result.predicted_category.value} "
                                  f"(confidence: {classification_result.confidence_score:.3f})")
                    
                    # Root Cause Analysis
                    if ("rca" in self.systems and classification_result and 
                        classification_result.confidence_score > 0.7):
                        
                        hypotheses = self.systems["rca"].analyze_root_cause(
                            classification_result,
                            [processed_log] if processed_log else [],
                            {"error_rate": 5.0, "cpu_usage": 75.0}  # Mock system metrics
                        )
                        
                        if hypotheses:
                            logger.info(f"Generated {len(hypotheses)} root cause hypotheses")
                            for hypothesis in hypotheses[:2]:  # Log top 2
                                logger.info(f"  - {hypothesis.root_cause} "
                                          f"(confidence: {hypothesis.confidence_score:.3f})")
                    
                    # Get knowledge base suggestions
                    if ("knowledge_base" in self.systems and classification_result and 
                        classification_result.confidence_score > 0.6):
                        
                        suggestions = self.systems["knowledge_base"].get_intelligent_suggestions(
                            classification_result,
                            log_entry.message
                        )
                        
                        if suggestions:
                            logger.info(f"Found {len(suggestions)} knowledge base suggestions")
                    
                except Exception as e:
                    logger.warning(f"Error processing log entry: {e}")
            
            # Anomaly Detection
            if "anomaly" in self.systems and len(recent_logs) > 5:
                anomalies = self.systems["anomaly"].detect_anomalies(
                    recent_logs,
                    {"error_rate": 8.0, "cpu_usage": 80.0, "memory_usage": 75.0}
                )
                
                if anomalies:
                    logger.warning(f"Detected {len(anomalies)} anomalies")
                    for anomaly in anomalies[:3]:  # Log top 3
                        logger.warning(f"  - {anomaly.anomaly_type.value}: {anomaly.description}")
            
            # Trend Analysis (less frequent)
            if "trends" in self.systems and len(recent_logs) > 20:
                timestamps = [log.timestamp for log in recent_logs]
                values = [1 for _ in recent_logs]  # Simple count-based trend
                
                trend_result = self.systems["trends"].analyze_trends(
                    timestamps, values, "log_volume"
                )
                
                logger.info(f"Trend analysis: {trend_result.trend_type.value} "
                          f"({trend_result.trend_direction}, confidence: {trend_result.confidence:.3f})")
                
        except Exception as e:
            logger.error(f"Error in log processing: {e}")
    
    def run_dashboard(self):
        """Run the Streamlit dashboard"""
        logger.info("Starting AI DevOps dashboard...")
        
        try:
            import subprocess
            import sys
            
            # Run the dashboard
            dashboard_path = os.path.join(os.path.dirname(__file__), "ai_devops_dashboard.py")
            subprocess.run([sys.executable, "-m", "streamlit", "run", dashboard_path])
            
        except Exception as e:
            logger.error(f"Error running dashboard: {e}")
    
    def shutdown(self):
        """Shutdown all systems gracefully"""
        logger.info("Shutting down AI DevOps systems...")
        
        try:
            if "ingestion" in self.systems:
                self.systems["ingestion"].stop_collection()
            
            if "storage" in self.systems:
                self.systems["storage"].shutdown()
            
            if "knowledge_base" in self.systems:
                # Perform any cleanup
                pass
            
            logger.info("All systems shut down successfully")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="AI DevOps Workflow System")
    parser.add_argument("--mode", choices=["monitor", "dashboard", "test"], 
                       default="monitor", help="Operation mode")
    parser.add_argument("--config", default="ai_devops_config.json", 
                       help="Configuration file path")
    
    args = parser.parse_args()
    
    try:
        orchestrator = AIDevOpsOrchestrator(args.config)
        
        if args.mode == "monitor":
            orchestrator.start_monitoring()
        elif args.mode == "dashboard":
            orchestrator.run_dashboard()
        elif args.mode == "test":
            logger.info("Running system tests...")
            # Add test functionality here
            logger.info("All systems initialized successfully!")
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
