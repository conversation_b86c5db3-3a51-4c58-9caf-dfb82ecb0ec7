#!/usr/bin/env python3
"""
AI DevOps Monitoring Dashboard
Interactive dashboard for real-time anomaly visualization, alerting, and trend analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import time
import threading

# Import our AI DevOps components
from log_ingestion_system import Log<PERSON>ngestion<PERSON>anager, LogEntry, LogSource
from nlp_preprocessing import NLPPreprocessingPipeline
from error_classification_system import ErrorClassificationSystem, ErrorCategory
from root_cause_analysis import RootCauseAnalysisEngine
from knowledge_base_integration import KnowledgeBaseManager
from anomaly_detection_system import AnomalyDetectionEngine
from trend_analysis_system import TrendAnalysisEngine
from log_storage_system import LogStorageManager, LogQuery

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIDevOpsDashboard:
    """Main AI DevOps Dashboard class"""
    
    def __init__(self):
        self.initialize_systems()
        self.setup_page_config()
        
    def setup_page_config(self):
        """Configure Streamlit page"""
        st.set_page_config(
            page_title="AI DevOps Monitoring Dashboard",
            page_icon="🤖",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        .metric-card {
            background-color: #f0f2f6;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid #1f77b4;
        }
        .alert-critical {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        .alert-warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        .alert-info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_systems(self):
        """Initialize all AI DevOps systems"""
        try:
            # Initialize core systems
            self.log_ingestion = LogIngestionManager()
            self.nlp_pipeline = NLPPreprocessingPipeline()
            self.error_classifier = ErrorClassificationSystem()
            self.rca_engine = RootCauseAnalysisEngine()
            self.knowledge_base = KnowledgeBaseManager()
            self.anomaly_detector = AnomalyDetectionEngine()
            self.trend_analyzer = TrendAnalysisEngine()
            self.storage_manager = LogStorageManager()
            
            # Initialize systems
            self.log_ingestion.initialize_connectors()
            self.knowledge_base.initialize()
            self.storage_manager.initialize()
            
            # Start log collection
            self.log_ingestion.start_collection()
            
            logger.info("All AI DevOps systems initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing systems: {e}")
            st.error(f"Failed to initialize AI DevOps systems: {e}")
    
    def render_header(self):
        """Render dashboard header"""
        st.markdown('<h1 class="main-header">🤖 AI DevOps Monitoring Dashboard</h1>', 
                   unsafe_allow_html=True)
        
        # Status indicators
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Log Ingestion", "Active", delta="✅")
        with col2:
            st.metric("AI Classification", "Running", delta="🧠")
        with col3:
            st.metric("Anomaly Detection", "Monitoring", delta="🔍")
        with col4:
            st.metric("Trend Analysis", "Learning", delta="📈")
    
    def render_sidebar(self):
        """Render sidebar with controls"""
        st.sidebar.title("🎛️ Dashboard Controls")
        
        # Time range selector
        st.sidebar.subheader("Time Range")
        time_range = st.sidebar.selectbox(
            "Select time range:",
            ["Last 1 Hour", "Last 6 Hours", "Last 24 Hours", "Last 7 Days", "Last 30 Days"]
        )
        
        # Log source filter
        st.sidebar.subheader("Log Sources")
        selected_sources = st.sidebar.multiselect(
            "Filter by source:",
            [source.value for source in LogSource],
            default=[LogSource.APPLICATION.value, LogSource.DOCKER.value]
        )
        
        # Alert level filter
        st.sidebar.subheader("Alert Levels")
        alert_levels = st.sidebar.multiselect(
            "Show alerts:",
            ["Critical", "High", "Medium", "Low"],
            default=["Critical", "High", "Medium"]
        )
        
        # Auto-refresh
        st.sidebar.subheader("Auto-Refresh")
        auto_refresh = st.sidebar.checkbox("Enable auto-refresh", value=True)
        refresh_interval = st.sidebar.slider("Refresh interval (seconds)", 10, 300, 30)
        
        # Manual refresh button
        if st.sidebar.button("🔄 Refresh Now"):
            st.experimental_rerun()
        
        return {
            'time_range': time_range,
            'selected_sources': selected_sources,
            'alert_levels': alert_levels,
            'auto_refresh': auto_refresh,
            'refresh_interval': refresh_interval
        }
    
    def get_time_range_delta(self, time_range: str) -> timedelta:
        """Convert time range string to timedelta"""
        time_map = {
            "Last 1 Hour": timedelta(hours=1),
            "Last 6 Hours": timedelta(hours=6),
            "Last 24 Hours": timedelta(days=1),
            "Last 7 Days": timedelta(days=7),
            "Last 30 Days": timedelta(days=30)
        }
        return time_map.get(time_range, timedelta(hours=1))
    
    def render_overview_metrics(self, filters: Dict[str, Any]):
        """Render overview metrics section"""
        st.subheader("📊 System Overview")
        
        # Get recent logs
        time_delta = self.get_time_range_delta(filters['time_range'])
        start_time = datetime.now() - time_delta
        
        query = LogQuery(
            start_time=start_time,
            sources=[LogSource(source) for source in filters['selected_sources']],
            limit=10000
        )
        
        recent_logs = self.storage_manager.search_logs(query)
        
        # Calculate metrics
        total_logs = len(recent_logs)
        error_logs = len([log for log in recent_logs if log['level'] == 'ERROR'])
        warning_logs = len([log for log in recent_logs if log['level'] == 'WARNING'])
        error_rate = (error_logs / total_logs * 100) if total_logs > 0 else 0
        
        # Display metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Total Logs",
                f"{total_logs:,}",
                delta=f"+{total_logs//10}" if total_logs > 0 else "0"
            )
        
        with col2:
            st.metric(
                "Error Rate",
                f"{error_rate:.1f}%",
                delta=f"{'↑' if error_rate > 5 else '↓'} {error_rate:.1f}%",
                delta_color="inverse"
            )
        
        with col3:
            st.metric(
                "Errors",
                f"{error_logs:,}",
                delta=f"+{error_logs//5}" if error_logs > 0 else "0"
            )
        
        with col4:
            st.metric(
                "Warnings",
                f"{warning_logs:,}",
                delta=f"+{warning_logs//3}" if warning_logs > 0 else "0"
            )
    
    def render_real_time_alerts(self, filters: Dict[str, Any]):
        """Render real-time alerts section"""
        st.subheader("🚨 Real-time Alerts")
        
        # Get recent logs for anomaly detection
        recent_log_entries = self.log_ingestion.get_recent_logs(100)
        
        if recent_log_entries:
            # Detect anomalies
            system_metrics = {
                'error_rate': 8.5,  # Mock metrics
                'cpu_usage': 75.0,
                'memory_usage': 82.0,
                'log_volume': 1200
            }
            
            anomalies = self.anomaly_detector.detect_anomalies(recent_log_entries, system_metrics)
            
            # Filter anomalies by alert level
            filtered_anomalies = [
                anomaly for anomaly in anomalies
                if anomaly.severity.title() in filters['alert_levels']
            ]
            
            if filtered_anomalies:
                for anomaly in filtered_anomalies[:5]:  # Show top 5 alerts
                    severity_class = f"alert-{anomaly.severity}"
                    
                    st.markdown(f"""
                    <div class="{severity_class}">
                        <strong>{anomaly.severity.upper()}: {anomaly.anomaly_type.value.replace('_', ' ').title()}</strong><br>
                        {anomaly.description}<br>
                        <small>Score: {anomaly.anomaly_score:.3f} | Confidence: {anomaly.confidence:.3f} | 
                        Time: {anomaly.timestamp.strftime('%H:%M:%S')}</small>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Show suggested actions
                    if anomaly.suggested_actions:
                        with st.expander("Suggested Actions"):
                            for action in anomaly.suggested_actions:
                                st.write(f"• {action}")
            else:
                st.info("No alerts matching current filters")
        else:
            st.info("No recent log data available")
    
    def render_error_classification(self, filters: Dict[str, Any]):
        """Render error classification section"""
        st.subheader("🧠 AI Error Classification")
        
        # Get recent error logs
        time_delta = self.get_time_range_delta(filters['time_range'])
        start_time = datetime.now() - time_delta
        
        query = LogQuery(
            start_time=start_time,
            levels=["ERROR"],
            limit=50
        )
        
        error_logs = self.storage_manager.search_logs(query)
        
        if error_logs:
            # Classify recent errors
            classifications = []
            
            for log in error_logs[:10]:  # Classify top 10 errors
                try:
                    result = self.error_classifier.classify_error(
                        log['message'],
                        {'source': log['source'], 'level': log['level']}
                    )
                    classifications.append({
                        'timestamp': log['timestamp'],
                        'message': log['message'][:100] + "..." if len(log['message']) > 100 else log['message'],
                        'category': result.predicted_category.value,
                        'confidence': result.confidence_score,
                        'source': log['source']
                    })
                except Exception as e:
                    logger.warning(f"Error classifying log: {e}")
            
            if classifications:
                # Display classification results
                df = pd.DataFrame(classifications)
                
                # Category distribution chart
                col1, col2 = st.columns([1, 1])
                
                with col1:
                    category_counts = df['category'].value_counts()
                    fig = px.pie(
                        values=category_counts.values,
                        names=category_counts.index,
                        title="Error Categories Distribution"
                    )
                    st.plotly_chart(fig, use_container_width=True)
                
                with col2:
                    # Confidence distribution
                    fig = px.histogram(
                        df,
                        x='confidence',
                        title="Classification Confidence Distribution",
                        nbins=10
                    )
                    st.plotly_chart(fig, use_container_width=True)
                
                # Classification table
                st.subheader("Recent Error Classifications")
                st.dataframe(
                    df[['timestamp', 'message', 'category', 'confidence', 'source']],
                    use_container_width=True
                )
        else:
            st.info("No error logs found in the selected time range")
    
    def render_trend_analysis(self, filters: Dict[str, Any]):
        """Render trend analysis section"""
        st.subheader("📈 Trend Analysis")
        
        # Get time series data
        time_delta = self.get_time_range_delta(filters['time_range'])
        start_time = datetime.now() - time_delta
        
        query = LogQuery(
            start_time=start_time,
            limit=10000
        )
        
        logs = self.storage_manager.search_logs(query)
        
        if logs:
            # Create time series data
            df = pd.DataFrame(logs)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Hourly log volume
            hourly_volume = df.set_index('timestamp').resample('H').size()
            
            # Error rate over time
            hourly_errors = df[df['level'] == 'ERROR'].set_index('timestamp').resample('H').size()
            hourly_total = df.set_index('timestamp').resample('H').size()
            error_rate_series = (hourly_errors / hourly_total * 100).fillna(0)
            
            # Perform trend analysis
            if len(hourly_volume) > 10:
                trend_result = self.trend_analyzer.analyze_trends(
                    hourly_volume.index.tolist(),
                    hourly_volume.values.tolist(),
                    "log_volume"
                )
                
                # Display trend analysis results
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    # Volume trend chart
                    fig = make_subplots(
                        rows=2, cols=1,
                        subplot_titles=('Log Volume Over Time', 'Error Rate Over Time'),
                        vertical_spacing=0.1
                    )
                    
                    # Log volume
                    fig.add_trace(
                        go.Scatter(
                            x=hourly_volume.index,
                            y=hourly_volume.values,
                            mode='lines+markers',
                            name='Log Volume',
                            line=dict(color='blue')
                        ),
                        row=1, col=1
                    )
                    
                    # Add forecast if available
                    if trend_result.forecast_values:
                        fig.add_trace(
                            go.Scatter(
                                x=trend_result.forecast_timestamps,
                                y=trend_result.forecast_values,
                                mode='lines',
                                name='Forecast',
                                line=dict(color='red', dash='dash')
                            ),
                            row=1, col=1
                        )
                    
                    # Error rate
                    fig.add_trace(
                        go.Scatter(
                            x=error_rate_series.index,
                            y=error_rate_series.values,
                            mode='lines+markers',
                            name='Error Rate %',
                            line=dict(color='orange')
                        ),
                        row=2, col=1
                    )
                    
                    fig.update_layout(height=600, title_text="System Trends")
                    st.plotly_chart(fig, use_container_width=True)
                
                with col2:
                    # Trend analysis summary
                    st.markdown("**Trend Analysis Summary**")
                    st.write(f"**Type:** {trend_result.trend_type.value.title()}")
                    st.write(f"**Direction:** {trend_result.trend_direction.title()}")
                    st.write(f"**Confidence:** {trend_result.confidence:.1%}")
                    st.write(f"**Change Rate:** {trend_result.change_rate:.1f}%")
                    st.write(f"**Seasonality:** {'Yes' if trend_result.seasonality_detected else 'No'}")
                    
                    st.markdown("**Description**")
                    st.write(trend_result.description)
                    
                    if trend_result.forecast_values:
                        st.markdown("**Next Hour Forecast**")
                        st.write(f"{trend_result.forecast_values[0]:.0f} logs")
        else:
            st.info("No data available for trend analysis")
    
    def render_root_cause_analysis(self, filters: Dict[str, Any]):
        """Render root cause analysis section"""
        st.subheader("🔍 Root Cause Analysis")
        
        # Get recent error logs for RCA
        query = LogQuery(
            start_time=datetime.now() - timedelta(hours=1),
            levels=["ERROR"],
            limit=10
        )
        
        error_logs = self.storage_manager.search_logs(query)
        
        if error_logs:
            # Select an error for analysis
            selected_error = st.selectbox(
                "Select error for root cause analysis:",
                options=range(len(error_logs)),
                format_func=lambda x: f"{error_logs[x]['timestamp']} - {error_logs[x]['message'][:50]}..."
            )
            
            if selected_error is not None:
                error_log = error_logs[selected_error]
                
                # Classify the error
                classification = self.error_classifier.classify_error(
                    error_log['message'],
                    {'source': error_log['source'], 'level': error_log['level']}
                )
                
                # Process log for RCA
                processed_log = self.nlp_pipeline.process_log_entry(
                    error_log['message'],
                    {'source': error_log['source'], 'level': error_log['level']}
                )
                
                # Perform root cause analysis
                hypotheses = self.rca_engine.analyze_root_cause(
                    classification,
                    [processed_log]
                )
                
                # Get intelligent suggestions from knowledge base
                suggestions = self.knowledge_base.get_intelligent_suggestions(
                    classification,
                    error_log['message']
                )
                
                # Display results
                col1, col2 = st.columns([1, 1])
                
                with col1:
                    st.markdown("**Error Classification**")
                    st.write(f"Category: {classification.predicted_category.value}")
                    st.write(f"Confidence: {classification.confidence_score:.1%}")
                    
                    st.markdown("**Root Cause Hypotheses**")
                    for i, hypothesis in enumerate(hypotheses[:3], 1):
                        with st.expander(f"Hypothesis {i}: {hypothesis.root_cause}"):
                            st.write(f"**Confidence:** {hypothesis.confidence_score:.1%}")
                            st.write(f"**Reasoning:** {hypothesis.reasoning}")
                            st.write("**Evidence:**")
                            for evidence in hypothesis.evidence_chain:
                                st.write(f"• {evidence}")
                            st.write("**Suggested Actions:**")
                            for action in hypothesis.suggested_actions:
                                st.write(f"• {action}")
                
                with col2:
                    st.markdown("**Knowledge Base Suggestions**")
                    for i, suggestion in enumerate(suggestions[:3], 1):
                        with st.expander(f"Suggestion {i}: {suggestion['title']}"):
                            st.write(f"**Type:** {suggestion['type']}")
                            st.write(f"**Confidence:** {suggestion['confidence']:.1%}")
                            st.write(f"**Description:** {suggestion['description']}")
                            if 'resolution_steps' in suggestion:
                                st.write("**Resolution Steps:**")
                                for step in suggestion['resolution_steps']:
                                    st.write(f"• {step}")
        else:
            st.info("No recent errors available for root cause analysis")
    
    def run(self):
        """Run the dashboard"""
        try:
            # Render header
            self.render_header()
            
            # Render sidebar and get filters
            filters = self.render_sidebar()
            
            # Main dashboard tabs
            tab1, tab2, tab3, tab4, tab5 = st.tabs([
                "📊 Overview", "🚨 Alerts", "🧠 Classification", 
                "📈 Trends", "🔍 Root Cause"
            ])
            
            with tab1:
                self.render_overview_metrics(filters)
            
            with tab2:
                self.render_real_time_alerts(filters)
            
            with tab3:
                self.render_error_classification(filters)
            
            with tab4:
                self.render_trend_analysis(filters)
            
            with tab5:
                self.render_root_cause_analysis(filters)
            
            # Auto-refresh
            if filters['auto_refresh']:
                time.sleep(filters['refresh_interval'])
                st.experimental_rerun()
                
        except Exception as e:
            logger.error(f"Dashboard error: {e}")
            st.error(f"Dashboard error: {e}")

# Main execution
if __name__ == "__main__":
    try:
        dashboard = AIDevOpsDashboard()
        dashboard.run()
    except Exception as e:
        st.error(f"Failed to start dashboard: {e}")
        logger.error(f"Dashboard startup error: {e}")
