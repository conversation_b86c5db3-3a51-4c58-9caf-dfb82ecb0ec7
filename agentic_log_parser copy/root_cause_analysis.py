#!/usr/bin/env python3
"""
Root Cause Analysis Engine
Intelligent root cause analysis using knowledge graphs, pattern matching, and causal inference
"""

import json
import logging
import networkx as nx
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import numpy as np
import pandas as pd
from collections import defaultdict, Counter

# Import our components
from error_classification_system import ErrorCategory, ErrorClassificationResult
from nlp_preprocessing import ProcessedLogEntry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CausalRelationType(Enum):
    """Types of causal relationships"""
    DIRECT_CAUSE = "direct_cause"
    INDIRECT_CAUSE = "indirect_cause"
    CORRELATION = "correlation"
    TEMPORAL_SEQUENCE = "temporal_sequence"
    DEPENDENCY = "dependency"
    CONFIGURATION_ISSUE = "configuration_issue"
    RESOURCE_CONSTRAINT = "resource_constraint"

@dataclass
class CausalRelation:
    """Represents a causal relationship between events/errors"""
    source_event: str
    target_event: str
    relation_type: CausalRelationType
    confidence: float
    evidence: List[str]
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class RootCauseHypothesis:
    """A hypothesis about the root cause of an error"""
    root_cause: str
    confidence_score: float
    evidence_chain: List[str]
    affected_components: List[str]
    suggested_actions: List[str]
    reasoning: str
    timestamp: datetime

class KnowledgeGraph:
    """Knowledge graph for storing causal relationships and system dependencies"""
    
    def __init__(self):
        self.graph = nx.DiGraph()
        self.causal_relations = []
        self.component_dependencies = {}
        self.error_patterns = {}
        
    def add_component(self, component_name: str, component_type: str, metadata: Dict[str, Any] = None):
        """Add a system component to the knowledge graph"""
        self.graph.add_node(
            component_name,
            type=component_type,
            metadata=metadata or {}
        )
    
    def add_dependency(self, source: str, target: str, dependency_type: str, strength: float = 1.0):
        """Add a dependency relationship between components"""
        self.graph.add_edge(
            source, target,
            type=dependency_type,
            strength=strength
        )
    
    def add_causal_relation(self, relation: CausalRelation):
        """Add a causal relationship to the knowledge graph"""
        self.causal_relations.append(relation)
        
        # Add to graph if nodes exist
        if relation.source_event in self.graph and relation.target_event in self.graph:
            self.graph.add_edge(
                relation.source_event,
                relation.target_event,
                type=relation.relation_type.value,
                confidence=relation.confidence,
                evidence=relation.evidence
            )
    
    def find_potential_causes(self, target_component: str, max_depth: int = 3) -> List[Tuple[str, float]]:
        """Find potential causes for a component failure"""
        potential_causes = []
        
        try:
            # Find all paths to the target component
            for node in self.graph.nodes():
                if node != target_component and nx.has_path(self.graph, node, target_component):
                    try:
                        path = nx.shortest_path(self.graph, node, target_component)
                        if len(path) <= max_depth + 1:
                            # Calculate confidence based on path length and edge weights
                            confidence = 1.0 / len(path)
                            for i in range(len(path) - 1):
                                edge_data = self.graph.get_edge_data(path[i], path[i + 1])
                                if edge_data and 'confidence' in edge_data:
                                    confidence *= edge_data['confidence']
                            
                            potential_causes.append((node, confidence))
                    except nx.NetworkXNoPath:
                        continue
        except Exception as e:
            logger.warning(f"Error finding potential causes: {e}")
        
        # Sort by confidence
        potential_causes.sort(key=lambda x: x[1], reverse=True)
        return potential_causes
    
    def get_component_dependencies(self, component: str) -> List[str]:
        """Get all components that the given component depends on"""
        dependencies = []
        try:
            if component in self.graph:
                dependencies = list(self.graph.successors(component))
        except Exception as e:
            logger.warning(f"Error getting dependencies for {component}: {e}")
        return dependencies
    
    def get_dependent_components(self, component: str) -> List[str]:
        """Get all components that depend on the given component"""
        dependents = []
        try:
            if component in self.graph:
                dependents = list(self.graph.predecessors(component))
        except Exception as e:
            logger.warning(f"Error getting dependents for {component}: {e}")
        return dependents

class TemporalAnalyzer:
    """Analyzes temporal patterns in log sequences to identify causal relationships"""
    
    def __init__(self, time_window_minutes: int = 30):
        self.time_window = timedelta(minutes=time_window_minutes)
        
    def find_temporal_sequences(self, log_entries: List[Tuple[datetime, str, str]]) -> List[Tuple[str, str, float]]:
        """Find temporal sequences that might indicate causal relationships"""
        sequences = []
        
        # Sort by timestamp
        sorted_logs = sorted(log_entries, key=lambda x: x[0])
        
        for i in range(len(sorted_logs)):
            current_time, current_event, current_component = sorted_logs[i]
            
            # Look for events within the time window
            for j in range(i + 1, len(sorted_logs)):
                next_time, next_event, next_component = sorted_logs[j]
                
                if next_time - current_time > self.time_window:
                    break
                
                # Calculate temporal confidence based on time proximity
                time_diff = (next_time - current_time).total_seconds()
                temporal_confidence = max(0, 1 - (time_diff / self.time_window.total_seconds()))
                
                sequences.append((current_event, next_event, temporal_confidence))
        
        return sequences
    
    def analyze_error_cascades(self, error_logs: List[Tuple[datetime, str, str, ErrorCategory]]) -> List[Dict[str, Any]]:
        """Analyze error cascades to identify propagation patterns"""
        cascades = []
        
        # Group errors by time windows
        time_windows = defaultdict(list)
        
        for timestamp, error_msg, component, category in error_logs:
            window_key = timestamp.replace(second=0, microsecond=0)
            time_windows[window_key].append((timestamp, error_msg, component, category))
        
        # Analyze each time window
        for window_time, errors in time_windows.items():
            if len(errors) > 1:
                # Sort errors within window
                errors.sort(key=lambda x: x[0])
                
                cascade = {
                    'window_start': window_time,
                    'error_sequence': [],
                    'potential_root_cause': None,
                    'affected_components': set()
                }
                
                for timestamp, error_msg, component, category in errors:
                    cascade['error_sequence'].append({
                        'timestamp': timestamp,
                        'error': error_msg,
                        'component': component,
                        'category': category.value
                    })
                    cascade['affected_components'].add(component)
                
                # First error might be the root cause
                if cascade['error_sequence']:
                    cascade['potential_root_cause'] = cascade['error_sequence'][0]
                
                cascade['affected_components'] = list(cascade['affected_components'])
                cascades.append(cascade)
        
        return cascades

class PatternMatcher:
    """Matches known error patterns and their typical root causes"""
    
    def __init__(self):
        self.known_patterns = {
            # Database-related patterns
            "database_connection_pool_exhausted": {
                "patterns": ["connection pool exhausted", "too many connections"],
                "root_causes": [
                    "High application load without proper connection pooling",
                    "Connection leaks in application code",
                    "Database server resource constraints"
                ],
                "suggested_actions": [
                    "Increase connection pool size",
                    "Review application code for connection leaks",
                    "Scale database resources",
                    "Implement connection pooling best practices"
                ]
            },
            
            # Network-related patterns
            "network_timeout_cascade": {
                "patterns": ["connection timeout", "read timeout", "network unreachable"],
                "root_causes": [
                    "Network infrastructure issues",
                    "Service overload causing slow responses",
                    "Firewall or security group misconfiguration"
                ],
                "suggested_actions": [
                    "Check network connectivity",
                    "Review service performance metrics",
                    "Verify firewall and security group settings",
                    "Implement circuit breaker pattern"
                ]
            },
            
            # Resource-related patterns
            "memory_exhaustion": {
                "patterns": ["out of memory", "memory exhausted", "heap space"],
                "root_causes": [
                    "Memory leaks in application",
                    "Insufficient memory allocation",
                    "Large data processing without proper streaming"
                ],
                "suggested_actions": [
                    "Analyze memory usage patterns",
                    "Increase memory allocation",
                    "Review code for memory leaks",
                    "Implement memory-efficient data processing"
                ]
            },
            
            # Build and deployment patterns
            "build_dependency_failure": {
                "patterns": ["dependency not found", "package not found", "module not found"],
                "root_causes": [
                    "Missing or incorrect dependency declarations",
                    "Repository or registry connectivity issues",
                    "Version conflicts between dependencies"
                ],
                "suggested_actions": [
                    "Verify dependency declarations",
                    "Check repository connectivity",
                    "Resolve version conflicts",
                    "Update dependency management configuration"
                ]
            }
        }
    
    def match_patterns(self, error_text: str, error_category: ErrorCategory) -> Optional[Dict[str, Any]]:
        """Match error text against known patterns"""
        error_text_lower = error_text.lower()
        
        for pattern_name, pattern_data in self.known_patterns.items():
            for pattern in pattern_data["patterns"]:
                if pattern.lower() in error_text_lower:
                    return {
                        "pattern_name": pattern_name,
                        "matched_pattern": pattern,
                        "root_causes": pattern_data["root_causes"],
                        "suggested_actions": pattern_data["suggested_actions"],
                        "confidence": 0.8  # High confidence for exact pattern matches
                    }
        
        return None

class CausalInferenceEngine:
    """Performs causal inference to identify root causes"""
    
    def __init__(self):
        self.correlation_threshold = 0.7
        self.temporal_threshold = 0.6
        
    def analyze_correlations(self, events: List[Dict[str, Any]]) -> List[Tuple[str, str, float]]:
        """Analyze correlations between different types of events"""
        correlations = []
        
        # Create event frequency matrix
        event_types = list(set(event['type'] for event in events))
        time_buckets = defaultdict(lambda: defaultdict(int))
        
        # Bucket events by time (e.g., 5-minute intervals)
        for event in events:
            timestamp = event['timestamp']
            bucket = timestamp.replace(minute=(timestamp.minute // 5) * 5, second=0, microsecond=0)
            time_buckets[bucket][event['type']] += 1
        
        # Calculate correlations between event types
        for i, event_type1 in enumerate(event_types):
            for j, event_type2 in enumerate(event_types[i+1:], i+1):
                correlation = self._calculate_correlation(time_buckets, event_type1, event_type2)
                if correlation > self.correlation_threshold:
                    correlations.append((event_type1, event_type2, correlation))
        
        return correlations
    
    def _calculate_correlation(self, time_buckets: Dict, event_type1: str, event_type2: str) -> float:
        """Calculate correlation coefficient between two event types"""
        values1 = []
        values2 = []
        
        for bucket_time in time_buckets:
            values1.append(time_buckets[bucket_time][event_type1])
            values2.append(time_buckets[bucket_time][event_type2])
        
        if len(values1) < 2:
            return 0.0
        
        try:
            correlation_matrix = np.corrcoef(values1, values2)
            return abs(correlation_matrix[0, 1]) if not np.isnan(correlation_matrix[0, 1]) else 0.0
        except:
            return 0.0
    
    def infer_causal_chains(self, events: List[Dict[str, Any]], 
                           correlations: List[Tuple[str, str, float]]) -> List[List[str]]:
        """Infer causal chains from events and correlations"""
        causal_chains = []
        
        # Build a graph of correlations
        correlation_graph = nx.DiGraph()
        for event1, event2, correlation in correlations:
            correlation_graph.add_edge(event1, event2, weight=correlation)
        
        # Find paths that could represent causal chains
        try:
            for node in correlation_graph.nodes():
                # Find all simple paths from this node
                for target in correlation_graph.nodes():
                    if node != target:
                        try:
                            paths = list(nx.all_simple_paths(correlation_graph, node, target, cutoff=4))
                            for path in paths:
                                if len(path) > 2:  # Only consider chains with multiple steps
                                    causal_chains.append(path)
                        except nx.NetworkXNoPath:
                            continue
        except Exception as e:
            logger.warning(f"Error inferring causal chains: {e}")
        
        return causal_chains

class RootCauseAnalysisEngine:
    """Main root cause analysis engine"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize components
        self.knowledge_graph = KnowledgeGraph()
        self.temporal_analyzer = TemporalAnalyzer(
            time_window_minutes=self.config.get('time_window_minutes', 30)
        )
        self.pattern_matcher = PatternMatcher()
        self.causal_inference = CausalInferenceEngine()
        
        # Configuration
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
        self.max_hypotheses = self.config.get('max_hypotheses', 5)
        
        # Initialize with basic system knowledge
        self._initialize_system_knowledge()
    
    def _initialize_system_knowledge(self):
        """Initialize the knowledge graph with basic system components and dependencies"""
        # Add common system components
        components = [
            ("web_server", "service"),
            ("application_server", "service"),
            ("database", "service"),
            ("load_balancer", "infrastructure"),
            ("cache", "service"),
            ("message_queue", "service"),
            ("file_system", "infrastructure"),
            ("network", "infrastructure")
        ]
        
        for component, comp_type in components:
            self.knowledge_graph.add_component(component, comp_type)
        
        # Add common dependencies
        dependencies = [
            ("web_server", "application_server", "service_dependency"),
            ("application_server", "database", "data_dependency"),
            ("application_server", "cache", "performance_dependency"),
            ("application_server", "message_queue", "async_dependency"),
            ("database", "file_system", "storage_dependency"),
            ("web_server", "network", "network_dependency"),
            ("load_balancer", "web_server", "traffic_dependency")
        ]
        
        for source, target, dep_type in dependencies:
            self.knowledge_graph.add_dependency(source, target, dep_type)
    
    def analyze_root_cause(self, 
                          error_classification: ErrorClassificationResult,
                          log_context: List[ProcessedLogEntry],
                          system_metrics: Dict[str, Any] = None) -> List[RootCauseHypothesis]:
        """Perform comprehensive root cause analysis"""
        
        hypotheses = []
        
        # 1. Pattern-based analysis
        pattern_match = self.pattern_matcher.match_patterns(
            log_context[0].original_text if log_context else "",
            error_classification.predicted_category
        )
        
        if pattern_match:
            hypothesis = RootCauseHypothesis(
                root_cause=pattern_match["pattern_name"],
                confidence_score=pattern_match["confidence"],
                evidence_chain=[pattern_match["matched_pattern"]],
                affected_components=["unknown"],
                suggested_actions=pattern_match["suggested_actions"],
                reasoning=f"Matched known pattern: {pattern_match['pattern_name']}",
                timestamp=datetime.now()
            )
            hypotheses.append(hypothesis)
        
        # 2. Temporal analysis
        if len(log_context) > 1:
            temporal_events = [
                (log.timestamp, log.original_text, log.source)
                for log in log_context
            ]
            
            sequences = self.temporal_analyzer.find_temporal_sequences(temporal_events)
            
            if sequences:
                # Create hypothesis based on temporal sequence
                first_event = sequences[0][0] if sequences else "Unknown"
                hypothesis = RootCauseHypothesis(
                    root_cause=f"Temporal sequence starting with: {first_event[:100]}...",
                    confidence_score=max([seq[2] for seq in sequences]) if sequences else 0.3,
                    evidence_chain=[seq[0] for seq in sequences[:3]],
                    affected_components=list(set([log.source for log in log_context])),
                    suggested_actions=["Investigate the temporal sequence of events"],
                    reasoning="Based on temporal analysis of log sequence",
                    timestamp=datetime.now()
                )
                hypotheses.append(hypothesis)
        
        # 3. Knowledge graph analysis
        affected_component = log_context[0].source if log_context else "unknown"
        potential_causes = self.knowledge_graph.find_potential_causes(affected_component)
        
        for cause, confidence in potential_causes[:3]:  # Top 3 potential causes
            hypothesis = RootCauseHypothesis(
                root_cause=f"Dependency failure in {cause}",
                confidence_score=confidence,
                evidence_chain=[f"Component dependency: {cause} -> {affected_component}"],
                affected_components=[cause, affected_component],
                suggested_actions=[
                    f"Check health of {cause}",
                    f"Verify connectivity between {cause} and {affected_component}",
                    "Review dependency configuration"
                ],
                reasoning=f"Knowledge graph indicates {cause} as potential cause",
                timestamp=datetime.now()
            )
            hypotheses.append(hypothesis)
        
        # 4. System metrics analysis (if available)
        if system_metrics:
            metrics_hypothesis = self._analyze_system_metrics(system_metrics, error_classification)
            if metrics_hypothesis:
                hypotheses.append(metrics_hypothesis)
        
        # Sort hypotheses by confidence and return top N
        hypotheses.sort(key=lambda h: h.confidence_score, reverse=True)
        return hypotheses[:self.max_hypotheses]
    
    def _analyze_system_metrics(self, metrics: Dict[str, Any], 
                               error_classification: ErrorClassificationResult) -> Optional[RootCauseHypothesis]:
        """Analyze system metrics to identify potential root causes"""
        
        # Define thresholds for different metrics
        thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 90.0,
            'network_latency': 1000.0,  # ms
            'error_rate': 5.0  # %
        }
        
        issues = []
        for metric, value in metrics.items():
            if metric in thresholds and value > thresholds[metric]:
                issues.append(f"{metric}: {value} (threshold: {thresholds[metric]})")
        
        if issues:
            return RootCauseHypothesis(
                root_cause="System resource constraints",
                confidence_score=0.7,
                evidence_chain=issues,
                affected_components=["system"],
                suggested_actions=[
                    "Scale system resources",
                    "Optimize resource usage",
                    "Implement resource monitoring alerts"
                ],
                reasoning="System metrics indicate resource constraints",
                timestamp=datetime.now()
            )
        
        return None
    
    def add_feedback(self, hypothesis: RootCauseHypothesis, was_correct: bool, 
                    actual_root_cause: str = None):
        """Add feedback to improve future analysis"""
        # This could be used to train ML models or update pattern weights
        feedback_data = {
            'hypothesis': hypothesis.root_cause,
            'was_correct': was_correct,
            'actual_root_cause': actual_root_cause,
            'timestamp': datetime.now()
        }
        
        # Store feedback for future model improvement
        logger.info(f"Feedback recorded: {feedback_data}")

class KnowledgeBaseIntegrator:
    """Integrates with existing DevOps knowledge base and historical incident data"""

    def __init__(self, kb_config: Dict[str, Any] = None):
        self.config = kb_config or {}
        self.historical_incidents = []
        self.solution_database = {}
        self.incident_patterns = {}

    def load_historical_incidents(self, incidents_file: str = "historical_incidents.json"):
        """Load historical incident data"""
        try:
            if os.path.exists(incidents_file):
                with open(incidents_file, 'r') as f:
                    self.historical_incidents = json.load(f)
                logger.info(f"Loaded {len(self.historical_incidents)} historical incidents")
            else:
                # Create sample historical incidents
                self._create_sample_incidents(incidents_file)
        except Exception as e:
            logger.error(f"Error loading historical incidents: {e}")

    def _create_sample_incidents(self, incidents_file: str):
        """Create sample historical incidents for demonstration"""
        sample_incidents = [
            {
                "id": "INC-001",
                "title": "Database Connection Pool Exhaustion",
                "description": "Application unable to connect to database due to pool exhaustion",
                "root_cause": "High traffic spike without proper connection pooling",
                "resolution": "Increased connection pool size and implemented connection recycling",
                "error_patterns": ["connection pool exhausted", "too many connections"],
                "affected_components": ["application_server", "database"],
                "resolution_time_hours": 2.5,
                "severity": "high",
                "timestamp": "2024-01-15T10:30:00Z"
            },
            {
                "id": "INC-002",
                "title": "Memory Leak in Application Server",
                "description": "Application server running out of memory causing crashes",
                "root_cause": "Memory leak in user session management",
                "resolution": "Fixed session cleanup logic and increased heap size",
                "error_patterns": ["out of memory", "heap space", "gc overhead"],
                "affected_components": ["application_server"],
                "resolution_time_hours": 4.0,
                "severity": "critical",
                "timestamp": "2024-01-10T14:20:00Z"
            }
        ]

        with open(incidents_file, 'w') as f:
            json.dump(sample_incidents, f, indent=2)

        self.historical_incidents = sample_incidents

    def find_similar_incidents(self, error_text: str, error_category: str) -> List[Dict[str, Any]]:
        """Find similar historical incidents based on error patterns"""
        similar_incidents = []
        error_text_lower = error_text.lower()

        for incident in self.historical_incidents:
            similarity_score = 0.0

            # Check error pattern matches
            for pattern in incident.get('error_patterns', []):
                if pattern.lower() in error_text_lower:
                    similarity_score += 0.4

            # Check category match (if available)
            if 'category' in incident and incident['category'] == error_category:
                similarity_score += 0.3

            # Check component overlap
            # This would need component extraction from current error
            similarity_score += 0.1  # Base similarity

            if similarity_score > 0.3:  # Threshold for similarity
                incident_copy = incident.copy()
                incident_copy['similarity_score'] = similarity_score
                similar_incidents.append(incident_copy)

        # Sort by similarity score
        similar_incidents.sort(key=lambda x: x['similarity_score'], reverse=True)
        return similar_incidents[:5]  # Top 5 similar incidents

# Example usage
if __name__ == "__main__":
    # Initialize root cause analysis engine
    rca_engine = RootCauseAnalysisEngine()
    
    # Mock error classification result
    from error_classification_system import ErrorCategory
    
    error_result = ErrorClassificationResult(
        predicted_category=ErrorCategory.DATABASE_ERROR,
        confidence_score=0.85,
        probability_distribution={"database_error": 0.85},
        features_used=["text_features"],
        model_name="test",
        timestamp=datetime.now()
    )
    
    # Mock log context
    from nlp_preprocessing import ProcessedLogEntry
    
    log_context = [
        ProcessedLogEntry(
            original_text="Database connection failed: timeout after 30 seconds",
            cleaned_text="database connection failed timeout seconds",
            tokens=["database", "connection", "failed", "timeout", "seconds"],
            normalized_tokens=["database", "connection", "failed", "timeout", "seconds"],
            entities=[],
            features={"text_length": 50},
            metadata={},
            timestamp=datetime.now(),
            log_level="ERROR",
            source="application_server"
        )
    ]
    
    # Perform root cause analysis
    hypotheses = rca_engine.analyze_root_cause(error_result, log_context)
    
    # Display results
    for i, hypothesis in enumerate(hypotheses, 1):
        print(f"\n--- Hypothesis {i} ---")
        print(f"Root Cause: {hypothesis.root_cause}")
        print(f"Confidence: {hypothesis.confidence_score:.3f}")
        print(f"Evidence: {hypothesis.evidence_chain}")
        print(f"Suggested Actions: {hypothesis.suggested_actions}")
        print(f"Reasoning: {hypothesis.reasoning}")
