import streamlit as st
from agent import AgenticLogParser
import tempfile

st.set_page_config(page_title="Agentic Log Parser", layout="wide")
st.title("🧠 Agentic Log Parser")
st.markdown("""
Upload your `.log` file and get intelligent error/warning suggestions based on rule-based matching and optional GPT fallback.
""")

# File upload
uploaded_file = st.file_uploader("📂 Upload a log file", type=["log", "txt"])

if uploaded_file:
    # Read uploaded content
    log_data = uploaded_file.read().decode("utf-8")

    st.info("Logs uploaded for processing.")
    st.markdown("---")
    st.markdown("🚀 Running static analysis...")

    # Instantiate the agent
    agent = AgenticLogParser(log_data)
    results = agent.run()

    # Display Errors
    if results["errors"]:
        st.error("**[ERRORS FOUND]**")
        for e in results["errors"]:
            st.code(e, language="text")

    # Display Warnings
    if results["warnings"]:
        st.warning("**[WARNINGS FOUND]**")
        for w in results["warnings"]:
            st.code(w, language="text")

    # Display Suggestions
    if results["suggestions"]:
        st.success("**[SUGGESTIONS]**")
        for s in results["suggestions"]:
            st.markdown(f"**Log**: `{s['log']}`")
            if s.get("solution"):
                st.markdown(f"✅ **Suggestion**: {s['solution']}")
            if s.get("comment"):
                st.markdown(f"💬 **Comment**: {s['comment']}")
            st.markdown(f"🔍 **Source**: `{s['source']}`")
            st.markdown("---")
    else:
        st.warning("🤖 No suggestions found.")
        # inside if not self.suggestions:
        for err in self.errors:
            gpt_response = query_gpt(err)
            self.suggestions.append({
                "log": err,
                "solution": gpt_response,
                "comment": None,
                "source": "GPT"
            })


else:
    st.info("Please upload a `.log` file to begin analysis.")
