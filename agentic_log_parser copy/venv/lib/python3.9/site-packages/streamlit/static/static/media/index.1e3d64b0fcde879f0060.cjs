"use strict";function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(exports,"__esModule",{value:!0});var prosemirrorState=require("prosemirror-state"),prosemirrorTransform=require("prosemirror-transform"),InputRule=_createClass((function e(t,n){_classCallCheck(this,e),this.match=t,this.match=t,this.handler="string"==typeof n?stringHandler(n):n}));function stringHandler(e){return function(t,n,r,o){var u=e;if(n[1]){var l=n[0].lastIndexOf(n[1]);u+=n[0].slice(l+n[1].length);var s=(r+=l)-o;s>0&&(u=n[0].slice(l-s,l)+u,r=o)}return t.tr.insertText(u,r,o)}}var MAX_MATCH=500;function inputRules(e){var t=e.rules,n=new prosemirrorState.Plugin({state:{init:function(){return null},apply:function(e,t){var n=e.getMeta(this);return n||(e.selectionSet||e.docChanged?null:t)}},props:{handleTextInput:function(e,r,o,u){return run(e,r,o,u,t,n)},handleDOMEvents:{compositionend:function(e){setTimeout((function(){var r=e.state.selection.$cursor;r&&run(e,r.pos,r.pos,"",t,n)}))}}},isInputRules:!0});return n}function run(e,t,n,r,o,u){if(e.composing)return!1;var l=e.state,s=l.doc.resolve(t);if(s.parent.type.spec.code)return!1;for(var i=s.parent.textBetween(Math.max(0,s.parentOffset-MAX_MATCH),s.parentOffset,null,"\ufffc")+r,p=0;p<o.length;p++){var a=o[p].match.exec(i),c=a&&o[p].handler(l,a,t-(a[0].length-r.length),n);if(c)return e.dispatch(c.setMeta(u,{transform:c,from:t,to:n,text:r})),!0}return!1}var undoInputRule=function(e,t){for(var n=e.plugins,r=0;r<n.length;r++){var o=n[r],u=void 0;if(o.spec.isInputRules&&(u=o.getState(e))){if(t){for(var l=e.tr,s=u.transform,i=s.steps.length-1;i>=0;i--)l.step(s.steps[i].invert(s.docs[i]));if(u.text){var p=l.doc.resolve(u.from).marks();l.replaceWith(u.from,u.to,e.schema.text(u.text,p))}else l.delete(u.from,u.to);t(l)}return!0}}return!1},emDash=new InputRule(/--$/,"\u2014"),ellipsis=new InputRule(/\.\.\.$/,"\u2026"),openDoubleQuote=new InputRule(/(?:^|[\s\{\[\(\<'"\u2018\u201C])(")$/,"\u201c"),closeDoubleQuote=new InputRule(/"$/,"\u201d"),openSingleQuote=new InputRule(/(?:^|[\s\{\[\(\<'"\u2018\u201C])(')$/,"\u2018"),closeSingleQuote=new InputRule(/'$/,"\u2019"),smartQuotes=[openDoubleQuote,closeDoubleQuote,openSingleQuote,closeSingleQuote];function wrappingInputRule(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return new InputRule(e,(function(e,o,u,l){var s=n instanceof Function?n(o):n,i=e.tr.delete(u,l),p=i.doc.resolve(u).blockRange(),a=p&&prosemirrorTransform.findWrapping(p,t,s);if(!a)return null;i.wrap(p,a);var c=i.doc.resolve(u-1).nodeBefore;return c&&c.type==t&&prosemirrorTransform.canJoin(i.doc,u-1)&&(!r||r(o,c))&&i.join(u-1),i}))}function textblockTypeInputRule(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return new InputRule(e,(function(e,r,o,u){var l=e.doc.resolve(o),s=n instanceof Function?n(r):n;return l.node(-1).canReplaceWith(l.index(-1),l.indexAfter(-1),t)?e.tr.delete(o,u).setBlockType(o,o,t,s):null}))}exports.InputRule=InputRule,exports.closeDoubleQuote=closeDoubleQuote,exports.closeSingleQuote=closeSingleQuote,exports.ellipsis=ellipsis,exports.emDash=emDash,exports.inputRules=inputRules,exports.openDoubleQuote=openDoubleQuote,exports.openSingleQuote=openSingleQuote,exports.smartQuotes=smartQuotes,exports.textblockTypeInputRule=textblockTypeInputRule,exports.undoInputRule=undoInputRule,exports.wrappingInputRule=wrappingInputRule;