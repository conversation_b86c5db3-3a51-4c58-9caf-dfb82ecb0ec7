"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[6853],{76853:(e,t,r)=>{r.r(t),r.d(t,{default:()=>x});var n=r(66845),o=r(23175),s=r.n(o),a=r(27466),i=r(21e3),l=r(66694),c=r(1515),d=r(63637),h=r(60484);const p=(0,c.Z)(d.Q,{shouldForwardProp:h.Z,target:"e1se5lgy2"})((e=>{let{theme:t,usingCustomTheme:r}=e;return{fontSize:t.fontSizes.sm,width:"1.375rem",height:"1.375rem",borderWidth:"3px",radius:"4px",justifyContents:"center",padding:t.spacing.none,margin:t.spacing.none,borderColor:t.colors.fadedText10,borderTopColor:r?t.colors.primary:t.colors.blue70,flexGrow:0,flexShrink:0}}),""),m=(0,c.Z)("div",{target:"e1se5lgy1"})((e=>{let{theme:t,width:r,cache:n}=e;return{width:r,...n?{paddingBottom:"1rem",background:"linear-gradient(to bottom, ".concat(t.colors.bgColor," 0%, ").concat(t.colors.bgColor," 80%, transparent 100%)")}:null}}),""),u=(0,c.Z)("div",{target:"e1se5lgy0"})((e=>{let{theme:t}=e;return{display:"flex",gap:t.spacing.sm,alignItems:"center",width:"100%"}}),"");var g=r(40864);const x=function(e){let{width:t,element:r}=e;const{activeTheme:o}=n.useContext(l.E),c=!(0,a.MJ)(o),{cache:d}=r;return(0,g.jsx)(m,{className:s()({stSpinner:!0,cacheSpinner:d}),"data-testid":"stSpinner",width:t,cache:d,children:(0,g.jsxs)(u,{children:[(0,g.jsx)(p,{usingCustomTheme:c}),(0,g.jsx)(i.ZP,{source:r.text,allowHTML:!1})]})})}}}]);