"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[43],{10043:(t,e,o)=>{o.r(e),o.d(e,{default:()=>V});var n=o(66845),i=o(25621),a=o(6988),r=o(40566),s=o(60784),l=o(23849),d=o(62622),c=o(63765),h=o(72789),g=o(96825),u=o.n(g),m=o(99394),f=o.n(m),p=o(27466);function b(t,e){const o={font:e.genericFonts.bodyFont,background:e.colors.bgColor,fieldTitle:"verbal",autosize:{type:"fit",contains:"padding"},title:{align:"left",anchor:"start",color:e.colors.headingColor,titleFontStyle:"normal",fontWeight:e.fontWeights.bold,fontSize:e.fontSizes.smPx+2,orient:"top",offset:26},header:{titleFontWeight:e.fontWeights.normal,titleFontSize:e.fontSizes.mdPx,titleColor:(0,p.Xy)(e),titleFontStyle:"normal",labelFontSize:e.fontSizes.twoSmPx,labelFontWeight:e.fontWeights.normal,labelColor:(0,p.Xy)(e),labelFontStyle:"normal"},axis:{labelFontSize:e.fontSizes.twoSmPx,labelFontWeight:e.fontWeights.normal,labelColor:(0,p.Xy)(e),labelFontStyle:"normal",titleFontWeight:e.fontWeights.normal,titleFontSize:e.fontSizes.smPx,titleColor:(0,p.Xy)(e),titleFontStyle:"normal",ticks:!1,gridColor:(0,p.ny)(e),domain:!1,domainWidth:1,domainColor:(0,p.ny)(e),labelFlush:!0,labelFlushOffset:1,labelBound:!1,labelLimit:100,titlePadding:e.spacing.lgPx,labelPadding:e.spacing.lgPx,labelSeparation:e.spacing.twoXSPx,labelOverlap:!0},legend:{labelFontSize:e.fontSizes.smPx,labelFontWeight:e.fontWeights.normal,labelColor:(0,p.Xy)(e),titleFontSize:e.fontSizes.smPx,titleFontWeight:e.fontWeights.normal,titleFontStyle:"normal",titleColor:(0,p.Xy)(e),titlePadding:5,labelPadding:e.spacing.lgPx,columnPadding:e.spacing.smPx,rowPadding:e.spacing.twoXSPx,padding:7,symbolStrokeWidth:4},range:{category:(0,p.iY)(e),diverging:(0,p.ru)(e),ramp:(0,p.Gy)(e),heatmap:(0,p.Gy)(e)},view:{columns:1,strokeWidth:0,stroke:"transparent",continuousHeight:350,continuousWidth:400},concat:{columns:1},facet:{columns:1},mark:{tooltip:!0,...(0,p.Iy)(e)?{color:"#0068C9"}:{color:"#83C9FF"}},bar:{binSpacing:e.spacing.twoXSPx,discreteBandSize:{band:.85}},axisDiscrete:{grid:!1},axisXPoint:{grid:!1},axisTemporal:{grid:!1},axisXBand:{grid:!1}};return t?f()({},o,t,((t,e)=>Array.isArray(e)?e:void 0)):o}const y=(0,o(1515).Z)("div",{target:"egd2k5h0"})((t=>{let{theme:e}=t;return{"&.vega-embed":{"&:hover summary, .vega-embed:focus summary":{background:"transparent"},"&.has-actions":{paddingRight:0},".vega-actions":{zIndex:e.zIndices.popupMenu,backgroundColor:e.colors.bgColor,boxShadow:"rgb(0 0 0 / 16%) 0px 4px 16px",border:"1px solid ".concat(e.colors.fadedText10),a:{fontFamily:e.genericFonts.bodyFont,fontWeight:e.fontWeights.normal,fontSize:e.fontSizes.md,margin:0,padding:"".concat(e.spacing.twoXS," ").concat(e.spacing.twoXL),color:e.colors.bodyText},"a:hover":{backgroundColor:e.colors.secondaryBg,color:e.colors.bodyText},":before":{content:"none"},":after":{content:"none"}},summary:{opacity:0,height:"auto",zIndex:e.zIndices.menuButton,border:"none",boxShadow:"none",borderRadius:e.radii.lg,color:e.colors.fadedText10,backgroundColor:"transparent",transition:"opacity 300ms 150ms,transform 300ms 150ms","&:active, &:focus-visible, &:hover":{border:"none",boxShadow:"none",color:e.colors.bodyText,opacity:"1 !important",background:e.colors.darkenedBgMix25}}}}}),"");var w=o(40864);const v={DATAFRAME_INDEX:"(index)"},x="source",S=new Set([h.GI.DatetimeIndex,h.GI.Float64Index,h.GI.Int64Index,h.GI.RangeIndex,h.GI.UInt64Index]);class F extends n.PureComponent{constructor(){super(...arguments),this.vegaView=void 0,this.vegaFinalizer=void 0,this.defaultDataName=x,this.element=null,this.state={error:void 0},this.finalizeView=()=>{this.vegaFinalizer&&this.vegaFinalizer(),this.vegaFinalizer=void 0,this.vegaView=void 0},this.generateSpec=()=>{var t,e;const{element:o,theme:n}=this.props,i=JSON.parse(o.spec),{useContainerWidth:a}=o;if("streamlit"===o.vegaLiteTheme?i.config=b(i.config,n):"streamlit"===(null===(t=i.usermeta)||void 0===t||null===(e=t.embedOptions)||void 0===e?void 0:e.theme)?(i.config=b(i.config,n),i.usermeta.embedOptions.theme=void 0):i.config=function(t,e){const{colors:o,fontSizes:n,genericFonts:i}=e,a={labelFont:i.bodyFont,titleFont:i.bodyFont,labelFontSize:n.twoSmPx,titleFontSize:n.twoSmPx},r={background:o.bgColor,axis:{labelColor:o.bodyText,titleColor:o.bodyText,gridColor:(0,p.ny)(e),...a},legend:{labelColor:o.bodyText,titleColor:o.bodyText,...a},title:{color:o.bodyText,subtitleColor:o.bodyText,...a},header:{labelColor:o.bodyText,titleColor:o.bodyText,...a},view:{stroke:(0,p.ny)(e),continuousHeight:350,continuousWidth:400},mark:{tooltip:!0}};return t?u()({},r,t):r}(i.config,n),this.props.height?(i.width=this.props.width,i.height=this.props.height):a&&(i.width=this.props.width),i.padding||(i.padding={}),null==i.padding.bottom&&(i.padding.bottom=20),i.datasets)throw new Error("Datasets should not be passed as part of the spec");return i}}async componentDidMount(){try{await this.createView()}catch(t){const e=(0,c.b)(t);this.setState({error:e})}}componentWillUnmount(){this.finalizeView()}async componentDidUpdate(t){const{element:e,theme:o}=t,{element:n,theme:i}=this.props,a=e.spec,{spec:r}=n;if(!this.vegaView||a!==r||o!==i||t.width!==this.props.width||t.height!==this.props.height||t.element.vegaLiteTheme!==this.props.element.vegaLiteTheme){(0,l.ji)("Vega spec changed.");try{await this.createView()}catch(u){const t=(0,c.b)(u);this.setState({error:t})}return}const s=e.data,{data:d}=n;(s||d)&&this.updateData(this.defaultDataName,s,d);const h=z(e)||{},g=z(n)||{};for(const[l,c]of Object.entries(g)){const t=l||this.defaultDataName,e=h[t];this.updateData(t,e,c)}for(const l of Object.keys(h))g.hasOwnProperty(l)||l===this.defaultDataName||this.updateData(l,null,null);this.vegaView.resize().runAsync()}updateData(t,e,o){if(!this.vegaView)throw new Error("Chart has not been drawn yet");if(!o||0===o.data.numRows){return void(this.vegaView._runtime.data.hasOwnProperty(t)&&this.vegaView.remove(t,r.truthy))}if(!e||0===e.data.numRows)return void this.vegaView.insert(t,C(o));const{dataRows:n,dataColumns:i}=e.dimensions,{dataRows:a,dataColumns:s}=o.dimensions;if(function(t,e,o,n,i,a){if(o!==a)return!1;if(e>=i)return!1;if(0===e)return!1;const r=a-1,s=e-1;if(t.getDataValue(0,r)!==n.getDataValue(0,r)||t.getDataValue(s,r)!==n.getDataValue(s,r))return!1;return!0}(e,n,i,o,a,s))n<a&&this.vegaView.insert(t,C(o,n));else{const e=r.changeset().remove(r.truthy).insert(C(o));this.vegaView.change(t,e),(0,l.ji)("Had to clear the ".concat(t," dataset before inserting data through Vega view."))}}async createView(){if((0,l.ji)("Creating a new Vega view."),!this.element)throw Error("Element missing.");this.finalizeView();const t=this.props.element,e=this.generateSpec(),o={ast:!0,expr:s.N,tooltip:{disableDefaultStyle:!0},defaultStyle:!1,forceActionsMenu:!0},{vgSpec:n,view:i,finalize:r}=await(0,a.ZP)(this.element,e,o);this.vegaView=i,this.vegaFinalizer=r;const d=function(t){const e=z(t);if(null==e)return null;const o={};for(const[n,i]of Object.entries(e))o[n]=C(i);return o}(t),c=d?Object.keys(d):[];if(1===c.length){const[t]=c;this.defaultDataName=t}else 0===c.length&&n.data&&(this.defaultDataName=x);const h=function(t){const e=t.data;if(!e||0===e.data.numRows)return null;return C(e)}(t);if(h&&i.insert(this.defaultDataName,h),d)for(const[a,s]of Object.entries(d))i.insert(a,s);await i.runAsync(),this.vegaView.resize().runAsync()}render(){if(this.state.error)throw this.state.error;return(0,w.jsx)(y,{"data-testid":"stArrowVegaLiteChart",ref:t=>{this.element=t}})}}function z(t){var e;if(0===(null===(e=t.datasets)||void 0===e?void 0:e.length))return null;const o={};return t.datasets.forEach((t=>{if(!t)return;const e=t.hasName?t.name:null;o[e]=t.data})),o}function C(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t.isEmpty())return[];const o=[],{dataRows:n,dataColumns:i}=t.dimensions,a=h.fu.getTypeName(t.types.index[0]),r=S.has(a);for(let s=e;s<n;s++){const e={};if(r){const o=t.getIndexValue(s,0);e[v.DATAFRAME_INDEX]="bigint"===typeof o?Number(o):o}for(let o=0;o<i;o++){const n=t.getDataValue(s,o);e[t.columns[0][o]]="bigint"===typeof n?Number(n):n}o.push(e)}return o}const V=(0,i.b)((0,d.Z)(F))}}]);