"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var w3cKeyname=require("w3c-keyname"),prosemirrorState=require("prosemirror-state"),mac="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function normalizeKeyName(e){var t,r,a,i,n=e.split(/-(?!$)/),o=n[n.length-1];"Space"==o&&(o=" ");for(var s=0;s<n.length-1;s++){var l=n[s];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))t=!0;else if(/^(c|ctrl|control)$/i.test(l))r=!0;else if(/^s(hift)?$/i.test(l))a=!0;else{if(!/^mod$/i.test(l))throw new Error("Unrecognized modifier name: "+l);mac?i=!0:r=!0}}return t&&(o="Alt-"+o),r&&(o="Ctrl-"+o),i&&(o="Meta-"+o),a&&(o="Shift-"+o),o}function normalize(e){var t=Object.create(null);for(var r in e)t[normalizeKeyName(r)]=e[r];return t}function modifiers(e,t,r){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),!1!==r&&t.shiftKey&&(e="Shift-"+e),e}function keymap(e){return new prosemirrorState.Plugin({props:{handleKeyDown:keydownHandler(e)}})}function keydownHandler(e){var t=normalize(e);return function(e,r){var a,i=w3cKeyname.keyName(r),n=1==i.length&&" "!=i,o=t[modifiers(i,r,!n)];if(o&&o(e.state,e.dispatch,e))return!0;if(n&&(r.shiftKey||r.altKey||r.metaKey||i.charCodeAt(0)>127)&&(a=w3cKeyname.base[r.keyCode])&&a!=i){var s=t[modifiers(a,r,!0)];if(s&&s(e.state,e.dispatch,e))return!0}else if(n&&r.shiftKey){var l=t[modifiers(i,r,!0)];if(l&&l(e.state,e.dispatch,e))return!0}return!1}}exports.keydownHandler=keydownHandler,exports.keymap=keymap;