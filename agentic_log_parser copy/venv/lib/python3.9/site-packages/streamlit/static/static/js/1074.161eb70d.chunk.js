"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[1074],{87814:(e,t,s)=>{s.d(t,{K:()=>o});var i=s(50641);class o{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,s){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,i.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,s),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}},91074:(e,t,s)=>{s.r(t),s.d(t,{default:()=>w});var i=s(66845),o=s(91706),r=s(81810),l=s.n(r),a=s(25621),n=s(87814),d=s(90481),p=s(91034),h=s(12576),c=s(98478),u=s(86659);const m=(0,s(1515).Z)("div",{target:"e6zijwc0"})((e=>{let{theme:t}=e;return{"span[aria-disabled='true']":{background:t.colors.fadedText05}}}),"");var g=s(8879),v=s(68411),f=s(26628),b=s(97965),S=s(50641),y=s(40864);class x extends i.PureComponent{constructor(){super(...arguments),this.formClearHelper=new n.K,this.state={value:this.initialValue},this.commitWidgetValue=e=>{this.props.widgetMgr.setIntArrayValue(this.props.element,this.state.value,e)},this.onFormCleared=()=>{this.setState(((e,t)=>({value:t.element.default})),(()=>this.commitWidgetValue({fromUi:!0})))},this.onChange=e=>{this.props.element.maxSelections&&"select"===e.type&&this.state.value.length>=this.props.element.maxSelections||this.setState(this.generateNewState(e),(()=>{this.commitWidgetValue({fromUi:!0})}))},this.filterOptions=(e,t)=>{if(this.overMaxSelections())return[];const s=e.filter((e=>!this.state.value.includes(Number(e.value))));return(0,b.HX)(s,t)}}overMaxSelections(){return this.props.element.maxSelections>0&&this.state.value.length>=this.props.element.maxSelections}getNoResultsMsg(){const{maxSelections:e}=this.props.element,{value:t}=this.state;if(0===e)return"No results";if(t.length===e){const t=1!==e?"options":"option";return"You can only select up to ".concat(e," ").concat(t,". Remove an option first.")}return"No results"}get initialValue(){const e=this.props.widgetMgr.getIntArrayValue(this.props.element);return void 0!==e?e:this.props.element.default}componentDidMount(){this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}componentDidUpdate(){this.maybeUpdateFromProtobuf()}componentWillUnmount(){this.formClearHelper.disconnect()}maybeUpdateFromProtobuf(){const{setValue:e}=this.props.element;e&&this.updateFromProtobuf()}updateFromProtobuf(){const{value:e}=this.props.element;this.props.element.setValue=!1,this.setState({value:e},(()=>{this.commitWidgetValue({fromUi:!1})}))}get valueFromState(){return this.state.value.map((e=>{const t=this.props.element.options[e];return{value:e.toString(),label:t}}))}generateNewState(e){const t=()=>{var t;const s=null===(t=e.option)||void 0===t?void 0:t.value;return parseInt(s,10)};switch(e.type){case"remove":return{value:l()(this.state.value,t())};case"clear":return{value:[]};case"select":return{value:this.state.value.concat([t()])};default:throw new Error("State transition is unknown: ".concat(e.type))}}render(){var e;const{element:t,theme:s,width:i,widgetMgr:r}=this.props,l={width:i},{options:a}=t,n=0===a.length||this.props.disabled,b=0===a.length?"No options to select.":t.placeholder,x=a.map(((e,t)=>({label:e,value:t.toString()})));this.formClearHelper.manageFormClearListener(r,t.formId,this.onFormCleared);const w=a.length>10;return(0,y.jsxs)("div",{className:"row-widget stMultiSelect","data-testid":"stMultiSelect",style:l,children:[(0,y.jsx)(c.O,{label:t.label,disabled:n,labelVisibility:(0,S.iF)(null===(e=t.labelVisibility)||void 0===e?void 0:e.value),children:t.help&&(0,y.jsx)(u.dT,{children:(0,y.jsx)(g.Z,{content:t.help,placement:v.u.TOP_RIGHT})})}),(0,y.jsx)(m,{children:(0,y.jsx)(p.Z,{options:x,labelKey:"label",valueKey:"value","aria-label":t.label,placeholder:b,type:h.wD.select,multi:!0,onChange:this.onChange,value:this.valueFromState,disabled:n,size:"compact",noResultsMsg:this.getNoResultsMsg(),filterOptions:this.filterOptions,closeOnSelect:!1,overrides:{SelectArrow:{component:d.Z,props:{overrides:{Svg:{style:()=>({width:s.iconSizes.xl,height:s.iconSizes.xl})}}}},IconsContainer:{style:()=>({paddingRight:s.spacing.sm})},ControlContainer:{style:{borderLeftWidth:"1px",borderRightWidth:"1px",borderTopWidth:"1px",borderBottomWidth:"1px"}},Placeholder:{style:()=>({flex:"inherit"})},ValueContainer:{style:()=>({minHeight:"38.4px",paddingLeft:s.spacing.sm,paddingTop:0,paddingBottom:0,paddingRight:0})},ClearIcon:{props:{overrides:{Svg:{style:{color:s.colors.darkGray,transform:"scale(1.5)",width:s.spacing.twoXL,":hover":{fill:s.colors.bodyText}}}}}},SearchIcon:{style:{color:s.colors.darkGray}},Tag:{props:{overrides:{Root:{style:{borderTopLeftRadius:s.radii.md,borderTopRightRadius:s.radii.md,borderBottomRightRadius:s.radii.md,borderBottomLeftRadius:s.radii.md,fontSize:s.fontSizes.sm,paddingLeft:s.spacing.sm,marginLeft:0,marginRight:s.spacing.sm,height:"28px"}},Action:{style:{paddingLeft:0}},ActionIcon:{props:{overrides:{Svg:{style:{width:"10px",height:"10px"}}}}},Text:{style:{fontSize:s.fontSizes.md}}}}},MultiValue:{props:{overrides:{Root:{style:{fontSize:s.fontSizes.sm}}}}},Input:{props:{readOnly:o.tq&&!1===w?"readonly":null}},Dropdown:{component:f.Z}}})})]})}}const w=(0,a.b)(x)}}]);