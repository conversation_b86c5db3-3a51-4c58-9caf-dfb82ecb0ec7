"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[8492],{28492:(e,t,o)=>{o.r(t),o.d(t,{default:()=>N});var r=o(66845),i=o(25621),n=o(50641),a=o(80318),l=o(80745);function s(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,r)}return o}function c(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?s(Object(o),!0).forEach((function(t){d(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):s(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function d(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function u(e){var t=e.$disabled,o=e.$checked,r=e.$isIndeterminate,i=e.$error,n=e.$isHovered,a=e.$isActive,l=e.$theme.colors;return t?o||r?l.tickFillDisabled:l.tickFill:i&&(r||o)?a?l.tickFillErrorSelectedHoverActive:n?l.tickFillErrorSelectedHover:l.tickFillErrorSelected:i?a?l.tickFillErrorHoverActive:n?l.tickFillErrorHover:l.tickFillError:r||o?a?l.tickFillSelectedHoverActive:n?l.tickFillSelectedHover:l.tickFillSelected:a?l.tickFillActive:n?l.tickFillHover:l.tickFill}function p(e){var t=e.$disabled,o=e.$theme.colors;return t?o.contentSecondary:o.contentPrimary}var h=(0,l.zo)("label",(function(e){var t=e.$disabled,o=e.$labelPlacement;return{flexDirection:"top"===o||"bottom"===o?"column":"row",display:"flex",alignItems:"top"===o||"bottom"===o?"center":"flex-start",cursor:t?"not-allowed":"pointer",userSelect:"none"}}));h.displayName="Root",h.displayName="Root";var m=(0,l.zo)("span",(function(e){var t=e.$checked,o=e.$disabled,r=e.$error,i=e.$isIndeterminate,n=e.$theme,a=e.$isFocusVisible,l=n.sizing,s=n.animation,c=o?n.colors.tickMarkFillDisabled:r?n.colors.tickMarkFillError:n.colors.tickMarkFill,d=encodeURIComponent('\n    <svg width="14" height="4" viewBox="0 0 14 4" fill="none" xmlns="http://www.w3.org/2000/svg">\n      <path d="M14 0.5H0V3.5H14V0.5Z" fill="'.concat(c,'"/>\n    </svg>\n  ')),p=encodeURIComponent('\n    <svg width="17" height="13" viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">\n      <path d="M6.50002 12.6L0.400024 6.60002L2.60002 4.40002L6.50002 8.40002L13.9 0.900024L16.1 3.10002L6.50002 12.6Z" fill="'.concat(c,'"/>\n    </svg>\n  ')),h=n.borders.checkboxBorderRadius,m=function(e){var t=e.$disabled,o=e.$checked,r=e.$error,i=e.$isIndeterminate,n=e.$theme,a=e.$isFocusVisible,l=n.colors;return t?l.tickFillDisabled:o||i?"transparent":r?l.borderNegative:a?l.borderSelected:l.tickBorder}(e);return{flex:"0 0 auto",transitionDuration:s.timing200,transitionTimingFunction:s.easeOutCurve,transitionProperty:"background-image, border-color, background-color",width:l.scale700,height:l.scale700,left:"4px",top:"4px",boxSizing:"border-box",borderLeftStyle:"solid",borderRightStyle:"solid",borderTopStyle:"solid",borderBottomStyle:"solid",borderLeftWidth:"3px",borderRightWidth:"3px",borderTopWidth:"3px",borderBottomWidth:"3px",borderLeftColor:m,borderRightColor:m,borderTopColor:m,borderBottomColor:m,borderTopLeftRadius:h,borderTopRightRadius:h,borderBottomRightRadius:h,borderBottomLeftRadius:h,outline:a&&t?"3px solid ".concat(n.colors.accent):"none",display:"inline-block",verticalAlign:"middle",backgroundImage:i?"url('data:image/svg+xml,".concat(d,"');"):t?"url('data:image/svg+xml,".concat(p,"');"):null,backgroundColor:u(e),backgroundRepeat:"no-repeat",backgroundPosition:"center",backgroundSize:"contain",marginTop:n.sizing.scale0,marginBottom:n.sizing.scale0,marginLeft:n.sizing.scale0,marginRight:n.sizing.scale0}}));m.displayName="Checkmark",m.displayName="Checkmark";var g=(0,l.zo)("div",(function(e){var t=e.$theme.typography;return c(c(c({verticalAlign:"middle"},function(e){var t,o=e.$labelPlacement,r=void 0===o?"":o,i=e.$theme,n=i.sizing.scale300;switch(r){case"top":t="Bottom";break;case"bottom":t="Top";break;case"left":t="Right";break;default:t="Left"}return"rtl"===i.direction&&"Left"===t?t="Right":"rtl"===i.direction&&"Right"===t&&(t="Left"),d({},"padding".concat(t),n)}(e)),{},{color:p(e)},t.LabelMedium),{},{lineHeight:"24px"})}));g.displayName="Label",g.displayName="Label";var b=(0,l.zo)("input",{opacity:0,width:0,height:0,overflow:"hidden",margin:0,padding:0,position:"absolute"});b.displayName="Input",b.displayName="Input";var f=(0,l.zo)("div",(function(e){var t=e.$theme.colors.toggleFill;return e.$disabled?t=e.$theme.colors.toggleFillDisabled:e.$checked&&e.$error?t=e.$theme.colors.tickFillErrorSelected:e.$checked&&(t=e.$theme.colors.toggleFillChecked),{backgroundColor:t,borderTopLeftRadius:"50%",borderTopRightRadius:"50%",borderBottomRightRadius:"50%",borderBottomLeftRadius:"50%",boxShadow:e.$isFocusVisible?"0 0 0 3px ".concat(e.$theme.colors.accent):e.$isHovered&&!e.$disabled?e.$theme.lighting.shadow500:e.$theme.lighting.shadow400,outline:"none",height:e.$theme.sizing.scale700,width:e.$theme.sizing.scale700,transform:e.$checked?"translateX(".concat("rtl"===e.$theme.direction?"-100%":"100%",")"):null,transition:"transform ".concat(e.$theme.animation.timing200)}}));f.displayName="Toggle",f.displayName="Toggle";var v=(0,l.zo)("div",(function(e){var t=e.$theme.colors.toggleTrackFill;return e.$disabled?t=e.$theme.colors.toggleTrackFillDisabled:e.$error&&e.$checked&&(t=e.$theme.colors.tickFillError),{alignItems:"center",backgroundColor:t,borderTopLeftRadius:"7px",borderTopRightRadius:"7px",borderBottomRightRadius:"7px",borderBottomLeftRadius:"7px",display:"flex",height:e.$theme.sizing.scale550,marginTop:e.$theme.sizing.scale200,marginBottom:e.$theme.sizing.scale100,marginLeft:e.$theme.sizing.scale200,marginRight:e.$theme.sizing.scale100,width:e.$theme.sizing.scale1000}}));v.displayName="ToggleTrack",v.displayName="ToggleTrack";var y=Object.freeze({default:"default",toggle:"toggle",toggle_round:"toggle"}),k=Object.freeze({top:"top",right:"right",bottom:"bottom",left:"left"}),$=o(17964);function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}function x(){return x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},x.apply(this,arguments)}function F(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function R(e,t){return R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},R(e,t)}function C(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var o,r=L(e);if(t){var i=L(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return function(e,t){if(t&&("object"===w(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return T(e)}(this,o)}}function T(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function L(e){return L=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},L(e)}function O(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}var S=function(e){return e.stopPropagation()},P=function(e){!function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&R(e,t)}(l,e);var t,o,i,n=C(l);function l(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l);for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return O(T(e=n.call.apply(n,[this].concat(o))),"state",{isFocused:e.props.autoFocus||!1,isFocusVisible:!1,isHovered:!1,isActive:!1}),O(T(e),"onMouseEnter",(function(t){e.setState({isHovered:!0}),e.props.onMouseEnter(t)})),O(T(e),"onMouseLeave",(function(t){e.setState({isHovered:!1,isActive:!1}),e.props.onMouseLeave(t)})),O(T(e),"onMouseDown",(function(t){e.setState({isActive:!0}),e.props.onMouseDown(t)})),O(T(e),"onMouseUp",(function(t){e.setState({isActive:!1}),e.props.onMouseUp(t)})),O(T(e),"onFocus",(function(t){e.setState({isFocused:!0}),e.props.onFocus(t),(0,$.E)(t)&&e.setState({isFocusVisible:!0})})),O(T(e),"onBlur",(function(t){e.setState({isFocused:!1}),e.props.onBlur(t),!1!==e.state.isFocusVisible&&e.setState({isFocusVisible:!1})})),e}return t=l,(o=[{key:"componentDidMount",value:function(){var e=this.props,t=e.autoFocus,o=e.inputRef;t&&o.current&&o.current.focus()}},{key:"render",value:function(){var e=this.props,t=e.overrides,o=void 0===t?{}:t,i=e.onChange,n=e.labelPlacement,l=void 0===n?this.props.checkmarkType===y.toggle?"left":"right":n,s=e.inputRef,c=e.isIndeterminate,d=e.error,u=e.disabled,p=e.value,k=e.name,$=e.type,w=e.checked,F=e.children,R=e.required,C=e.title,T=o.Root,L=o.Checkmark,O=o.Label,P=o.Input,M=o.Toggle,j=o.ToggleTrack,B=(0,a.XG)(T)||h,E=(0,a.XG)(L)||m,H=(0,a.XG)(O)||g,V=(0,a.XG)(P)||b,I=(0,a.XG)(M)||f,z=(0,a.XG)(j)||v,D={onChange:i,onFocus:this.onFocus,onBlur:this.onBlur},W={onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp},A={$isFocused:this.state.isFocused,$isFocusVisible:this.state.isFocusVisible,$isHovered:this.state.isHovered,$isActive:this.state.isActive,$error:d,$checked:w,$isIndeterminate:c,$required:R,$disabled:u,$value:p},U=F&&r.createElement(H,x({$labelPlacement:l},A,(0,a.ch)(O)),this.props.containsInteractiveElement?r.createElement("div",{onClick:function(e){return e.preventDefault()}},F):F);return r.createElement(B,x({"data-baseweb":"checkbox",title:C||null,$labelPlacement:l},A,W,(0,a.ch)(T)),("top"===l||"left"===l)&&U,this.props.checkmarkType===y.toggle?r.createElement(z,x({},A,(0,a.ch)(j)),r.createElement(I,x({},A,(0,a.ch)(M)))):r.createElement(E,x({},A,(0,a.ch)(L))),r.createElement(V,x({value:p,name:k,checked:w,required:R,"aria-label":this.props["aria-label"]||this.props.ariaLabel,"aria-checked":c?"mixed":w,"aria-describedby":this.props["aria-describedby"],"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":d||null,"aria-required":R||null,disabled:u,type:$,ref:s,onClick:S},A,D,(0,a.ch)(P))),("bottom"===l||"right"===l)&&U)}}])&&F(t.prototype,o),i&&F(t,i),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.Component);O(P,"defaultProps",{overrides:{},checked:!1,containsInteractiveElement:!1,disabled:!1,autoFocus:!1,isIndeterminate:!1,inputRef:r.createRef(),error:!1,type:"checkbox",checkmarkType:y.default,onChange:function(){},onMouseEnter:function(){},onMouseLeave:function(){},onMouseDown:function(){},onMouseUp:function(){},onFocus:function(){},onBlur:function(){}});const M=P;var j=o(16295),B=o(35704),E=o(87814),H=o(27466),V=o(8879),I=o(68411),z=o(86659),D=o(21e3);const W=(0,o(1515).Z)("div",{target:"edwcd610"})((e=>{let{visibility:t}=e;return{display:t===n.Ws.Collapsed?"none":"flex",visibility:t===n.Ws.Hidden?"hidden":"visible",verticalAlign:"middle",flexDirection:"row",alignItems:"center"}}),"");var A=o(40864);class U extends r.PureComponent{constructor(){super(...arguments),this.formClearHelper=new E.K,this.state={value:this.initialValue},this.commitWidgetValue=e=>{this.props.widgetMgr.setBoolValue(this.props.element,this.state.value,e)},this.onFormCleared=()=>{this.setState(((e,t)=>({value:t.element.default})),(()=>this.commitWidgetValue({fromUi:!0})))},this.onChange=e=>{const t=e.target.checked;this.setState({value:t},(()=>this.commitWidgetValue({fromUi:!0})))}}get initialValue(){const e=this.props.widgetMgr.getBoolValue(this.props.element);return void 0!==e?e:this.props.element.default}componentDidMount(){this.props.element.setValue?this.updateFromProtobuf():this.commitWidgetValue({fromUi:!1})}componentDidUpdate(){this.maybeUpdateFromProtobuf()}componentWillUnmount(){this.formClearHelper.disconnect()}maybeUpdateFromProtobuf(){const{setValue:e}=this.props.element;e&&this.updateFromProtobuf()}updateFromProtobuf(){const{value:e}=this.props.element;this.props.element.setValue=!1,this.setState({value:e},(()=>{this.commitWidgetValue({fromUi:!1})}))}render(){var e;const{theme:t,width:o,element:r,disabled:i,widgetMgr:a}=this.props,{colors:l,spacing:s}=t,c=(0,H.Iy)(t),d={width:o},u=i?l.fadedText40:l.bodyText;return this.formClearHelper.manageFormClearListener(a,r.formId,this.onFormCleared),(0,A.jsx)("div",{className:"row-widget stCheckbox","data-testid":"stCheckbox",style:d,children:(0,A.jsx)(M,{checked:this.state.value,disabled:i,onChange:this.onChange,"aria-label":r.label,checkmarkType:r.type===j.XZ.StyleType.TOGGLE?y.toggle:y.default,labelPlacement:k.right,overrides:{Root:{style:e=>{let{$isFocusVisible:t}=e;return{marginBottom:0,marginTop:0,paddingRight:s.twoThirdsSmFont,backgroundColor:t?l.darkenedBgMix25:"",display:"flex",alignItems:"start"}}},Toggle:{style:e=>{let{$checked:t}=e,o=c?l.bgColor:l.bodyText;return i&&(o=c?l.gray60:l.gray90),{width:"12px",height:"12px",transform:t?"translateX(16px)":"",backgroundColor:o,boxShadow:""}}},ToggleTrack:{style:e=>{let{$checked:o,$isHovered:r}=e,n=l.fadedText40;return r&&!i&&(n=l.fadedText20),o&&!i&&(n=l.primary),{marginRight:0,marginLeft:"2px",paddingLeft:"2px",paddingRight:"2px",width:"32px",minWidth:"32px",height:"16px",minHeight:"16px",borderBottomLeftRadius:t.radii.lg,borderTopLeftRadius:t.radii.lg,borderBottomRightRadius:t.radii.lg,borderTopRightRadius:t.radii.lg,backgroundColor:n}}},Checkmark:{style:e=>{let{$isFocusVisible:t,$checked:o}=e;const r=o&&!i?l.primary:l.fadedText40;return{outline:0,width:"1rem",height:"1rem",marginTop:"0.30rem",boxShadow:t&&o?"0 0 0 0.2rem ".concat((0,B.DZ)(l.primary,.5)):"",borderLeftWidth:"1px",borderRightWidth:"1px",borderTopWidth:"1px",borderBottomWidth:"1px",borderLeftColor:r,borderRightColor:r,borderTopColor:r,borderBottomColor:r}}},Label:{style:{position:"relative",top:"1px",color:u}}},children:(0,A.jsxs)(W,{visibility:(0,n.iF)(null===(e=r.labelVisibility)||void 0===e?void 0:e.value),"data-testid":"stWidgetLabel",children:[(0,A.jsx)(D.ZP,{source:r.label,allowHTML:!1,isLabel:!0,largerLabel:!0}),r.help&&(0,A.jsx)(z.Hp,{color:u,children:(0,A.jsx)(V.Z,{content:r.help,placement:I.u.TOP_RIGHT})})]})})})}}const N=(0,i.b)(U)},87814:(e,t,o)=>{o.d(t,{K:()=>i});var r=o(50641);class i{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,o){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,r.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,o),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);