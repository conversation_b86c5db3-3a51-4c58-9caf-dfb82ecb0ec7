"use strict";function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}function _get(){return _get="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var o=_superPropBase(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},_get.apply(this,arguments)}function _superPropBase(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=_getPrototypeOf(e)););return e}function _inherits(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return _setPrototypeOf=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},_setPrototypeOf(e,t)}function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var n,o=_getPrototypeOf(e);if(t){var r=_getPrototypeOf(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return _possibleConstructorReturn(this,n)}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function _getPrototypeOf(e){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},_getPrototypeOf(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}Object.defineProperty(exports,"__esModule",{value:!0});var prosemirrorState=require("prosemirror-state"),prosemirrorModel=require("prosemirror-model"),prosemirrorTransform=require("prosemirror-transform"),nav="undefined"!=typeof navigator?navigator:null,doc="undefined"!=typeof document?document:null,agent=nav&&nav.userAgent||"",ie_edge=/Edge\/(\d+)/.exec(agent),ie_upto10=/MSIE \d/.exec(agent),ie_11up=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(agent),ie=!!(ie_upto10||ie_11up||ie_edge),ie_version=ie_upto10?document.documentMode:ie_11up?+ie_11up[1]:ie_edge?+ie_edge[1]:0,gecko=!ie&&/gecko\/(\d+)/i.test(agent);gecko&&(/Firefox\/(\d+)/.exec(agent)||[0,0])[1];var _chrome=!ie&&/Chrome\/(\d+)/.exec(agent),chrome=!!_chrome,chrome_version=_chrome?+_chrome[1]:0,safari=!ie&&!!nav&&/Apple Computer/.test(nav.vendor),ios=safari&&(/Mobile\/\w+/.test(agent)||!!nav&&nav.maxTouchPoints>2),mac=ios||!!nav&&/Mac/.test(nav.platform),android=/Android \d/.test(agent),webkit=!!doc&&"webkitFontSmoothing"in doc.documentElement.style,webkit_version=webkit?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,domIndex=function(e){for(var t=0;;t++)if(!(e=e.previousSibling))return t},parentNode=function(e){var t=e.assignedSlot||e.parentNode;return t&&11==t.nodeType?t.host:t},reusedRange=null,textRange=function(e,t,n){var o=reusedRange||(reusedRange=document.createRange());return o.setEnd(e,null==n?e.nodeValue.length:n),o.setStart(e,t||0),o},isEquivalentPosition=function(e,t,n,o){return n&&(scanFor(e,t,n,o,-1)||scanFor(e,t,n,o,1))},atomElements=/^(img|br|input|textarea|hr)$/i;function scanFor(e,t,n,o,r){for(;;){if(e==n&&t==o)return!0;if(t==(r<0?0:nodeSize(e))){var i=e.parentNode;if(!i||1!=i.nodeType||hasBlockDesc(e)||atomElements.test(e.nodeName)||"false"==e.contentEditable)return!1;t=domIndex(e)+(r<0?0:1),e=i}else{if(1!=e.nodeType)return!1;if("false"==(e=e.childNodes[t+(r<0?-1:0)]).contentEditable)return!1;t=r<0?nodeSize(e):0}}}function nodeSize(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function isOnEdge(e,t,n){for(var o=0==t,r=t==nodeSize(e);o||r;){if(e==n)return!0;var i=domIndex(e);if(!(e=e.parentNode))return!1;o=o&&0==i,r=r&&i==nodeSize(e)}}function hasBlockDesc(e){for(var t,n=e;n&&!(t=n.pmViewDesc);n=n.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==e||t.contentDOM==e)}var selectionCollapsed=function(e){var t=e.isCollapsed;return t&&chrome&&e.rangeCount&&!e.getRangeAt(0).collapsed&&(t=!1),t};function keyEvent(e,t){var n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=e,n.key=n.code=t,n}function windowRect(e){return{left:0,right:e.documentElement.clientWidth,top:0,bottom:e.documentElement.clientHeight}}function getSide(e,t){return"number"==typeof e?e:e[t]}function clientRect(e){var t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,o=t.height/e.offsetHeight||1;return{left:t.left,right:t.left+e.clientWidth*n,top:t.top,bottom:t.top+e.clientHeight*o}}function scrollRectIntoView(e,t,n){for(var o=e.someProp("scrollThreshold")||0,r=e.someProp("scrollMargin")||5,i=e.dom.ownerDocument,s=n||e.dom;s;s=parentNode(s))if(1==s.nodeType){var a=s,l=a==i.body,c=l?windowRect(i):clientRect(a),d=0,u=0;if(t.top<c.top+getSide(o,"top")?u=-(c.top-t.top+getSide(r,"top")):t.bottom>c.bottom-getSide(o,"bottom")&&(u=t.bottom-c.bottom+getSide(r,"bottom")),t.left<c.left+getSide(o,"left")?d=-(c.left-t.left+getSide(r,"left")):t.right>c.right-getSide(o,"right")&&(d=t.right-c.right+getSide(r,"right")),d||u)if(l)i.defaultView.scrollBy(d,u);else{var h=a.scrollLeft,f=a.scrollTop;u&&(a.scrollTop+=u),d&&(a.scrollLeft+=d);var p=a.scrollLeft-h,m=a.scrollTop-f;t={left:t.left-p,top:t.top-m,right:t.right-p,bottom:t.bottom-m}}if(l)break}}function storeScrollPos(e){for(var t,n,o=e.dom.getBoundingClientRect(),r=Math.max(0,o.top),i=(o.left+o.right)/2,s=r+1;s<Math.min(innerHeight,o.bottom);s+=5){var a=e.root.elementFromPoint(i,s);if(a&&a!=e.dom&&e.dom.contains(a)){var l=a.getBoundingClientRect();if(l.top>=r-20){t=a,n=l.top;break}}}return{refDOM:t,refTop:n,stack:scrollStack(e.dom)}}function scrollStack(e){for(var t=[],n=e.ownerDocument,o=e;o&&(t.push({dom:o,top:o.scrollTop,left:o.scrollLeft}),e!=n);o=parentNode(o));return t}function resetScrollPos(e){var t=e.refDOM,n=e.refTop,o=e.stack,r=t?t.getBoundingClientRect().top:0;restoreScrollStack(o,0==r?0:r-n)}function restoreScrollStack(e,t){for(var n=0;n<e.length;n++){var o=e[n],r=o.dom,i=o.top,s=o.left;r.scrollTop!=i+t&&(r.scrollTop=i+t),r.scrollLeft!=s&&(r.scrollLeft=s)}}var preventScrollSupported=null;function focusPreventScroll(e){if(e.setActive)return e.setActive();if(preventScrollSupported)return e.focus(preventScrollSupported);var t=scrollStack(e);e.focus(null==preventScrollSupported?{get preventScroll(){return preventScrollSupported={preventScroll:!0},!0}}:void 0),preventScrollSupported||(preventScrollSupported=!1,restoreScrollStack(t,0))}function findOffsetInNode(e,t){for(var n,o,r=2e8,i=0,s=t.top,a=t.top,l=e.firstChild,c=0;l;l=l.nextSibling,c++){var d=void 0;if(1==l.nodeType)d=l.getClientRects();else{if(3!=l.nodeType)continue;d=textRange(l).getClientRects()}for(var u=0;u<d.length;u++){var h=d[u];if(h.top<=s&&h.bottom>=a){s=Math.max(h.bottom,s),a=Math.min(h.top,a);var f=h.left>t.left?h.left-t.left:h.right<t.left?t.left-h.right:0;if(f<r){n=l,r=f,o=f&&3==n.nodeType?{left:h.right<t.left?h.right:h.left,top:t.top}:t,1==l.nodeType&&f&&(i=c+(t.left>=(h.left+h.right)/2?1:0));continue}}!n&&(t.left>=h.right&&t.top>=h.top||t.left>=h.left&&t.top>=h.bottom)&&(i=c+1)}}return n&&3==n.nodeType?findOffsetInText(n,o):!n||r&&1==n.nodeType?{node:e,offset:i}:findOffsetInNode(n,o)}function findOffsetInText(e,t){for(var n=e.nodeValue.length,o=document.createRange(),r=0;r<n;r++){o.setEnd(e,r+1),o.setStart(e,r);var i=singleRect(o,1);if(i.top!=i.bottom&&inRect(t,i))return{node:e,offset:r+(t.left>=(i.left+i.right)/2?1:0)}}return{node:e,offset:0}}function inRect(e,t){return e.left>=t.left-1&&e.left<=t.right+1&&e.top>=t.top-1&&e.top<=t.bottom+1}function targetKludge(e,t){var n=e.parentNode;return n&&/^li$/i.test(n.nodeName)&&t.left<e.getBoundingClientRect().left?n:e}function posFromElement(e,t,n){var o=findOffsetInNode(t,n),r=o.node,i=o.offset,s=-1;if(1==r.nodeType&&!r.firstChild){var a=r.getBoundingClientRect();s=a.left!=a.right&&n.left>(a.left+a.right)/2?1:-1}return e.docView.posFromDOM(r,i,s)}function posFromCaret(e,t,n,o){for(var r=-1,i=t;i!=e.dom;){var s=e.docView.nearestDesc(i,!0);if(!s)return null;if(s.node.isBlock&&s.parent){var a=s.dom.getBoundingClientRect();if(a.left>o.left||a.top>o.top)r=s.posBefore;else{if(!(a.right<o.left||a.bottom<o.top))break;r=s.posAfter}}i=s.dom.parentNode}return r>-1?r:e.docView.posFromDOM(t,n,1)}function elementFromPoint(e,t,n){var o=e.childNodes.length;if(o&&n.top<n.bottom)for(var r=Math.max(0,Math.min(o-1,Math.floor(o*(t.top-n.top)/(n.bottom-n.top))-2)),i=r;;){var s=e.childNodes[i];if(1==s.nodeType)for(var a=s.getClientRects(),l=0;l<a.length;l++){var c=a[l];if(inRect(t,c))return elementFromPoint(s,t,c)}if((i=(i+1)%o)==r)break}return e}function _posAtCoords(e,t){var n,o=e.dom.ownerDocument,r=0;if(o.caretPositionFromPoint)try{var i=o.caretPositionFromPoint(t.left,t.top);i&&(n=i.offsetNode,r=i.offset)}catch(p){}if(!n&&o.caretRangeFromPoint){var s=o.caretRangeFromPoint(t.left,t.top);s&&(n=s.startContainer,r=s.startOffset)}var a,l=(e.root.elementFromPoint?e.root:o).elementFromPoint(t.left,t.top);if(!l||!e.dom.contains(1!=l.nodeType?l.parentNode:l)){var c=e.dom.getBoundingClientRect();if(!inRect(t,c))return null;if(!(l=elementFromPoint(e.dom,t,c)))return null}if(safari)for(var d=l;n&&d;d=parentNode(d))d.draggable&&(n=void 0);if(l=targetKludge(l,t),n){if(gecko&&1==n.nodeType&&(r=Math.min(r,n.childNodes.length))<n.childNodes.length){var u,h=n.childNodes[r];"IMG"==h.nodeName&&(u=h.getBoundingClientRect()).right<=t.left&&u.bottom>t.top&&r++}n==e.dom&&r==n.childNodes.length-1&&1==n.lastChild.nodeType&&t.top>n.lastChild.getBoundingClientRect().bottom?a=e.state.doc.content.size:0!=r&&1==n.nodeType&&"BR"==n.childNodes[r-1].nodeName||(a=posFromCaret(e,n,r,t))}null==a&&(a=posFromElement(e,l,t));var f=e.docView.nearestDesc(l,!0);return{pos:a,inside:f?f.posAtStart-f.border:-1}}function singleRect(e,t){var n=e.getClientRects();return n.length?n[t<0?0:n.length-1]:e.getBoundingClientRect()}var BIDI=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function _coordsAtPos(e,t,n){var o=e.docView.domFromPos(t,n<0?-1:1),r=o.node,i=o.offset,s=o.atom,a=webkit||gecko;if(3==r.nodeType){if(!a||!BIDI.test(r.nodeValue)&&(n<0?i:i!=r.nodeValue.length)){var l=i,c=i,d=n<0?1:-1;return n<0&&!i?(c++,d=-1):n>=0&&i==r.nodeValue.length?(l--,d=1):n<0?l--:c++,flattenV(singleRect(textRange(r,l,c),1),d<0)}var u=singleRect(textRange(r,i,i),n);if(gecko&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){var h=singleRect(textRange(r,i-1,i-1),-1);if(h.top==u.top){var f=singleRect(textRange(r,i,i+1),-1);if(f.top!=u.top)return flattenV(f,f.left<h.left)}}return u}if(!e.state.doc.resolve(t-(s||0)).parent.inlineContent){if(null==s&&i&&(n<0||i==nodeSize(r))){var p=r.childNodes[i-1];if(1==p.nodeType)return flattenH(p.getBoundingClientRect(),!1)}if(null==s&&i<nodeSize(r)){var m=r.childNodes[i];if(1==m.nodeType)return flattenH(m.getBoundingClientRect(),!0)}return flattenH(r.getBoundingClientRect(),n>=0)}if(null==s&&i&&(n<0||i==nodeSize(r))){var v=r.childNodes[i-1],g=3==v.nodeType?textRange(v,nodeSize(v)-(a?0:1)):1!=v.nodeType||"BR"==v.nodeName&&v.nextSibling?null:v;if(g)return flattenV(singleRect(g,1),!1)}if(null==s&&i<nodeSize(r)){for(var y=r.childNodes[i];y.pmViewDesc&&y.pmViewDesc.ignoreForCoords;)y=y.nextSibling;var S=y?3==y.nodeType?textRange(y,0,a?0:1):1==y.nodeType?y:null:null;if(S)return flattenV(singleRect(S,-1),!0)}return flattenV(singleRect(3==r.nodeType?textRange(r):r,-n),n>=0)}function flattenV(e,t){if(0==e.width)return e;var n=t?e.left:e.right;return{top:e.top,bottom:e.bottom,left:n,right:n}}function flattenH(e,t){if(0==e.height)return e;var n=t?e.top:e.bottom;return{top:n,bottom:n,left:e.left,right:e.right}}function withFlushedState(e,t,n){var o=e.state,r=e.root.activeElement;o!=t&&e.updateState(t),r!=e.dom&&e.focus();try{return n()}finally{o!=t&&e.updateState(o),r!=e.dom&&r&&r.focus()}}function endOfTextblockVertical(e,t,n){var o=t.selection,r="up"==n?o.$from:o.$to;return withFlushedState(e,t,(function(){for(var t=e.docView.domFromPos(r.pos,"up"==n?-1:1).node;;){var o=e.docView.nearestDesc(t,!0);if(!o)break;if(o.node.isBlock){t=o.dom;break}t=o.dom.parentNode}for(var i=_coordsAtPos(e,r.pos,1),s=t.firstChild;s;s=s.nextSibling){var a=void 0;if(1==s.nodeType)a=s.getClientRects();else{if(3!=s.nodeType)continue;a=textRange(s,0,s.nodeValue.length).getClientRects()}for(var l=0;l<a.length;l++){var c=a[l];if(c.bottom>c.top+1&&("up"==n?i.top-c.top>2*(c.bottom-i.top):c.bottom-i.bottom>2*(i.bottom-c.top)))return!1}}return!0}))}var maybeRTL=/[\u0590-\u08ac]/;function endOfTextblockHorizontal(e,t,n){var o=t.selection.$head;if(!o.parent.isTextblock)return!1;var r=o.parentOffset,i=!r,s=r==o.parent.content.size,a=e.domSelection();return maybeRTL.test(o.parent.textContent)&&a.modify?withFlushedState(e,t,(function(){var t=a.getRangeAt(0),r=a.focusNode,i=a.focusOffset,s=a.caretBidiLevel;a.modify("move",n,"character");var l=!(o.depth?e.docView.domAfterPos(o.before()):e.dom).contains(1==a.focusNode.nodeType?a.focusNode:a.focusNode.parentNode)||r==a.focusNode&&i==a.focusOffset;return a.removeAllRanges(),a.addRange(t),null!=s&&(a.caretBidiLevel=s),l})):"left"==n||"backward"==n?i:s}var cachedState=null,cachedDir=null,cachedResult=!1;function _endOfTextblock(e,t,n){return cachedState==t&&cachedDir==n?cachedResult:(cachedState=t,cachedDir=n,cachedResult="up"==n||"down"==n?endOfTextblockVertical(e,t,n):endOfTextblockHorizontal(e,t,n))}var NOT_DIRTY=0,CHILD_DIRTY=1,CONTENT_DIRTY=2,NODE_DIRTY=3,ViewDesc=function(){function e(t,n,o,r){_classCallCheck(this,e),this.parent=t,this.children=n,this.dom=o,this.contentDOM=r,this.dirty=NOT_DIRTY,o.pmViewDesc=this}return _createClass(e,[{key:"matchesWidget",value:function(e){return!1}},{key:"matchesMark",value:function(e){return!1}},{key:"matchesNode",value:function(e,t,n){return!1}},{key:"matchesHack",value:function(e){return!1}},{key:"parseRule",value:function(){return null}},{key:"stopEvent",value:function(e){return!1}},{key:"size",get:function(){for(var e=0,t=0;t<this.children.length;t++)e+=this.children[t].size;return e}},{key:"border",get:function(){return 0}},{key:"destroy",value:function(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(var e=0;e<this.children.length;e++)this.children[e].destroy()}},{key:"posBeforeChild",value:function(e){for(var t=0,n=this.posAtStart;;t++){var o=this.children[t];if(o==e)return n;n+=o.size}}},{key:"posBefore",get:function(){return this.parent.posBeforeChild(this)}},{key:"posAtStart",get:function(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}},{key:"posAfter",get:function(){return this.posBefore+this.size}},{key:"posAtEnd",get:function(){return this.posAtStart+this.size-2*this.border}},{key:"localPosFromDOM",value:function(e,t,n){if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode)){if(n<0){var o,r;if(e==this.contentDOM)o=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;o=e.previousSibling}for(;o&&(!(r=o.pmViewDesc)||r.parent!=this);)o=o.previousSibling;return o?this.posBeforeChild(r)+r.size:this.posAtStart}var i,s;if(e==this.contentDOM)i=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.nextSibling}for(;i&&(!(s=i.pmViewDesc)||s.parent!=this);)i=i.nextSibling;return i?this.posBeforeChild(s):this.posAtEnd}var a;if(e==this.dom&&this.contentDOM)a=t>domIndex(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))a=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(var l=e;;l=l.parentNode){if(l==this.dom){a=!1;break}if(l.previousSibling)break}if(null==a&&t==e.childNodes.length)for(var c=e;;c=c.parentNode){if(c==this.dom){a=!0;break}if(c.nextSibling)break}}return(null==a?n>0:a)?this.posAtEnd:this.posAtStart}},{key:"nearestDesc",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!0,o=e;o;o=o.parentNode){var r=this.getDesc(o),i=void 0;if(r&&(!t||r.node)){if(!n||!(i=r.nodeDOM)||(1==i.nodeType?i.contains(1==e.nodeType?e:e.parentNode):i==e))return r;n=!1}}}},{key:"getDesc",value:function(e){for(var t=e.pmViewDesc,n=t;n;n=n.parent)if(n==this)return t}},{key:"posFromDOM",value:function(e,t,n){for(var o=e;o;o=o.parentNode){var r=this.getDesc(o);if(r)return r.localPosFromDOM(e,t,n)}return-1}},{key:"descAt",value:function(e){for(var t=0,n=0;t<this.children.length;t++){var o=this.children[t],r=n+o.size;if(n==e&&r!=n){for(;!o.border&&o.children.length;)o=o.children[0];return o}if(e<r)return o.descAt(e-n-o.border);n=r}}},{key:"domFromPos",value:function(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};for(var n,o=0,r=0,i=0;o<this.children.length;o++){var s=this.children[o],a=i+s.size;if(a>e||s instanceof TrailingHackViewDesc){r=e-i;break}i=a}if(r)return this.children[o].domFromPos(r-this.children[o].border,t);for(;o&&!(n=this.children[o-1]).size&&n instanceof WidgetViewDesc&&n.side>=0;o--);if(t<=0){for(var l,c=!0;(l=o?this.children[o-1]:null)&&l.dom.parentNode!=this.contentDOM;o--,c=!1);return l&&t&&c&&!l.border&&!l.domAtom?l.domFromPos(l.size,t):{node:this.contentDOM,offset:l?domIndex(l.dom)+1:0}}for(var d,u=!0;(d=o<this.children.length?this.children[o]:null)&&d.dom.parentNode!=this.contentDOM;o++,u=!1);return d&&u&&!d.border&&!d.domAtom?d.domFromPos(0,t):{node:this.contentDOM,offset:d?domIndex(d.dom):this.contentDOM.childNodes.length}}},{key:"parseRange",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};for(var o=-1,r=-1,i=n,s=0;;s++){var a=this.children[s],l=i+a.size;if(-1==o&&e<=l){var c=i+a.border;if(e>=c&&t<=l-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,c);e=i;for(var d=s;d>0;d--){var u=this.children[d-1];if(u.size&&u.dom.parentNode==this.contentDOM&&!u.emptyChildAt(1)){o=domIndex(u.dom)+1;break}e-=u.size}-1==o&&(o=0)}if(o>-1&&(l>t||s==this.children.length-1)){t=l;for(var h=s+1;h<this.children.length;h++){var f=this.children[h];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(-1)){r=domIndex(f.dom);break}t+=f.size}-1==r&&(r=this.contentDOM.childNodes.length);break}i=l}return{node:this.contentDOM,from:e,to:t,fromOffset:o,toOffset:r}}},{key:"emptyChildAt",value:function(e){if(this.border||!this.contentDOM||!this.children.length)return!1;var t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}},{key:"domAfterPos",value:function(e){var t=this.domFromPos(e,0),n=t.node,o=t.offset;if(1!=n.nodeType||o==n.childNodes.length)throw new RangeError("No node after pos "+e);return n.childNodes[o]}},{key:"setSelection",value:function(e,t,n){for(var o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=Math.min(e,t),i=Math.max(e,t),s=0,a=0;s<this.children.length;s++){var l=this.children[s],c=a+l.size;if(r>a&&i<c)return l.setSelection(e-a-l.border,t-a-l.border,n,o);a=c}var d=this.domFromPos(e,e?-1:1),u=t==e?d:this.domFromPos(t,t?-1:1),h=n.getSelection(),f=!1;if((gecko||safari)&&e==t){var p=d,m=p.node,v=p.offset;if(3==m.nodeType){if((f=!(!v||"\n"!=m.nodeValue[v-1]))&&v==m.nodeValue.length)for(var g,y=m;y;y=y.parentNode){if(g=y.nextSibling){"BR"==g.nodeName&&(d=u={node:g.parentNode,offset:domIndex(g)+1});break}var S=y.pmViewDesc;if(S&&S.node&&S.node.isBlock)break}}else{var k=m.childNodes[v-1];f=k&&("BR"==k.nodeName||"false"==k.contentEditable)}}if(gecko&&h.focusNode&&h.focusNode!=u.node&&1==h.focusNode.nodeType){var D=h.focusNode.childNodes[h.focusOffset];D&&"false"==D.contentEditable&&(o=!0)}if(o||f&&safari||!isEquivalentPosition(d.node,d.offset,h.anchorNode,h.anchorOffset)||!isEquivalentPosition(u.node,u.offset,h.focusNode,h.focusOffset)){var b=!1;if((h.extend||e==t)&&!f){h.collapse(d.node,d.offset);try{e!=t&&h.extend(u.node,u.offset),b=!0}catch(O){if(!(O instanceof DOMException))throw O}}if(!b){if(e>t){var C=d;d=u,u=C}var w=document.createRange();w.setEnd(u.node,u.offset),w.setStart(d.node,d.offset),h.removeAllRanges(),h.addRange(w)}}}},{key:"ignoreMutation",value:function(e){return!this.contentDOM&&"selection"!=e.type}},{key:"contentLost",get:function(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}},{key:"markDirty",value:function(e,t){for(var n=0,o=0;o<this.children.length;o++){var r=this.children[o],i=n+r.size;if(n==i?e<=i&&t>=n:e<i&&t>n){var s=n+r.border,a=i-r.border;if(e>=s&&t<=a)return this.dirty=e==n||t==i?CONTENT_DIRTY:CHILD_DIRTY,void(e!=s||t!=a||!r.contentLost&&r.dom.parentNode==this.contentDOM?r.markDirty(e-s,t-s):r.dirty=NODE_DIRTY);r.dirty=r.dom!=r.contentDOM||r.dom.parentNode!=this.contentDOM||r.children.length?NODE_DIRTY:CONTENT_DIRTY}n=i}this.dirty=CONTENT_DIRTY}},{key:"markParentsDirty",value:function(){for(var e=1,t=this.parent;t;t=t.parent,e++){var n=1==e?CONTENT_DIRTY:CHILD_DIRTY;t.dirty<n&&(t.dirty=n)}}},{key:"domAtom",get:function(){return!1}},{key:"ignoreForCoords",get:function(){return!1}}]),e}(),WidgetViewDesc=function(e){_inherits(n,ViewDesc);var t=_createSuper(n);function n(e,o,r,i){var s;_classCallCheck(this,n);var a,l=o.type.toDOM;if("function"==typeof l&&(l=l(r,(function(){return a?a.parent?a.parent.posBeforeChild(a):void 0:i}))),!o.type.spec.raw){if(1!=l.nodeType){var c=document.createElement("span");c.appendChild(l),l=c}l.contentEditable="false",l.classList.add("ProseMirror-widget")}return(s=t.call(this,e,[],l,null)).widget=o,s.widget=o,a=_assertThisInitialized(s),s}return _createClass(n,[{key:"matchesWidget",value:function(e){return this.dirty==NOT_DIRTY&&e.type.eq(this.widget.type)}},{key:"parseRule",value:function(){return{ignore:!0}}},{key:"stopEvent",value:function(e){var t=this.widget.spec.stopEvent;return!!t&&t(e)}},{key:"ignoreMutation",value:function(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}},{key:"destroy",value:function(){this.widget.type.destroy(this.dom),_get(_getPrototypeOf(n.prototype),"destroy",this).call(this)}},{key:"domAtom",get:function(){return!0}},{key:"side",get:function(){return this.widget.type.side}}]),n}(),CompositionViewDesc=function(e){_inherits(n,ViewDesc);var t=_createSuper(n);function n(e,o,r,i){var s;return _classCallCheck(this,n),(s=t.call(this,e,[],o,null)).textDOM=r,s.text=i,s}return _createClass(n,[{key:"size",get:function(){return this.text.length}},{key:"localPosFromDOM",value:function(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}},{key:"domFromPos",value:function(e){return{node:this.textDOM,offset:e}}},{key:"ignoreMutation",value:function(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}]),n}(),MarkViewDesc=function(e){_inherits(n,ViewDesc);var t=_createSuper(n);function n(e,o,r,i){var s;return _classCallCheck(this,n),(s=t.call(this,e,[],r,i)).mark=o,s}return _createClass(n,[{key:"parseRule",value:function(){return this.dirty&NODE_DIRTY||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM||void 0}}},{key:"matchesMark",value:function(e){return this.dirty!=NODE_DIRTY&&this.mark.eq(e)}},{key:"markDirty",value:function(e,t){if(_get(_getPrototypeOf(n.prototype),"markDirty",this).call(this,e,t),this.dirty!=NOT_DIRTY){for(var o=this.parent;!o.node;)o=o.parent;o.dirty<this.dirty&&(o.dirty=this.dirty),this.dirty=NOT_DIRTY}}},{key:"slice",value:function(e,t,o){var r=n.create(this.parent,this.mark,!0,o),i=this.children,s=this.size;t<s&&(i=replaceNodes(i,t,s,o)),e>0&&(i=replaceNodes(i,0,e,o));for(var a=0;a<i.length;a++)i[a].parent=r;return r.children=i,r}}],[{key:"create",value:function(e,t,o,r){var i=r.nodeViews[t.type.name],s=i&&i(t,r,o);return s&&s.dom||(s=prosemirrorModel.DOMSerializer.renderSpec(document,t.type.spec.toDOM(t,o))),new n(e,t,s.dom,s.contentDOM||s.dom)}}]),n}(),NodeViewDesc=function(e){_inherits(n,ViewDesc);var t=_createSuper(n);function n(e,o,r,i,s,a,l,c,d){var u;return _classCallCheck(this,n),(u=t.call(this,e,[],s,a)).node=o,u.outerDeco=r,u.innerDeco=i,u.nodeDOM=l,a&&u.updateChildren(c,d),u}return _createClass(n,[{key:"parseRule",value:function(){var e=this;if(this.node.type.spec.reparseInView)return null;var t={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(t.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(var n=this.children.length-1;n>=0;n--){var o=this.children[n];if(this.dom.contains(o.dom.parentNode)){t.contentElement=o.dom.parentNode;break}}t.contentElement||(t.getContent=function(){return prosemirrorModel.Fragment.empty})}else t.contentElement=this.contentDOM;else t.getContent=function(){return e.node.content};return t}},{key:"matchesNode",value:function(e,t,n){return this.dirty==NOT_DIRTY&&e.eq(this.node)&&sameOuterDeco(t,this.outerDeco)&&n.eq(this.innerDeco)}},{key:"size",get:function(){return this.node.nodeSize}},{key:"border",get:function(){return this.node.isLeaf?0:1}},{key:"updateChildren",value:function(e,t){var n=this,o=this.node.inlineContent,r=t,i=e.composing?this.localCompositionInfo(e,t):null,s=i&&i.pos>-1?i:null,a=i&&i.pos<0,l=new ViewTreeUpdater(this,s&&s.node,e);iterDeco(this.node,this.innerDeco,(function(t,i,s){t.spec.marks?l.syncToMarks(t.spec.marks,o,e):t.type.side>=0&&!s&&l.syncToMarks(i==n.node.childCount?prosemirrorModel.Mark.none:n.node.child(i).marks,o,e),l.placeWidget(t,e,r)}),(function(t,n,s,c){var d;l.syncToMarks(t.marks,o,e),l.findNodeMatch(t,n,s,c)||a&&e.state.selection.from>r&&e.state.selection.to<r+t.nodeSize&&(d=l.findIndexWithChild(i.node))>-1&&l.updateNodeAt(t,n,s,d,e)||l.updateNextNode(t,n,s,e,c)||l.addNode(t,n,s,e,r),r+=t.nodeSize})),l.syncToMarks([],o,e),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||this.dirty==CONTENT_DIRTY)&&(s&&this.protectLocalComposition(e,s),renderDescs(this.contentDOM,this.children,e),ios&&iosHacks(this.dom))}},{key:"localCompositionInfo",value:function(e,t){var n=e.state.selection,o=n.from,r=n.to;if(!(e.state.selection instanceof prosemirrorState.TextSelection)||o<t||r>t+this.node.content.size)return null;var i=e.domSelection(),s=nearbyTextNode(i.focusNode,i.focusOffset);if(!s||!this.dom.contains(s.parentNode))return null;if(this.node.inlineContent){var a=s.nodeValue,l=findTextInFragment(this.node.content,a,o-t,r-t);return l<0?null:{node:s,pos:l,text:a}}return{node:s,pos:-1,text:""}}},{key:"protectLocalComposition",value:function(e,t){var n=t.node,o=t.pos,r=t.text;if(!this.getDesc(n)){for(var i=n;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}var s=new CompositionViewDesc(this,i,n,r);e.input.compositionNodes.push(s),this.children=replaceNodes(this.children,o,o+r.length,e,s)}}},{key:"update",value:function(e,t,n,o){return!(this.dirty==NODE_DIRTY||!e.sameMarkup(this.node))&&(this.updateInner(e,t,n,o),!0)}},{key:"updateInner",value:function(e,t,n,o){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(o,this.posAtStart),this.dirty=NOT_DIRTY}},{key:"updateOuterDeco",value:function(e){if(!sameOuterDeco(e,this.outerDeco)){var t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=patchOuterDeco(this.dom,this.nodeDOM,computeOuterDeco(this.outerDeco,this.node,t),computeOuterDeco(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}}},{key:"selectNode",value:function(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}},{key:"deselectNode",value:function(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable")}},{key:"domAtom",get:function(){return this.node.isAtom}}],[{key:"create",value:function(e,t,o,r,i,s){var a,l=i.nodeViews[t.type.name],c=l&&l(t,i,(function(){return a?a.parent?a.parent.posBeforeChild(a):void 0:s}),o,r),d=c&&c.dom,u=c&&c.contentDOM;if(t.isText)if(d){if(3!=d.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else d=document.createTextNode(t.text);else if(!d){var h=prosemirrorModel.DOMSerializer.renderSpec(document,t.type.spec.toDOM(t));d=h.dom,u=h.contentDOM}u||t.isText||"BR"==d.nodeName||(d.hasAttribute("contenteditable")||(d.contentEditable="false"),t.type.spec.draggable&&(d.draggable=!0));var f=d;return d=applyOuterDeco(d,o,t),c?a=new CustomNodeViewDesc(e,t,o,r,d,u||null,f,c,i,s+1):t.isText?new TextViewDesc(e,t,o,r,d,f,i):new n(e,t,o,r,d,u||null,f,i,s+1)}}]),n}();function docViewDesc(e,t,n,o,r){return applyOuterDeco(o,t,e),new NodeViewDesc(void 0,e,t,n,o,o,o,r,0)}var TextViewDesc=function(e){_inherits(n,NodeViewDesc);var t=_createSuper(n);function n(e,o,r,i,s,a,l){return _classCallCheck(this,n),t.call(this,e,o,r,i,s,null,a,l,0)}return _createClass(n,[{key:"parseRule",value:function(){for(var e=this.nodeDOM.parentNode;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}},{key:"update",value:function(e,t,n,o){return!(this.dirty==NODE_DIRTY||this.dirty!=NOT_DIRTY&&!this.inParent()||!e.sameMarkup(this.node))&&(this.updateOuterDeco(t),this.dirty==NOT_DIRTY&&e.text==this.node.text||e.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=e.text,o.trackWrites==this.nodeDOM&&(o.trackWrites=null)),this.node=e,this.dirty=NOT_DIRTY,!0)}},{key:"inParent",value:function(){for(var e=this.parent.contentDOM,t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}},{key:"domFromPos",value:function(e){return{node:this.nodeDOM,offset:e}}},{key:"localPosFromDOM",value:function(e,t,o){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):_get(_getPrototypeOf(n.prototype),"localPosFromDOM",this).call(this,e,t,o)}},{key:"ignoreMutation",value:function(e){return"characterData"!=e.type&&"selection"!=e.type}},{key:"slice",value:function(e,t,o){var r=this.node.cut(e,t),i=document.createTextNode(r.text);return new n(this.parent,r,this.outerDeco,this.innerDeco,i,i,o)}},{key:"markDirty",value:function(e,t){_get(_getPrototypeOf(n.prototype),"markDirty",this).call(this,e,t),this.dom==this.nodeDOM||0!=e&&t!=this.nodeDOM.nodeValue.length||(this.dirty=NODE_DIRTY)}},{key:"domAtom",get:function(){return!1}}]),n}(),TrailingHackViewDesc=function(e){_inherits(n,ViewDesc);var t=_createSuper(n);function n(){return _classCallCheck(this,n),t.apply(this,arguments)}return _createClass(n,[{key:"parseRule",value:function(){return{ignore:!0}}},{key:"matchesHack",value:function(e){return this.dirty==NOT_DIRTY&&this.dom.nodeName==e}},{key:"domAtom",get:function(){return!0}},{key:"ignoreForCoords",get:function(){return"IMG"==this.dom.nodeName}}]),n}(),CustomNodeViewDesc=function(e){_inherits(n,NodeViewDesc);var t=_createSuper(n);function n(e,o,r,i,s,a,l,c,d,u){var h;return _classCallCheck(this,n),(h=t.call(this,e,o,r,i,s,a,l,d,u)).spec=c,h}return _createClass(n,[{key:"update",value:function(e,t,o,r){if(this.dirty==NODE_DIRTY)return!1;if(this.spec.update){var i=this.spec.update(e,t,o);return i&&this.updateInner(e,t,o,r),i}return!(!this.contentDOM&&!e.isLeaf)&&_get(_getPrototypeOf(n.prototype),"update",this).call(this,e,t,o,r)}},{key:"selectNode",value:function(){this.spec.selectNode?this.spec.selectNode():_get(_getPrototypeOf(n.prototype),"selectNode",this).call(this)}},{key:"deselectNode",value:function(){this.spec.deselectNode?this.spec.deselectNode():_get(_getPrototypeOf(n.prototype),"deselectNode",this).call(this)}},{key:"setSelection",value:function(e,t,o,r){this.spec.setSelection?this.spec.setSelection(e,t,o):_get(_getPrototypeOf(n.prototype),"setSelection",this).call(this,e,t,o,r)}},{key:"destroy",value:function(){this.spec.destroy&&this.spec.destroy(),_get(_getPrototypeOf(n.prototype),"destroy",this).call(this)}},{key:"stopEvent",value:function(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}},{key:"ignoreMutation",value:function(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):_get(_getPrototypeOf(n.prototype),"ignoreMutation",this).call(this,e)}}]),n}();function renderDescs(e,t,n){for(var o=e.firstChild,r=!1,i=0;i<t.length;i++){var s=t[i],a=s.dom;if(a.parentNode==e){for(;a!=o;)o=rm(o),r=!0;o=o.nextSibling}else r=!0,e.insertBefore(a,o);if(s instanceof MarkViewDesc){var l=o?o.previousSibling:e.lastChild;renderDescs(s.contentDOM,s.children,n),o=l?l.nextSibling:e.firstChild}}for(;o;)o=rm(o),r=!0;r&&n.trackWrites==e&&(n.trackWrites=null)}var OuterDecoLevel=function(e){e&&(this.nodeName=e)};OuterDecoLevel.prototype=Object.create(null);var noDeco=[new OuterDecoLevel];function computeOuterDeco(e,t,n){if(0==e.length)return noDeco;for(var o=n?noDeco[0]:new OuterDecoLevel,r=[o],i=0;i<e.length;i++){var s=e[i].type.attrs;if(s)for(var a in s.nodeName&&r.push(o=new OuterDecoLevel(s.nodeName)),s){var l=s[a];null!=l&&(n&&1==r.length&&r.push(o=new OuterDecoLevel(t.isInline?"span":"div")),"class"==a?o.class=(o.class?o.class+" ":"")+l:"style"==a?o.style=(o.style?o.style+";":"")+l:"nodeName"!=a&&(o[a]=l))}}return r}function patchOuterDeco(e,t,n,o){if(n==noDeco&&o==noDeco)return t;for(var r=t,i=0;i<o.length;i++){var s=o[i],a=n[i];if(i){var l=void 0;a&&a.nodeName==s.nodeName&&r!=e&&(l=r.parentNode)&&l.nodeName.toLowerCase()==s.nodeName||((l=document.createElement(s.nodeName)).pmIsDeco=!0,l.appendChild(r),a=noDeco[0]),r=l}patchAttributes(r,a||noDeco[0],s)}return r}function patchAttributes(e,t,n){for(var o in t)"class"==o||"style"==o||"nodeName"==o||o in n||e.removeAttribute(o);for(var r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=t[r]&&e.setAttribute(r,n[r]);if(t.class!=n.class){for(var i=t.class?t.class.split(" ").filter(Boolean):[],s=n.class?n.class.split(" ").filter(Boolean):[],a=0;a<i.length;a++)-1==s.indexOf(i[a])&&e.classList.remove(i[a]);for(var l=0;l<s.length;l++)-1==i.indexOf(s[l])&&e.classList.add(s[l]);0==e.classList.length&&e.removeAttribute("class")}if(t.style!=n.style){if(t.style)for(var c,d=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;c=d.exec(t.style);)e.style.removeProperty(c[1]);n.style&&(e.style.cssText+=n.style)}}function applyOuterDeco(e,t,n){return patchOuterDeco(e,e,noDeco,computeOuterDeco(t,n,1!=e.nodeType))}function sameOuterDeco(e,t){if(e.length!=t.length)return!1;for(var n=0;n<e.length;n++)if(!e[n].type.eq(t[n].type))return!1;return!0}function rm(e){var t=e.nextSibling;return e.parentNode.removeChild(e),t}var ViewTreeUpdater=function(){function e(t,n,o){_classCallCheck(this,e),this.lock=n,this.view=o,this.index=0,this.stack=[],this.changed=!1,this.top=t,this.preMatch=preMatch(t.node.content,t)}return _createClass(e,[{key:"destroyBetween",value:function(e,t){if(e!=t){for(var n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}},{key:"destroyRest",value:function(){this.destroyBetween(this.index,this.top.children.length)}},{key:"syncToMarks",value:function(e,t,n){for(var o=0,r=this.stack.length>>1,i=Math.min(r,e.length);o<i&&(o==r-1?this.top:this.stack[o+1<<1]).matchesMark(e[o])&&!1!==e[o].type.spec.spanning;)o++;for(;o<r;)this.destroyRest(),this.top.dirty=NOT_DIRTY,this.index=this.stack.pop(),this.top=this.stack.pop(),r--;for(;r<e.length;){this.stack.push(this.top,this.index+1);for(var s=-1,a=this.index;a<Math.min(this.index+3,this.top.children.length);a++)if(this.top.children[a].matchesMark(e[r])){s=a;break}if(s>-1)s>this.index&&(this.changed=!0,this.destroyBetween(this.index,s)),this.top=this.top.children[this.index];else{var l=MarkViewDesc.create(this.top,e[r],t,n);this.top.children.splice(this.index,0,l),this.top=l,this.changed=!0}this.index=0,r++}}},{key:"findNodeMatch",value:function(e,t,n,o){var r,i=-1;if(o>=this.preMatch.index&&(r=this.preMatch.matches[o-this.preMatch.index]).parent==this.top&&r.matchesNode(e,t,n))i=this.top.children.indexOf(r,this.index);else for(var s=this.index,a=Math.min(this.top.children.length,s+5);s<a;s++){var l=this.top.children[s];if(l.matchesNode(e,t,n)&&!this.preMatch.matched.has(l)){i=s;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}},{key:"updateNodeAt",value:function(e,t,n,o,r){var i=this.top.children[o];return i.dirty==NODE_DIRTY&&i.dom==i.contentDOM&&(i.dirty=CONTENT_DIRTY),!!i.update(e,t,n,r)&&(this.destroyBetween(this.index,o),this.index++,!0)}},{key:"findIndexWithChild",value:function(e){for(;;){var t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){var n=e.pmViewDesc;if(n)for(var o=this.index;o<this.top.children.length;o++)if(this.top.children[o]==n)return o;return-1}e=t}}},{key:"updateNextNode",value:function(e,t,n,o,r){for(var i=this.index;i<this.top.children.length;i++){var s=this.top.children[i];if(s instanceof NodeViewDesc){var a=this.preMatch.matched.get(s);if(null!=a&&a!=r)return!1;var l=s.dom;if(!(this.lock&&(l==this.lock||1==l.nodeType&&l.contains(this.lock.parentNode))&&!(e.isText&&s.node&&s.node.isText&&s.nodeDOM.nodeValue==e.text&&s.dirty!=NODE_DIRTY&&sameOuterDeco(t,s.outerDeco)))&&s.update(e,t,n,o))return this.destroyBetween(this.index,i),s.dom!=l&&(this.changed=!0),this.index++,!0;break}}return!1}},{key:"addNode",value:function(e,t,n,o,r){this.top.children.splice(this.index++,0,NodeViewDesc.create(this.top,e,t,n,o,r)),this.changed=!0}},{key:"placeWidget",value:function(e,t,n){var o=this.index<this.top.children.length?this.top.children[this.index]:null;if(!o||!o.matchesWidget(e)||e!=o.widget&&o.widget.type.toDOM.parentNode){var r=new WidgetViewDesc(this.top,e,t,n);this.top.children.splice(this.index++,0,r),this.changed=!0}else this.index++}},{key:"addTextblockHacks",value:function(){for(var e=this.top.children[this.index-1],t=this.top;e instanceof MarkViewDesc;)e=(t=e).children[t.children.length-1];(!e||!(e instanceof TextViewDesc)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((safari||chrome)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}},{key:"addHackNode",value:function(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{var n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");var o=new TrailingHackViewDesc(this.top,[],n,null);t!=this.top?t.children.push(o):t.children.splice(this.index++,0,o),this.changed=!0}}}]),e}();function preMatch(e,t){var n=t,o=n.children.length,r=e.childCount,i=new Map,s=[];e:for(;r>0;){for(var a=void 0;;)if(o){var l=n.children[o-1];if(!(l instanceof MarkViewDesc)){a=l,o--;break}n=l,o=l.children.length}else{if(n==t)break e;o=n.parent.children.indexOf(n),n=n.parent}var c=a.node;if(c){if(c!=e.child(r-1))break;--r,i.set(a,r),s.push(a)}}return{index:r,matched:i,matches:s.reverse()}}function compareSide(e,t){return e.type.side-t.type.side}function iterDeco(e,t,n,o){var r=t.locals(e),i=0;if(0!=r.length)for(var s=0,a=[],l=null,c=0;;){if(s<r.length&&r[s].to==i){for(var d=r[s++],u=void 0;s<r.length&&r[s].to==i;)(u||(u=[d])).push(r[s++]);if(u){u.sort(compareSide);for(var h=0;h<u.length;h++)n(u[h],c,!!l)}else n(d,c,!!l)}var f=void 0,p=void 0;if(l)p=-1,f=l,l=null;else{if(!(c<e.childCount))break;p=c,f=e.child(c++)}for(var m=0;m<a.length;m++)a[m].to<=i&&a.splice(m--,1);for(;s<r.length&&r[s].from<=i&&r[s].to>i;)a.push(r[s++]);var v=i+f.nodeSize;if(f.isText){var g=v;s<r.length&&r[s].from<g&&(g=r[s].from);for(var y=0;y<a.length;y++)a[y].to<g&&(g=a[y].to);g<v&&(l=f.cut(g-i),f=f.cut(0,g-i),v=g,p=-1)}o(f,f.isInline&&!f.isLeaf?a.filter((function(e){return!e.inline})):a.slice(),t.forChild(i,f),p),i=v}else for(var S=0;S<e.childCount;S++){var k=e.child(S);o(k,r,t.forChild(i,k),S),i+=k.nodeSize}}function iosHacks(e){if("UL"==e.nodeName||"OL"==e.nodeName){var t=e.style.cssText;e.style.cssText=t+"; list-style: square !important",window.getComputedStyle(e).listStyle,e.style.cssText=t}}function nearbyTextNode(e,t){for(;;){if(3==e.nodeType)return e;if(1==e.nodeType&&t>0){if(e.childNodes.length>t&&3==e.childNodes[t].nodeType)return e.childNodes[t];t=nodeSize(e=e.childNodes[t-1])}else{if(!(1==e.nodeType&&t<e.childNodes.length))return null;e=e.childNodes[t],t=0}}}function findTextInFragment(e,t,n,o){for(var r=0,i=0;r<e.childCount&&i<=o;){var s=e.child(r++),a=i;if(i+=s.nodeSize,s.isText){for(var l=s.text;r<e.childCount;){var c=e.child(r++);if(i+=c.nodeSize,!c.isText)break;l+=c.text}if(i>=n){var d=a<o?l.lastIndexOf(t,o-a-1):-1;if(d>=0&&d+t.length+a>=n)return a+d;if(n==o&&l.length>=o+t.length-a&&l.slice(o-a,o-a+t.length)==t)return o}}}return-1}function replaceNodes(e,t,n,o,r){for(var i=[],s=0,a=0;s<e.length;s++){var l=e[s],c=a,d=a+=l.size;c>=n||d<=t?i.push(l):(c<t&&i.push(l.slice(0,t-c,o)),r&&(i.push(r),r=void 0),d>n&&i.push(l.slice(n-c,l.size,o)))}return i}function selectionFromDOM(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=e.domSelection(),o=e.state.doc;if(!n.focusNode)return null;var r=e.docView.nearestDesc(n.focusNode),i=r&&0==r.size,s=e.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(s<0)return null;var a,l,c=o.resolve(s);if(selectionCollapsed(n)){for(a=c;r&&!r.node;)r=r.parent;var d=r.node;if(r&&d.isAtom&&prosemirrorState.NodeSelection.isSelectable(d)&&r.parent&&(!d.isInline||!isOnEdge(n.focusNode,n.focusOffset,r.dom))){var u=r.posBefore;l=new prosemirrorState.NodeSelection(s==u?c:o.resolve(u))}}else{var h=e.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(h<0)return null;a=o.resolve(h)}l||(l=selectionBetween(e,a,c,"pointer"==t||e.state.selection.head<c.pos&&!i?1:-1));return l}function editorOwnsSelection(e){return e.editable?e.hasFocus():hasSelection(e)&&document.activeElement&&document.activeElement.contains(e.dom)}function selectionToDOM(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.state.selection;if(syncNodeSelection(e,n),editorOwnsSelection(e)){if(!t&&e.input.mouseDown&&e.input.mouseDown.allowDefault&&chrome){var o=e.domSelection(),r=e.domObserver.currentSelection;if(o.anchorNode&&r.anchorNode&&isEquivalentPosition(o.anchorNode,o.anchorOffset,r.anchorNode,r.anchorOffset))return e.input.mouseDown.delayedSelectionSync=!0,void e.domObserver.setCurSelection()}if(e.domObserver.disconnectSelection(),e.cursorWrapper)selectCursorWrapper(e);else{var i,s,a=n.anchor,l=n.head;!brokenSelectBetweenUneditable||n instanceof prosemirrorState.TextSelection||(n.$from.parent.inlineContent||(i=temporarilyEditableNear(e,n.from)),n.empty||n.$from.parent.inlineContent||(s=temporarilyEditableNear(e,n.to))),e.docView.setSelection(a,l,e.root,t),brokenSelectBetweenUneditable&&(i&&resetEditable(i),s&&resetEditable(s)),n.visible?e.dom.classList.remove("ProseMirror-hideselection"):(e.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&removeClassOnSelectionChange(e))}e.domObserver.setCurSelection(),e.domObserver.connectSelection()}}var brokenSelectBetweenUneditable=safari||chrome&&chrome_version<63;function temporarilyEditableNear(e,t){var n=e.docView.domFromPos(t,0),o=n.node,r=n.offset,i=r<o.childNodes.length?o.childNodes[r]:null,s=r?o.childNodes[r-1]:null;if(safari&&i&&"false"==i.contentEditable)return setEditable(i);if((!i||"false"==i.contentEditable)&&(!s||"false"==s.contentEditable)){if(i)return setEditable(i);if(s)return setEditable(s)}}function setEditable(e){return e.contentEditable="true",safari&&e.draggable&&(e.draggable=!1,e.wasDraggable=!0),e}function resetEditable(e){e.contentEditable="false",e.wasDraggable&&(e.draggable=!0,e.wasDraggable=null)}function removeClassOnSelectionChange(e){var t=e.dom.ownerDocument;t.removeEventListener("selectionchange",e.input.hideSelectionGuard);var n=e.domSelection(),o=n.anchorNode,r=n.anchorOffset;t.addEventListener("selectionchange",e.input.hideSelectionGuard=function(){n.anchorNode==o&&n.anchorOffset==r||(t.removeEventListener("selectionchange",e.input.hideSelectionGuard),setTimeout((function(){editorOwnsSelection(e)&&!e.state.selection.visible||e.dom.classList.remove("ProseMirror-hideselection")}),20))})}function selectCursorWrapper(e){var t=e.domSelection(),n=document.createRange(),o=e.cursorWrapper.dom,r="IMG"==o.nodeName;r?n.setEnd(o.parentNode,domIndex(o)+1):n.setEnd(o,0),n.collapse(!1),t.removeAllRanges(),t.addRange(n),!r&&!e.state.selection.visible&&ie&&ie_version<=11&&(o.disabled=!0,o.disabled=!1)}function syncNodeSelection(e,t){if(t instanceof prosemirrorState.NodeSelection){var n=e.docView.descAt(t.from);n!=e.lastSelectedViewDesc&&(clearNodeSelection(e),n&&n.selectNode(),e.lastSelectedViewDesc=n)}else clearNodeSelection(e)}function clearNodeSelection(e){e.lastSelectedViewDesc&&(e.lastSelectedViewDesc.parent&&e.lastSelectedViewDesc.deselectNode(),e.lastSelectedViewDesc=void 0)}function selectionBetween(e,t,n,o){return e.someProp("createSelectionBetween",(function(o){return o(e,t,n)}))||prosemirrorState.TextSelection.between(t,n,o)}function hasFocusAndSelection(e){return(!e.editable||e.root.activeElement==e.dom)&&hasSelection(e)}function hasSelection(e){var t=e.domSelection();if(!t.anchorNode)return!1;try{return e.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(e.editable||e.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(n){return!1}}function anchorInRightPlace(e){var t=e.docView.domFromPos(e.state.selection.anchor,0),n=e.domSelection();return isEquivalentPosition(t.node,t.offset,n.anchorNode,n.anchorOffset)}function moveSelectionBlock(e,t){var n=e.selection,o=n.$anchor,r=n.$head,i=t>0?o.max(r):o.min(r),s=i.parent.inlineContent?i.depth?e.doc.resolve(t>0?i.after():i.before()):null:i;return s&&prosemirrorState.Selection.findFrom(s,t)}function apply(e,t){return e.dispatch(e.state.tr.setSelection(t).scrollIntoView()),!0}function selectHorizontally(e,t,n){var o=e.state.selection;if(!(o instanceof prosemirrorState.TextSelection)){if(o instanceof prosemirrorState.NodeSelection&&o.node.isInline)return apply(e,new prosemirrorState.TextSelection(t>0?o.$to:o.$from));var r=moveSelectionBlock(e.state,t);return!!r&&apply(e,r)}if(!o.empty||n.indexOf("s")>-1)return!1;if(e.endOfTextblock(t>0?"right":"left")){var i=moveSelectionBlock(e.state,t);return!!(i&&i instanceof prosemirrorState.NodeSelection)&&apply(e,i)}if(!(mac&&n.indexOf("m")>-1)){var s,a=o.$head,l=a.textOffset?null:t<0?a.nodeBefore:a.nodeAfter;if(!l||l.isText)return!1;var c=t<0?a.pos-l.nodeSize:a.pos;return!!(l.isAtom||(s=e.docView.descAt(c))&&!s.contentDOM)&&(prosemirrorState.NodeSelection.isSelectable(l)?apply(e,new prosemirrorState.NodeSelection(t<0?e.state.doc.resolve(a.pos-l.nodeSize):a)):!!webkit&&apply(e,new prosemirrorState.TextSelection(e.state.doc.resolve(t<0?c:c+l.nodeSize))))}}function nodeLen(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function isIgnorable(e){var t=e.pmViewDesc;return t&&0==t.size&&(e.nextSibling||"BR"!=e.nodeName)}function skipIgnoredNodesLeft(e){var t=e.domSelection(),n=t.focusNode,o=t.focusOffset;if(n){var r,i,s=!1;for(gecko&&1==n.nodeType&&o<nodeLen(n)&&isIgnorable(n.childNodes[o])&&(s=!0);;)if(o>0){if(1!=n.nodeType)break;var a=n.childNodes[o-1];if(isIgnorable(a))r=n,i=--o;else{if(3!=a.nodeType)break;o=(n=a).nodeValue.length}}else{if(isBlockNode(n))break;for(var l=n.previousSibling;l&&isIgnorable(l);)r=n.parentNode,i=domIndex(l),l=l.previousSibling;if(l)o=nodeLen(n=l);else{if((n=n.parentNode)==e.dom)break;o=0}}s?setSelFocus(e,t,n,o):r&&setSelFocus(e,t,r,i)}}function skipIgnoredNodesRight(e){var t=e.domSelection(),n=t.focusNode,o=t.focusOffset;if(n){for(var r,i,s=nodeLen(n);;)if(o<s){if(1!=n.nodeType)break;if(!isIgnorable(n.childNodes[o]))break;r=n,i=++o}else{if(isBlockNode(n))break;for(var a=n.nextSibling;a&&isIgnorable(a);)r=a.parentNode,i=domIndex(a)+1,a=a.nextSibling;if(a)o=0,s=nodeLen(n=a);else{if((n=n.parentNode)==e.dom)break;o=s=0}}r&&setSelFocus(e,t,r,i)}}function isBlockNode(e){var t=e.pmViewDesc;return t&&t.node&&t.node.isBlock}function setSelFocus(e,t,n,o){if(selectionCollapsed(t)){var r=document.createRange();r.setEnd(n,o),r.setStart(n,o),t.removeAllRanges(),t.addRange(r)}else t.extend&&t.extend(n,o);e.domObserver.setCurSelection();var i=e.state;setTimeout((function(){e.state==i&&selectionToDOM(e)}),50)}function selectVertically(e,t,n){var o=e.state.selection;if(o instanceof prosemirrorState.TextSelection&&!o.empty||n.indexOf("s")>-1)return!1;if(mac&&n.indexOf("m")>-1)return!1;var r=o.$from,i=o.$to;if(!r.parent.inlineContent||e.endOfTextblock(t<0?"up":"down")){var s=moveSelectionBlock(e.state,t);if(s&&s instanceof prosemirrorState.NodeSelection)return apply(e,s)}if(!r.parent.inlineContent){var a=t<0?r:i,l=o instanceof prosemirrorState.AllSelection?prosemirrorState.Selection.near(a,t):prosemirrorState.Selection.findFrom(a,t);return!!l&&apply(e,l)}return!1}function stopNativeHorizontalDelete(e,t){if(!(e.state.selection instanceof prosemirrorState.TextSelection))return!0;var n=e.state.selection,o=n.$head,r=n.$anchor,i=n.empty;if(!o.sameParent(r))return!0;if(!i)return!1;if(e.endOfTextblock(t>0?"forward":"backward"))return!0;var s=!o.textOffset&&(t<0?o.nodeBefore:o.nodeAfter);if(s&&!s.isText){var a=e.state.tr;return t<0?a.delete(o.pos-s.nodeSize,o.pos):a.delete(o.pos,o.pos+s.nodeSize),e.dispatch(a),!0}return!1}function switchEditable(e,t,n){e.domObserver.stop(),t.contentEditable=n,e.domObserver.start()}function safariDownArrowBug(e){if(!safari||e.state.selection.$head.parentOffset>0)return!1;var t=e.domSelection(),n=t.focusNode,o=t.focusOffset;if(n&&1==n.nodeType&&0==o&&n.firstChild&&"false"==n.firstChild.contentEditable){var r=n.firstChild;switchEditable(e,r,"true"),setTimeout((function(){return switchEditable(e,r,"false")}),20)}return!1}function getMods(e){var t="";return e.ctrlKey&&(t+="c"),e.metaKey&&(t+="m"),e.altKey&&(t+="a"),e.shiftKey&&(t+="s"),t}function captureKeyDown(e,t){var n=t.keyCode,o=getMods(t);return 8==n||mac&&72==n&&"c"==o?stopNativeHorizontalDelete(e,-1)||skipIgnoredNodesLeft(e):46==n||mac&&68==n&&"c"==o?stopNativeHorizontalDelete(e,1)||skipIgnoredNodesRight(e):13==n||27==n||(37==n||mac&&66==n&&"c"==o?selectHorizontally(e,-1,o)||skipIgnoredNodesLeft(e):39==n||mac&&70==n&&"c"==o?selectHorizontally(e,1,o)||skipIgnoredNodesRight(e):38==n||mac&&80==n&&"c"==o?selectVertically(e,-1,o)||skipIgnoredNodesLeft(e):40==n||mac&&78==n&&"c"==o?safariDownArrowBug(e)||selectVertically(e,1,o)||skipIgnoredNodesRight(e):o==(mac?"m":"c")&&(66==n||73==n||89==n||90==n))}function serializeForClipboard(e,t){for(var n=[],o=t.content,r=t.openStart,i=t.openEnd;r>1&&i>1&&1==o.childCount&&1==o.firstChild.childCount;){r--,i--;var s=o.firstChild;n.push(s.type.name,s.attrs!=s.type.defaultAttrs?s.attrs:null),o=s.content}var a=e.someProp("clipboardSerializer")||prosemirrorModel.DOMSerializer.fromSchema(e.state.schema),l=detachedDoc(),c=l.createElement("div");c.appendChild(a.serializeFragment(o,{document:l}));for(var d,u=c.firstChild,h=0;u&&1==u.nodeType&&(d=wrapMap[u.nodeName.toLowerCase()]);){for(var f=d.length-1;f>=0;f--){for(var p=l.createElement(d[f]);c.firstChild;)p.appendChild(c.firstChild);c.appendChild(p),h++}u=c.firstChild}return u&&1==u.nodeType&&u.setAttribute("data-pm-slice","".concat(r," ").concat(i).concat(h?" -".concat(h):""," ").concat(JSON.stringify(n))),{dom:c,text:e.someProp("clipboardTextSerializer",(function(e){return e(t)}))||t.content.textBetween(0,t.content.size,"\n\n")}}function parseFromClipboard(e,t,n,o,r){var i,s,a=r.parent.type.spec.code;if(!n&&!t)return null;var l=t&&(o||a||!n);if(l){if(e.someProp("transformPastedText",(function(e){t=e(t,a||o)})),a)return t?new prosemirrorModel.Slice(prosemirrorModel.Fragment.from(e.state.schema.text(t.replace(/\r\n?/g,"\n"))),0,0):prosemirrorModel.Slice.empty;var c=e.someProp("clipboardTextParser",(function(e){return e(t,r,o)}));if(c)s=c;else{var d=r.marks(),u=e.state.schema,h=prosemirrorModel.DOMSerializer.fromSchema(u);i=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach((function(e){var t=i.appendChild(document.createElement("p"));e&&t.appendChild(h.serializeNode(u.text(e,d)))}))}}else e.someProp("transformPastedHTML",(function(e){n=e(n)})),i=readHTML(n),webkit&&restoreReplacedSpaces(i);var f=i&&i.querySelector("[data-pm-slice]"),p=f&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(f.getAttribute("data-pm-slice")||"");if(p&&p[3])for(var m=+p[3];m>0&&i.firstChild;m--)i=i.firstChild;if(!s){var v=e.someProp("clipboardParser")||e.someProp("domParser")||prosemirrorModel.DOMParser.fromSchema(e.state.schema);s=v.parseSlice(i,{preserveWhitespace:!(!l&&!p),context:r,ruleFromNode:function(e){return"BR"!=e.nodeName||e.nextSibling||!e.parentNode||inlineParents.test(e.parentNode.nodeName)?null:{ignore:!0}}})}if(p)s=addContext(closeSlice(s,+p[1],+p[2]),p[4]);else if((s=prosemirrorModel.Slice.maxOpen(normalizeSiblings(s.content,r),!0)).openStart||s.openEnd){for(var g=0,y=0,S=s.content.firstChild;g<s.openStart&&!S.type.spec.isolating;g++,S=S.firstChild);for(var k=s.content.lastChild;y<s.openEnd&&!k.type.spec.isolating;y++,k=k.lastChild);s=closeSlice(s,g,y)}return e.someProp("transformPasted",(function(e){s=e(s)})),s}var inlineParents=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function normalizeSiblings(e,t){if(e.childCount<2)return e;for(var n=function(n){var o=t.node(n).contentMatchAt(t.index(n)),r=void 0,i=[];if(e.forEach((function(e){if(i){var t,n=o.findWrapping(e.type);if(!n)return i=null;if(t=i.length&&r.length&&addToSibling(n,r,e,i[i.length-1],0))i[i.length-1]=t;else{i.length&&(i[i.length-1]=closeRight(i[i.length-1],r.length));var s=withWrappers(e,n);i.push(s),o=o.matchType(s.type),r=n}}})),i)return{v:prosemirrorModel.Fragment.from(i)}},o=t.depth;o>=0;o--){var r=n(o);if("object"===_typeof(r))return r.v}return e}function withWrappers(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=t.length-1;o>=n;o--)e=t[o].create(null,prosemirrorModel.Fragment.from(e));return e}function addToSibling(e,t,n,o,r){if(r<e.length&&r<t.length&&e[r]==t[r]){var i=addToSibling(e,t,n,o.lastChild,r+1);if(i)return o.copy(o.content.replaceChild(o.childCount-1,i));if(o.contentMatchAt(o.childCount).matchType(r==e.length-1?n.type:e[r+1]))return o.copy(o.content.append(prosemirrorModel.Fragment.from(withWrappers(n,e,r+1))))}}function closeRight(e,t){if(0==t)return e;var n=e.content.replaceChild(e.childCount-1,closeRight(e.lastChild,t-1)),o=e.contentMatchAt(e.childCount).fillBefore(prosemirrorModel.Fragment.empty,!0);return e.copy(n.append(o))}function closeRange(e,t,n,o,r,i){var s=t<0?e.firstChild:e.lastChild,a=s.content;return r<o-1&&(a=closeRange(a,t,n,o,r+1,i)),r>=n&&(a=t<0?s.contentMatchAt(0).fillBefore(a,e.childCount>1||i<=r).append(a):a.append(s.contentMatchAt(s.childCount).fillBefore(prosemirrorModel.Fragment.empty,!0))),e.replaceChild(t<0?0:e.childCount-1,s.copy(a))}function closeSlice(e,t,n){return t<e.openStart&&(e=new prosemirrorModel.Slice(closeRange(e.content,-1,t,e.openStart,0,e.openEnd),t,e.openEnd)),n<e.openEnd&&(e=new prosemirrorModel.Slice(closeRange(e.content,1,n,e.openEnd,0,0),e.openStart,n)),e}var wrapMap={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},_detachedDoc=null;function detachedDoc(){return _detachedDoc||(_detachedDoc=document.implementation.createHTMLDocument("title"))}function readHTML(e){var t=/^(\s*<meta [^>]*>)*/.exec(e);t&&(e=e.slice(t[0].length));var n,o=detachedDoc().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(e);if((n=r&&wrapMap[r[1].toLowerCase()])&&(e=n.map((function(e){return"<"+e+">"})).join("")+e+n.map((function(e){return"</"+e+">"})).reverse().join("")),o.innerHTML=e,n)for(var i=0;i<n.length;i++)o=o.querySelector(n[i])||o;return o}function restoreReplacedSpaces(e){for(var t=e.querySelectorAll(chrome?"span:not([class]):not([style])":"span.Apple-converted-space"),n=0;n<t.length;n++){var o=t[n];1==o.childNodes.length&&"\xa0"==o.textContent&&o.parentNode&&o.parentNode.replaceChild(e.ownerDocument.createTextNode(" "),o)}}function addContext(e,t){if(!e.size)return e;var n,o=e.content.firstChild.type.schema;try{n=JSON.parse(t)}catch(c){return e}for(var r=e.content,i=e.openStart,s=e.openEnd,a=n.length-2;a>=0;a-=2){var l=o.nodes[n[a]];if(!l||l.hasRequiredAttrs())break;r=prosemirrorModel.Fragment.from(l.create(n[a+1],r)),i++,s++}return new prosemirrorModel.Slice(r,i,s)}var handlers={},editHandlers={},passiveHandlers={touchstart:!0,touchmove:!0},InputState=_createClass((function e(){_classCallCheck(this,e),this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}));function initInput(e){var t=function(t){var n=handlers[t];e.dom.addEventListener(t,e.input.eventHandlers[t]=function(t){!eventBelongsToView(e,t)||runCustomHandler(e,t)||!e.editable&&t.type in editHandlers||n(e,t)},passiveHandlers[t]?{passive:!0}:void 0)};for(var n in handlers)t(n);safari&&e.dom.addEventListener("input",(function(){return null})),ensureListeners(e)}function setSelectionOrigin(e,t){e.input.lastSelectionOrigin=t,e.input.lastSelectionTime=Date.now()}function destroyInput(e){for(var t in e.domObserver.stop(),e.input.eventHandlers)e.dom.removeEventListener(t,e.input.eventHandlers[t]);clearTimeout(e.input.composingTimeout),clearTimeout(e.input.lastIOSEnterFallbackTimeout)}function ensureListeners(e){e.someProp("handleDOMEvents",(function(t){for(var n in t)e.input.eventHandlers[n]||e.dom.addEventListener(n,e.input.eventHandlers[n]=function(t){return runCustomHandler(e,t)})}))}function runCustomHandler(e,t){return e.someProp("handleDOMEvents",(function(n){var o=n[t.type];return!!o&&(o(e,t)||t.defaultPrevented)}))}function eventBelongsToView(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(var n=t.target;n!=e.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(t))return!1;return!0}function _dispatchEvent(e,t){runCustomHandler(e,t)||!handlers[t.type]||!e.editable&&t.type in editHandlers||handlers[t.type](e,t)}function eventCoords(e){return{left:e.clientX,top:e.clientY}}function isNear(e,t){var n=t.x-e.clientX,o=t.y-e.clientY;return n*n+o*o<100}function runHandlerOnContext(e,t,n,o,r){if(-1==o)return!1;for(var i=e.state.doc.resolve(o),s=function(o){if(e.someProp(t,(function(t){return o>i.depth?t(e,n,i.nodeAfter,i.before(o),r,!0):t(e,n,i.node(o),i.before(o),r,!1)})))return{v:!0}},a=i.depth+1;a>0;a--){var l=s(a);if("object"===_typeof(l))return l.v}return!1}function updateSelection(e,t,n){e.focused||e.focus();var o=e.state.tr.setSelection(t);"pointer"==n&&o.setMeta("pointer",!0),e.dispatch(o)}function selectClickedLeaf(e,t){if(-1==t)return!1;var n=e.state.doc.resolve(t),o=n.nodeAfter;return!!(o&&o.isAtom&&prosemirrorState.NodeSelection.isSelectable(o))&&(updateSelection(e,new prosemirrorState.NodeSelection(n),"pointer"),!0)}function selectClickedNode(e,t){if(-1==t)return!1;var n,o,r=e.state.selection;r instanceof prosemirrorState.NodeSelection&&(n=r.node);for(var i=e.state.doc.resolve(t),s=i.depth+1;s>0;s--){var a=s>i.depth?i.nodeAfter:i.node(s);if(prosemirrorState.NodeSelection.isSelectable(a)){o=n&&r.$from.depth>0&&s>=r.$from.depth&&i.before(r.$from.depth+1)==r.$from.pos?i.before(r.$from.depth):i.before(s);break}}return null!=o&&(updateSelection(e,prosemirrorState.NodeSelection.create(e.state.doc,o),"pointer"),!0)}function handleSingleClick(e,t,n,o,r){return runHandlerOnContext(e,"handleClickOn",t,n,o)||e.someProp("handleClick",(function(n){return n(e,t,o)}))||(r?selectClickedNode(e,n):selectClickedLeaf(e,n))}function handleDoubleClick(e,t,n,o){return runHandlerOnContext(e,"handleDoubleClickOn",t,n,o)||e.someProp("handleDoubleClick",(function(n){return n(e,t,o)}))}function handleTripleClick(e,t,n,o){return runHandlerOnContext(e,"handleTripleClickOn",t,n,o)||e.someProp("handleTripleClick",(function(n){return n(e,t,o)}))||defaultTripleClick(e,n,o)}function defaultTripleClick(e,t,n){if(0!=n.button)return!1;var o=e.state.doc;if(-1==t)return!!o.inlineContent&&(updateSelection(e,prosemirrorState.TextSelection.create(o,0,o.content.size),"pointer"),!0);for(var r=o.resolve(t),i=r.depth+1;i>0;i--){var s=i>r.depth?r.nodeAfter:r.node(i),a=r.before(i);if(s.inlineContent)updateSelection(e,prosemirrorState.TextSelection.create(o,a+1,a+1+s.content.size),"pointer");else{if(!prosemirrorState.NodeSelection.isSelectable(s))continue;updateSelection(e,prosemirrorState.NodeSelection.create(o,a),"pointer")}return!0}}function forceDOMFlush(e){return endComposition(e)}editHandlers.keydown=function(e,t){var n=t;if(e.input.shiftKey=16==n.keyCode||n.shiftKey,!inOrNearComposition(e,n)&&(e.input.lastKeyCode=n.keyCode,e.input.lastKeyCodeTime=Date.now(),!android||!chrome||13!=n.keyCode))if(229!=n.keyCode&&e.domObserver.forceFlush(),!ios||13!=n.keyCode||n.ctrlKey||n.altKey||n.metaKey)e.someProp("handleKeyDown",(function(t){return t(e,n)}))||captureKeyDown(e,n)?n.preventDefault():setSelectionOrigin(e,"key");else{var o=Date.now();e.input.lastIOSEnter=o,e.input.lastIOSEnterFallbackTimeout=setTimeout((function(){e.input.lastIOSEnter==o&&(e.someProp("handleKeyDown",(function(t){return t(e,keyEvent(13,"Enter"))})),e.input.lastIOSEnter=0)}),200)}},editHandlers.keyup=function(e,t){16==t.keyCode&&(e.input.shiftKey=!1)},editHandlers.keypress=function(e,t){var n=t;if(!(inOrNearComposition(e,n)||!n.charCode||n.ctrlKey&&!n.altKey||mac&&n.metaKey))if(e.someProp("handleKeyPress",(function(t){return t(e,n)})))n.preventDefault();else{var o=e.state.selection;if(!(o instanceof prosemirrorState.TextSelection)||!o.$from.sameParent(o.$to)){var r=String.fromCharCode(n.charCode);e.someProp("handleTextInput",(function(t){return t(e,o.$from.pos,o.$to.pos,r)}))||e.dispatch(e.state.tr.insertText(r).scrollIntoView()),n.preventDefault()}}};var selectNodeModifier=mac?"metaKey":"ctrlKey";handlers.mousedown=function(e,t){var n=t;e.input.shiftKey=n.shiftKey;var o=forceDOMFlush(e),r=Date.now(),i="singleClick";r-e.input.lastClick.time<500&&isNear(n,e.input.lastClick)&&!n[selectNodeModifier]&&("singleClick"==e.input.lastClick.type?i="doubleClick":"doubleClick"==e.input.lastClick.type&&(i="tripleClick")),e.input.lastClick={time:r,x:n.clientX,y:n.clientY,type:i};var s=e.posAtCoords(eventCoords(n));s&&("singleClick"==i?(e.input.mouseDown&&e.input.mouseDown.done(),e.input.mouseDown=new MouseDown(e,s,n,!!o)):("doubleClick"==i?handleDoubleClick:handleTripleClick)(e,s.pos,s.inside,n)?n.preventDefault():setSelectionOrigin(e,"pointer"))};var MouseDown=function(){function e(t,n,o,r){var i,s,a=this;if(_classCallCheck(this,e),this.view=t,this.pos=n,this.event=o,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=t.state.doc,this.selectNode=!!o[selectNodeModifier],this.allowDefault=o.shiftKey,n.inside>-1)i=t.state.doc.nodeAt(n.inside),s=n.inside;else{var l=t.state.doc.resolve(n.pos);i=l.parent,s=l.depth?l.before():0}var c=r?null:o.target,d=c?t.docView.nearestDesc(c,!0):null;this.target=d?d.dom:null;var u=t.state.selection;(0==o.button&&i.type.spec.draggable&&!1!==i.type.spec.selectable||u instanceof prosemirrorState.NodeSelection&&u.from<=s&&u.to>s)&&(this.mightDrag={node:i,pos:s,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!gecko||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout((function(){a.view.input.mouseDown==a&&a.target.setAttribute("contentEditable","false")}),20),this.view.domObserver.start()),t.root.addEventListener("mouseup",this.up=this.up.bind(this)),t.root.addEventListener("mousemove",this.move=this.move.bind(this)),setSelectionOrigin(t,"pointer")}return _createClass(e,[{key:"done",value:function(){var e=this;this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout((function(){return selectionToDOM(e.view)})),this.view.input.mouseDown=null}},{key:"up",value:function(e){if(this.done(),this.view.dom.contains(e.target)){var t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(eventCoords(e))),this.updateAllowDefault(e),this.allowDefault||!t?setSelectionOrigin(this.view,"pointer"):handleSingleClick(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():0==e.button&&(this.flushed||safari&&this.mightDrag&&!this.mightDrag.node.isAtom||chrome&&!(this.view.state.selection instanceof prosemirrorState.TextSelection)&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(updateSelection(this.view,prosemirrorState.Selection.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):setSelectionOrigin(this.view,"pointer")}}},{key:"move",value:function(e){this.updateAllowDefault(e),setSelectionOrigin(this.view,"pointer"),0==e.buttons&&this.done()}},{key:"updateAllowDefault",value:function(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}]),e}();function inOrNearComposition(e,t){return!!e.composing||!!(safari&&Math.abs(t.timeStamp-e.input.compositionEndedAt)<500)&&(e.input.compositionEndedAt=-2e8,!0)}handlers.touchstart=function(e){e.input.lastTouch=Date.now(),forceDOMFlush(e),setSelectionOrigin(e,"pointer")},handlers.touchmove=function(e){e.input.lastTouch=Date.now(),setSelectionOrigin(e,"pointer")},handlers.contextmenu=function(e){return forceDOMFlush(e)};var timeoutComposition=android?5e3:-1;function scheduleComposeEnd(e,t){clearTimeout(e.input.composingTimeout),t>-1&&(e.input.composingTimeout=setTimeout((function(){return endComposition(e)}),t))}function clearComposition(e){for(e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=timestampFromCustomEvent());e.input.compositionNodes.length>0;)e.input.compositionNodes.pop().markParentsDirty()}function timestampFromCustomEvent(){var e=document.createEvent("Event");return e.initEvent("event",!0,!0),e.timeStamp}function endComposition(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!(android&&e.domObserver.flushingSoon>=0)){if(e.domObserver.forceFlush(),clearComposition(e),t||e.docView&&e.docView.dirty){var n=selectionFromDOM(e);return n&&!n.eq(e.state.selection)?e.dispatch(e.state.tr.setSelection(n)):e.updateState(e.state),!0}return!1}}function captureCopy(e,t){if(e.dom.parentNode){var n=e.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(t),n.style.cssText="position: fixed; left: -10000px; top: 10px";var o=getSelection(),r=document.createRange();r.selectNodeContents(t),e.dom.blur(),o.removeAllRanges(),o.addRange(r),setTimeout((function(){n.parentNode&&n.parentNode.removeChild(n),e.focus()}),50)}}editHandlers.compositionstart=editHandlers.compositionupdate=function(e){if(!e.composing){e.domObserver.flush();var t=e.state,n=t.selection.$from;if(t.selection.empty&&(t.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some((function(e){return!1===e.type.spec.inclusive}))))e.markCursor=e.state.storedMarks||n.marks(),endComposition(e,!0),e.markCursor=null;else if(endComposition(e),gecko&&t.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length)for(var o=e.domSelection(),r=o.focusNode,i=o.focusOffset;r&&1==r.nodeType&&0!=i;){var s=i<0?r.lastChild:r.childNodes[i-1];if(!s)break;if(3==s.nodeType){o.collapse(s,s.nodeValue.length);break}r=s,i=-1}e.input.composing=!0}scheduleComposeEnd(e,timeoutComposition)},editHandlers.compositionend=function(e,t){e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=t.timeStamp,scheduleComposeEnd(e,20))};var brokenClipboardAPI=ie&&ie_version<15||ios&&webkit_version<604;function sliceSingleNode(e){return 0==e.openStart&&0==e.openEnd&&1==e.content.childCount?e.content.firstChild:null}function capturePaste(e,t){if(e.dom.parentNode){var n=e.input.shiftKey||e.state.selection.$from.parent.type.spec.code,o=e.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(o.contentEditable="true"),o.style.cssText="position: fixed; left: -10000px; top: 10px",o.focus(),setTimeout((function(){e.focus(),o.parentNode&&o.parentNode.removeChild(o),n?doPaste(e,o.value,null,t):doPaste(e,o.textContent,o.innerHTML,t)}),50)}}function doPaste(e,t,n,o){var r=parseFromClipboard(e,t,n,e.input.shiftKey,e.state.selection.$from);if(e.someProp("handlePaste",(function(t){return t(e,o,r||prosemirrorModel.Slice.empty)})))return!0;if(!r)return!1;var i=sliceSingleNode(r),s=i?e.state.tr.replaceSelectionWith(i,e.input.shiftKey):e.state.tr.replaceSelection(r);return e.dispatch(s.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}handlers.copy=editHandlers.cut=function(e,t){var n=t,o=e.state.selection,r="cut"==n.type;if(!o.empty){var i=brokenClipboardAPI?null:n.clipboardData,s=serializeForClipboard(e,o.content()),a=s.dom,l=s.text;i?(n.preventDefault(),i.clearData(),i.setData("text/html",a.innerHTML),i.setData("text/plain",l)):captureCopy(e,a),r&&e.dispatch(e.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))}},editHandlers.paste=function(e,t){var n=t;if(!e.composing||android){var o=brokenClipboardAPI?null:n.clipboardData;o&&doPaste(e,o.getData("text/plain"),o.getData("text/html"),n)?n.preventDefault():capturePaste(e,n)}};var Dragging=_createClass((function e(t,n){_classCallCheck(this,e),this.slice=t,this.move=n})),dragCopyModifier=mac?"altKey":"ctrlKey";for(var prop in handlers.dragstart=function(e,t){var n=t,o=e.input.mouseDown;if(o&&o.done(),n.dataTransfer){var r=e.state.selection,i=r.empty?null:e.posAtCoords(eventCoords(n));if(i&&i.pos>=r.from&&i.pos<=(r instanceof prosemirrorState.NodeSelection?r.to-1:r.to));else if(o&&o.mightDrag)e.dispatch(e.state.tr.setSelection(prosemirrorState.NodeSelection.create(e.state.doc,o.mightDrag.pos)));else if(n.target&&1==n.target.nodeType){var s=e.docView.nearestDesc(n.target,!0);s&&s.node.type.spec.draggable&&s!=e.docView&&e.dispatch(e.state.tr.setSelection(prosemirrorState.NodeSelection.create(e.state.doc,s.posBefore)))}var a=e.state.selection.content(),l=serializeForClipboard(e,a),c=l.dom,d=l.text;n.dataTransfer.clearData(),n.dataTransfer.setData(brokenClipboardAPI?"Text":"text/html",c.innerHTML),n.dataTransfer.effectAllowed="copyMove",brokenClipboardAPI||n.dataTransfer.setData("text/plain",d),e.dragging=new Dragging(a,!n[dragCopyModifier])}},handlers.dragend=function(e){var t=e.dragging;window.setTimeout((function(){e.dragging==t&&(e.dragging=null)}),50)},editHandlers.dragover=editHandlers.dragenter=function(e,t){return t.preventDefault()},editHandlers.drop=function(e,t){var n=t,o=e.dragging;if(e.dragging=null,n.dataTransfer){var r=e.posAtCoords(eventCoords(n));if(r){var i=e.state.doc.resolve(r.pos),s=o&&o.slice;s?e.someProp("transformPasted",(function(e){s=e(s)})):s=parseFromClipboard(e,n.dataTransfer.getData(brokenClipboardAPI?"Text":"text/plain"),brokenClipboardAPI?null:n.dataTransfer.getData("text/html"),!1,i);var a=!(!o||n[dragCopyModifier]);if(e.someProp("handleDrop",(function(t){return t(e,n,s||prosemirrorModel.Slice.empty,a)})))n.preventDefault();else if(s){n.preventDefault();var l=s?prosemirrorTransform.dropPoint(e.state.doc,i.pos,s):i.pos;null==l&&(l=i.pos);var c=e.state.tr;a&&c.deleteSelection();var d=c.mapping.map(l),u=0==s.openStart&&0==s.openEnd&&1==s.content.childCount,h=c.doc;if(u?c.replaceRangeWith(d,d,s.content.firstChild):c.replaceRange(d,d,s),!c.doc.eq(h)){var f=c.doc.resolve(d);if(u&&prosemirrorState.NodeSelection.isSelectable(s.content.firstChild)&&f.nodeAfter&&f.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new prosemirrorState.NodeSelection(f));else{var p=c.mapping.map(l);c.mapping.maps[c.mapping.maps.length-1].forEach((function(e,t,n,o){return p=o})),c.setSelection(selectionBetween(e,f,c.doc.resolve(p)))}e.focus(),e.dispatch(c.setMeta("uiEvent","drop"))}}}}},handlers.focus=function(e){e.input.lastFocus=Date.now(),e.focused||(e.domObserver.stop(),e.dom.classList.add("ProseMirror-focused"),e.domObserver.start(),e.focused=!0,setTimeout((function(){e.docView&&e.hasFocus()&&!e.domObserver.currentSelection.eq(e.domSelection())&&selectionToDOM(e)}),20))},handlers.blur=function(e,t){var n=t;e.focused&&(e.domObserver.stop(),e.dom.classList.remove("ProseMirror-focused"),e.domObserver.start(),n.relatedTarget&&e.dom.contains(n.relatedTarget)&&e.domObserver.currentSelection.clear(),e.focused=!1)},handlers.beforeinput=function(e,t){if(chrome&&android&&"deleteContentBackward"==t.inputType){e.domObserver.flushSoon();var n=e.input.domChangeCount;setTimeout((function(){if(e.input.domChangeCount==n&&(e.dom.blur(),e.focus(),!e.someProp("handleKeyDown",(function(t){return t(e,keyEvent(8,"Backspace"))})))){var t=e.state.selection.$cursor;t&&t.pos>0&&e.dispatch(e.state.tr.delete(t.pos-1,t.pos).scrollIntoView())}}),50)}},editHandlers)handlers[prop]=editHandlers[prop];function compareObjs(e,t){if(e==t)return!0;for(var n in e)if(e[n]!==t[n])return!1;for(var o in t)if(!(o in e))return!1;return!0}var WidgetType=function(){function e(t,n){_classCallCheck(this,e),this.toDOM=t,this.spec=n||noSpec,this.side=this.spec.side||0}return _createClass(e,[{key:"map",value:function(e,t,n,o){var r=e.mapResult(t.from+o,this.side<0?-1:1),i=r.pos;return r.deleted?null:new Decoration(i-n,i-n,this)}},{key:"valid",value:function(){return!0}},{key:"eq",value:function(t){return this==t||t instanceof e&&(this.spec.key&&this.spec.key==t.spec.key||this.toDOM==t.toDOM&&compareObjs(this.spec,t.spec))}},{key:"destroy",value:function(e){this.spec.destroy&&this.spec.destroy(e)}}]),e}(),InlineType=function(){function e(t,n){_classCallCheck(this,e),this.attrs=t,this.spec=n||noSpec}return _createClass(e,[{key:"map",value:function(e,t,n,o){var r=e.map(t.from+o,this.spec.inclusiveStart?-1:1)-n,i=e.map(t.to+o,this.spec.inclusiveEnd?1:-1)-n;return r>=i?null:new Decoration(r,i,this)}},{key:"valid",value:function(e,t){return t.from<t.to}},{key:"eq",value:function(t){return this==t||t instanceof e&&compareObjs(this.attrs,t.attrs)&&compareObjs(this.spec,t.spec)}},{key:"destroy",value:function(){}}],[{key:"is",value:function(t){return t.type instanceof e}}]),e}(),NodeType=function(){function e(t,n){_classCallCheck(this,e),this.attrs=t,this.spec=n||noSpec}return _createClass(e,[{key:"map",value:function(e,t,n,o){var r=e.mapResult(t.from+o,1);if(r.deleted)return null;var i=e.mapResult(t.to+o,-1);return i.deleted||i.pos<=r.pos?null:new Decoration(r.pos-n,i.pos-n,this)}},{key:"valid",value:function(e,t){var n,o=e.content.findIndex(t.from),r=o.index,i=o.offset;return i==t.from&&!(n=e.child(r)).isText&&i+n.nodeSize==t.to}},{key:"eq",value:function(t){return this==t||t instanceof e&&compareObjs(this.attrs,t.attrs)&&compareObjs(this.spec,t.spec)}},{key:"destroy",value:function(){}}]),e}(),Decoration=function(){function e(t,n,o){_classCallCheck(this,e),this.from=t,this.to=n,this.type=o}return _createClass(e,[{key:"copy",value:function(t,n){return new e(t,n,this.type)}},{key:"eq",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}},{key:"map",value:function(e,t,n){return this.type.map(e,this,t,n)}},{key:"spec",get:function(){return this.type.spec}},{key:"inline",get:function(){return this.type instanceof InlineType}}],[{key:"widget",value:function(t,n,o){return new e(t,t,new WidgetType(n,o))}},{key:"inline",value:function(t,n,o,r){return new e(t,n,new InlineType(o,r))}},{key:"node",value:function(t,n,o,r){return new e(t,n,new NodeType(o,r))}}]),e}(),none=[],noSpec={},DecorationSet=function(){function e(t,n){_classCallCheck(this,e),this.local=t.length?t:none,this.children=n.length?n:none}return _createClass(e,[{key:"find",value:function(e,t,n){var o=[];return this.findInner(null==e?0:e,null==t?1e9:t,o,0,n),o}},{key:"findInner",value:function(e,t,n,o,r){for(var i=0;i<this.local.length;i++){var s=this.local[i];s.from<=t&&s.to>=e&&(!r||r(s.spec))&&n.push(s.copy(s.from+o,s.to+o))}for(var a=0;a<this.children.length;a+=3)if(this.children[a]<t&&this.children[a+1]>e){var l=this.children[a]+1;this.children[a+2].findInner(e-l,t-l,n,o+l,r)}}},{key:"map",value:function(e,t,n){return this==empty||0==e.maps.length?this:this.mapInner(e,t,0,0,n||noSpec)}},{key:"mapInner",value:function(t,n,o,r,i){for(var s,a=0;a<this.local.length;a++){var l=this.local[a].map(t,o,r);l&&l.type.valid(n,l)?(s||(s=[])).push(l):i.onRemove&&i.onRemove(this.local[a].spec)}return this.children.length?mapChildren(this.children,s||[],t,n,o,r,i):s?new e(s.sort(byPos),none):empty}},{key:"add",value:function(t,n){return n.length?this==empty?e.create(t,n):this.addInner(t,n,0):this}},{key:"addInner",value:function(t,n,o){var r,i=this,s=0;t.forEach((function(e,t){var a,l=t+o;if(a=takeSpansForNode(n,e,l)){for(r||(r=i.children.slice());s<r.length&&r[s]<t;)s+=3;r[s]==t?r[s+2]=r[s+2].addInner(e,a,l+1):r.splice(s,0,t,t+e.nodeSize,buildTree(a,e,l+1,noSpec)),s+=3}}));for(var a=moveSpans(s?withoutNulls(n):n,-o),l=0;l<a.length;l++)a[l].type.valid(t,a[l])||a.splice(l--,1);return new e(a.length?this.local.concat(a).sort(byPos):this.local,r||this.children)}},{key:"remove",value:function(e){return 0==e.length||this==empty?this:this.removeInner(e,0)}},{key:"removeInner",value:function(t,n){for(var o=this.children,r=this.local,i=0;i<o.length;i+=3){for(var s,a=void 0,l=o[i]+n,c=o[i+1]+n,d=0;d<t.length;d++)(s=t[d])&&s.from>l&&s.to<c&&(t[d]=null,(a||(a=[])).push(s));if(a){o==this.children&&(o=this.children.slice());var u=o[i+2].removeInner(a,l+1);u!=empty?o[i+2]=u:(o.splice(i,3),i-=3)}}if(r.length)for(var h,f=0;f<t.length;f++)if(h=t[f])for(var p=0;p<r.length;p++)r[p].eq(h,n)&&(r==this.local&&(r=this.local.slice()),r.splice(p--,1));return o==this.children&&r==this.local?this:r.length||o.length?new e(r,o):empty}},{key:"forChild",value:function(t,n){if(this==empty)return this;if(n.isLeaf)return e.empty;for(var o,r,i=0;i<this.children.length;i+=3)if(this.children[i]>=t){this.children[i]==t&&(o=this.children[i+2]);break}for(var s=t+1,a=s+n.content.size,l=0;l<this.local.length;l++){var c=this.local[l];if(c.from<a&&c.to>s&&c.type instanceof InlineType){var d=Math.max(s,c.from)-s,u=Math.min(a,c.to)-s;d<u&&(r||(r=[])).push(c.copy(d,u))}}if(r){var h=new e(r.sort(byPos),none);return o?new DecorationGroup([h,o]):h}return o||empty}},{key:"eq",value:function(t){if(this==t)return!0;if(!(t instanceof e)||this.local.length!=t.local.length||this.children.length!=t.children.length)return!1;for(var n=0;n<this.local.length;n++)if(!this.local[n].eq(t.local[n]))return!1;for(var o=0;o<this.children.length;o+=3)if(this.children[o]!=t.children[o]||this.children[o+1]!=t.children[o+1]||!this.children[o+2].eq(t.children[o+2]))return!1;return!0}},{key:"locals",value:function(e){return removeOverlap(this.localsInner(e))}},{key:"localsInner",value:function(e){if(this==empty)return none;if(e.inlineContent||!this.local.some(InlineType.is))return this.local;for(var t=[],n=0;n<this.local.length;n++)this.local[n].type instanceof InlineType||t.push(this.local[n]);return t}}],[{key:"create",value:function(e,t){return t.length?buildTree(t,e,0,noSpec):empty}}]),e}();DecorationSet.empty=new DecorationSet([],[]),DecorationSet.removeOverlap=removeOverlap;var empty=DecorationSet.empty,DecorationGroup=function(){function e(t){_classCallCheck(this,e),this.members=t}return _createClass(e,[{key:"map",value:function(t,n){var o=this.members.map((function(e){return e.map(t,n,noSpec)}));return e.from(o)}},{key:"forChild",value:function(t,n){if(n.isLeaf)return DecorationSet.empty;for(var o=[],r=0;r<this.members.length;r++){var i=this.members[r].forChild(t,n);i!=empty&&(i instanceof e?o=o.concat(i.members):o.push(i))}return e.from(o)}},{key:"eq",value:function(t){if(!(t instanceof e)||t.members.length!=this.members.length)return!1;for(var n=0;n<this.members.length;n++)if(!this.members[n].eq(t.members[n]))return!1;return!0}},{key:"locals",value:function(e){for(var t,n=!0,o=0;o<this.members.length;o++){var r=this.members[o].localsInner(e);if(r.length)if(t){n&&(t=t.slice(),n=!1);for(var i=0;i<r.length;i++)t.push(r[i])}else t=r}return t?removeOverlap(n?t:t.sort(byPos)):none}}],[{key:"from",value:function(t){switch(t.length){case 0:return empty;case 1:return t[0];default:return new e(t)}}}]),e}();function mapChildren(e,t,n,o,r,i,s){for(var a=e.slice(),l=function(e,t){var o=0;n.maps[e].forEach((function(e,n,i,s){for(var l=s-i-(n-e),c=0;c<a.length;c+=3){var d=a[c+1];if(!(d<0||e>d+t-o)){var u=a[c]+t-o;n>=u?a[c+1]=e<=u?-2:-1:i>=r&&l&&(a[c]+=l,a[c+1]+=l)}}o+=l})),t=n.maps[e].map(t,-1),d=t},c=0,d=i;c<n.maps.length;c++)l(c,d);for(var u=!1,h=0;h<a.length;h+=3)if(a[h+1]<0){if(-2==a[h+1]){u=!0,a[h+1]=-1;continue}var f=n.map(e[h]+i),p=f-r;if(p<0||p>=o.content.size){u=!0;continue}var m=n.map(e[h+1]+i,-1)-r,v=o.content.findIndex(p),g=v.index,y=v.offset,S=o.maybeChild(g);if(S&&y==p&&y+S.nodeSize==m){var k=a[h+2].mapInner(n,S,f+1,e[h]+i+1,s);k!=empty?(a[h]=p,a[h+1]=m,a[h+2]=k):(a[h+1]=-2,u=!0)}else u=!0}if(u){var D=buildTree(mapAndGatherRemainingDecorations(a,e,t,n,r,i,s),o,0,s);t=D.local;for(var b=0;b<a.length;b+=3)a[b+1]<0&&(a.splice(b,3),b-=3);for(var C=0,w=0;C<D.children.length;C+=3){for(var O=D.children[C];w<a.length&&a[w]<O;)w+=3;a.splice(w,0,D.children[C],D.children[C+1],D.children[C+2])}}return new DecorationSet(t.sort(byPos),a)}function moveSpans(e,t){if(!t||!e.length)return e;for(var n=[],o=0;o<e.length;o++){var r=e[o];n.push(new Decoration(r.from+t,r.to+t,r.type))}return n}function mapAndGatherRemainingDecorations(e,t,n,o,r,i,s){function a(e,t){for(var i=0;i<e.local.length;i++){var l=e.local[i].map(o,r,t);l?n.push(l):s.onRemove&&s.onRemove(e.local[i].spec)}for(var c=0;c<e.children.length;c+=3)a(e.children[c+2],e.children[c]+t+1)}for(var l=0;l<e.length;l+=3)-1==e[l+1]&&a(e[l+2],t[l]+i+1);return n}function takeSpansForNode(e,t,n){if(t.isLeaf)return null;for(var o,r=n+t.nodeSize,i=null,s=0;s<e.length;s++)(o=e[s])&&o.from>n&&o.to<r&&((i||(i=[])).push(o),e[s]=null);return i}function withoutNulls(e){for(var t=[],n=0;n<e.length;n++)null!=e[n]&&t.push(e[n]);return t}function buildTree(e,t,n,o){var r=[],i=!1;t.forEach((function(t,s){var a=takeSpansForNode(e,t,s+n);if(a){i=!0;var l=buildTree(a,t,n+s+1,o);l!=empty&&r.push(s,s+t.nodeSize,l)}}));for(var s=moveSpans(i?withoutNulls(e):e,-n).sort(byPos),a=0;a<s.length;a++)s[a].type.valid(t,s[a])||(o.onRemove&&o.onRemove(s[a].spec),s.splice(a--,1));return s.length||r.length?new DecorationSet(s,r):empty}function byPos(e,t){return e.from-t.from||e.to-t.to}function removeOverlap(e){for(var t=e,n=0;n<t.length-1;n++){var o=t[n];if(o.from!=o.to)for(var r=n+1;r<t.length;r++){var i=t[r];if(i.from!=o.from){i.from<o.to&&(t==e&&(t=e.slice()),t[n]=o.copy(o.from,i.from),insertAhead(t,r,o.copy(i.from,o.to)));break}i.to!=o.to&&(t==e&&(t=e.slice()),t[r]=i.copy(i.from,o.to),insertAhead(t,r+1,i.copy(o.to,i.to)))}}return t}function insertAhead(e,t,n){for(;t<e.length&&byPos(n,e[t])>0;)t++;e.splice(t,0,n)}function viewDecorations(e){var t=[];return e.someProp("decorations",(function(n){var o=n(e.state);o&&o!=empty&&t.push(o)})),e.cursorWrapper&&t.push(DecorationSet.create(e.state.doc,[e.cursorWrapper.deco])),DecorationGroup.from(t)}var observeOptions={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},useCharData=ie&&ie_version<=11,SelectionState=function(){function e(){_classCallCheck(this,e),this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}return _createClass(e,[{key:"set",value:function(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}},{key:"clear",value:function(){this.anchorNode=this.focusNode=null}},{key:"eq",value:function(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}]),e}(),DOMObserver=function(){function e(t,n){var o=this;_classCallCheck(this,e),this.view=t,this.handleDOMChange=n,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new SelectionState,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.observer=window.MutationObserver&&new window.MutationObserver((function(e){for(var t=0;t<e.length;t++)o.queue.push(e[t]);ie&&ie_version<=11&&e.some((function(e){return"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length}))?o.flushSoon():o.flush()})),useCharData&&(this.onCharData=function(e){o.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),o.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}return _createClass(e,[{key:"flushSoon",value:function(){var e=this;this.flushingSoon<0&&(this.flushingSoon=window.setTimeout((function(){e.flushingSoon=-1,e.flush()}),20))}},{key:"forceFlush",value:function(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}},{key:"start",value:function(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,observeOptions)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}},{key:"stop",value:function(){var e=this;if(this.observer){var t=this.observer.takeRecords();if(t.length){for(var n=0;n<t.length;n++)this.queue.push(t[n]);window.setTimeout((function(){return e.flush()}),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}},{key:"connectSelection",value:function(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}},{key:"disconnectSelection",value:function(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}},{key:"suppressSelectionUpdates",value:function(){var e=this;this.suppressingSelectionUpdates=!0,setTimeout((function(){return e.suppressingSelectionUpdates=!1}),50)}},{key:"onSelectionChange",value:function(){if(hasFocusAndSelection(this.view)){if(this.suppressingSelectionUpdates)return selectionToDOM(this.view);if(ie&&ie_version<=11&&!this.view.state.selection.empty){var e=this.view.domSelection();if(e.focusNode&&isEquivalentPosition(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}},{key:"setCurSelection",value:function(){this.currentSelection.set(this.view.domSelection())}},{key:"ignoreSelectionChange",value:function(e){if(0==e.rangeCount)return!0;var t=e.getRangeAt(0).commonAncestorContainer,n=this.view.docView.nearestDesc(t);return n&&n.ignoreMutation({type:"selection",target:3==t.nodeType?t.parentNode:t})?(this.setCurSelection(),!0):void 0}},{key:"flush",value:function(){var e=this.view;if(e.docView&&!(this.flushingSoon>-1)){var t=this.observer?this.observer.takeRecords():[];this.queue.length&&(t=this.queue.concat(t),this.queue.length=0);var n=e.domSelection(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&hasFocusAndSelection(e)&&!this.ignoreSelectionChange(n),r=-1,i=-1,s=!1,a=[];if(e.editable)for(var l=0;l<t.length;l++){var c=this.registerMutation(t[l],a);c&&(r=r<0?c.from:Math.min(c.from,r),i=i<0?c.to:Math.max(c.to,i),c.typeOver&&(s=!0))}if(gecko&&a.length>1){var d=a.filter((function(e){return"BR"==e.nodeName}));if(2==d.length){var u=d[0],h=d[1];u.parentNode&&u.parentNode.parentNode==h.parentNode?h.remove():u.remove()}}var f=null;r<0&&o&&e.input.lastFocus>Date.now()-200&&e.input.lastTouch<Date.now()-300&&selectionCollapsed(n)&&(f=selectionFromDOM(e))&&f.eq(prosemirrorState.Selection.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,selectionToDOM(e),this.currentSelection.set(n),e.scrollToSelection()):(r>-1||o)&&(r>-1&&(e.docView.markDirty(r,i),checkCSS(e)),this.handleDOMChange(r,i,s,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||selectionToDOM(e),this.currentSelection.set(n))}}},{key:"registerMutation",value:function(e,t){if(t.indexOf(e.target)>-1)return null;var n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style")))return null;if(!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(var o=0;o<e.addedNodes.length;o++)t.push(e.addedNodes[o]);if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};var r=e.previousSibling,i=e.nextSibling;if(ie&&ie_version<=11&&e.addedNodes.length)for(var s=0;s<e.addedNodes.length;s++){var a=e.addedNodes[s],l=a.previousSibling,c=a.nextSibling;(!l||Array.prototype.indexOf.call(e.addedNodes,l)<0)&&(r=l),(!c||Array.prototype.indexOf.call(e.addedNodes,c)<0)&&(i=c)}var d=r&&r.parentNode==e.target?domIndex(r)+1:0,u=n.localPosFromDOM(e.target,d,-1),h=i&&i.parentNode==e.target?domIndex(i):e.target.childNodes.length;return{from:u,to:n.localPosFromDOM(e.target,h,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}}}]),e}(),cssChecked=new WeakMap,cssCheckWarned=!1;function checkCSS(e){if(!cssChecked.has(e)&&(cssChecked.set(e,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(e.dom).whiteSpace))){if(e.requiresGeckoHackNode=gecko,cssCheckWarned)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),cssCheckWarned=!0}}function parseBetween(e,t,n){var o,r=e.docView.parseRange(t,n),i=r.node,s=r.fromOffset,a=r.toOffset,l=r.from,c=r.to,d=e.domSelection(),u=d.anchorNode;if(u&&e.dom.contains(1==u.nodeType?u:u.parentNode)&&(o=[{node:u,offset:d.anchorOffset}],selectionCollapsed(d)||o.push({node:d.focusNode,offset:d.focusOffset})),chrome&&8===e.input.lastKeyCode)for(var h=a;h>s;h--){var f=i.childNodes[h-1],p=f.pmViewDesc;if("BR"==f.nodeName&&!p){a=h;break}if(!p||p.size)break}var m=e.state.doc,v=e.someProp("domParser")||prosemirrorModel.DOMParser.fromSchema(e.state.schema),g=m.resolve(l),y=null,S=v.parse(i,{topNode:g.parent,topMatch:g.parent.contentMatchAt(g.index()),topOpen:!0,from:s,to:a,preserveWhitespace:"pre"!=g.parent.type.whitespace||"full",findPositions:o,ruleFromNode:ruleFromNode,context:g});if(o&&null!=o[0].pos){var k=o[0].pos,D=o[1]&&o[1].pos;null==D&&(D=k),y={anchor:k+l,head:D+l}}return{doc:S,sel:y,from:l,to:c}}function ruleFromNode(e){var t=e.pmViewDesc;if(t)return t.parseRule();if("BR"==e.nodeName&&e.parentNode){if(safari&&/^(ul|ol)$/i.test(e.parentNode.nodeName)){var n=document.createElement("div");return n.appendChild(document.createElement("li")),{skip:n}}if(e.parentNode.lastChild==e||safari&&/^(tr|table)$/i.test(e.parentNode.nodeName))return{ignore:!0}}else if("IMG"==e.nodeName&&e.getAttribute("mark-placeholder"))return{ignore:!0};return null}function readDOMChange(e,t,n,o,r){if(t<0){var i=e.input.lastSelectionTime>Date.now()-50?e.input.lastSelectionOrigin:null,s=selectionFromDOM(e,i);if(s&&!e.state.selection.eq(s)){var a=e.state.tr.setSelection(s);"pointer"==i?a.setMeta("pointer",!0):"key"==i&&a.scrollIntoView(),e.dispatch(a)}}else{var l=e.state.doc.resolve(t),c=l.sharedDepth(n);t=l.before(c+1),n=e.state.doc.resolve(n).after(c+1);var d,u,h=e.state.selection,f=parseBetween(e,t,n),p=e.state.doc,m=p.slice(f.from,f.to);8===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime?(d=e.state.selection.to,u="end"):(d=e.state.selection.from,u="start"),e.input.lastKeyCode=null;var v=findDiff(m.content,f.doc.content,f.from,d,u);if((ios&&e.input.lastIOSEnter>Date.now()-225||android)&&r.some((function(e){return"DIV"==e.nodeName||"P"==e.nodeName}))&&(!v||v.endA>=v.endB)&&e.someProp("handleKeyDown",(function(t){return t(e,keyEvent(13,"Enter"))})))e.input.lastIOSEnter=0;else{if(!v){if(!(o&&h instanceof prosemirrorState.TextSelection&&!h.empty&&h.$head.sameParent(h.$anchor))||e.composing||f.sel&&f.sel.anchor!=f.sel.head){if(f.sel){var g=resolveSelection(e,e.state.doc,f.sel);g&&!g.eq(e.state.selection)&&e.dispatch(e.state.tr.setSelection(g))}return}v={start:h.from,endA:h.to,endB:h.to}}if(chrome&&e.cursorWrapper&&f.sel&&f.sel.anchor==e.cursorWrapper.deco.from&&f.sel.head==f.sel.anchor){var y=v.endB-v.start;f.sel={anchor:f.sel.anchor+y,head:f.sel.anchor+y}}e.input.domChangeCount++,e.state.selection.from<e.state.selection.to&&v.start==v.endB&&e.state.selection instanceof prosemirrorState.TextSelection&&(v.start>e.state.selection.from&&v.start<=e.state.selection.from+2&&e.state.selection.from>=f.from?v.start=e.state.selection.from:v.endA<e.state.selection.to&&v.endA>=e.state.selection.to-2&&e.state.selection.to<=f.to&&(v.endB+=e.state.selection.to-v.endA,v.endA=e.state.selection.to)),ie&&ie_version<=11&&v.endB==v.start+1&&v.endA==v.start&&v.start>f.from&&" \xa0"==f.doc.textBetween(v.start-f.from-1,v.start-f.from+1)&&(v.start--,v.endA--,v.endB--);var S,k=f.doc.resolveNoCache(v.start-f.from),D=f.doc.resolveNoCache(v.endB-f.from),b=p.resolve(v.start),C=k.sameParent(D)&&k.parent.inlineContent&&b.end()>=v.endA;if((ios&&e.input.lastIOSEnter>Date.now()-225&&(!C||r.some((function(e){return"DIV"==e.nodeName||"P"==e.nodeName})))||!C&&k.pos<f.doc.content.size&&(S=prosemirrorState.Selection.findFrom(f.doc.resolve(k.pos+1),1,!0))&&S.head==D.pos)&&e.someProp("handleKeyDown",(function(t){return t(e,keyEvent(13,"Enter"))})))e.input.lastIOSEnter=0;else if(e.state.selection.anchor>v.start&&looksLikeJoin(p,v.start,v.endA,k,D)&&e.someProp("handleKeyDown",(function(t){return t(e,keyEvent(8,"Backspace"))})))android&&chrome&&e.domObserver.suppressSelectionUpdates();else{chrome&&android&&v.endB==v.start&&(e.input.lastAndroidDelete=Date.now()),android&&!C&&k.start()!=D.start()&&0==D.parentOffset&&k.depth==D.depth&&f.sel&&f.sel.anchor==f.sel.head&&f.sel.head==v.endA&&(v.endB-=2,D=f.doc.resolveNoCache(v.endB-f.from),setTimeout((function(){e.someProp("handleKeyDown",(function(t){return t(e,keyEvent(13,"Enter"))}))}),20));var w,O,N,T=v.start,M=v.endA;if(C)if(k.pos==D.pos)ie&&ie_version<=11&&0==k.parentOffset&&(e.domObserver.suppressSelectionUpdates(),setTimeout((function(){return selectionToDOM(e)}),20)),w=e.state.tr.delete(T,M),O=p.resolve(v.start).marksAcross(p.resolve(v.endA));else if(v.endA==v.endB&&(N=isMarkChange(k.parent.content.cut(k.parentOffset,D.parentOffset),b.parent.content.cut(b.parentOffset,v.endA-b.start()))))w=e.state.tr,"add"==N.type?w.addMark(T,M,N.mark):w.removeMark(T,M,N.mark);else if(k.parent.child(k.index()).isText&&k.index()==D.index()-(D.textOffset?0:1)){var x=k.parent.textBetween(k.parentOffset,D.parentOffset);if(e.someProp("handleTextInput",(function(t){return t(e,T,M,x)})))return;w=e.state.tr.insertText(x,T,M)}if(w||(w=e.state.tr.replace(T,M,f.doc.slice(v.start-f.from,v.endB-f.from))),f.sel){var _=resolveSelection(e,w.doc,f.sel);_&&!(chrome&&android&&e.composing&&_.empty&&(v.start!=v.endB||e.input.lastAndroidDelete<Date.now()-100)&&(_.head==T||_.head==w.mapping.map(M)-1)||ie&&_.empty&&_.head==T)&&w.setSelection(_)}O&&w.ensureMarks(O),e.dispatch(w.scrollIntoView())}}}}function resolveSelection(e,t,n){return Math.max(n.anchor,n.head)>t.content.size?null:selectionBetween(e,t.resolve(n.anchor),t.resolve(n.head))}function isMarkChange(e,t){for(var n,o,r,i=e.firstChild.marks,s=t.firstChild.marks,a=i,l=s,c=0;c<s.length;c++)a=s[c].removeFromSet(a);for(var d=0;d<i.length;d++)l=i[d].removeFromSet(l);if(1==a.length&&0==l.length)o=a[0],n="add",r=function(e){return e.mark(o.addToSet(e.marks))};else{if(0!=a.length||1!=l.length)return null;o=l[0],n="remove",r=function(e){return e.mark(o.removeFromSet(e.marks))}}for(var u=[],h=0;h<t.childCount;h++)u.push(r(t.child(h)));if(prosemirrorModel.Fragment.from(u).eq(e))return{mark:o,type:n}}function looksLikeJoin(e,t,n,o,r){if(!o.parent.isTextblock||n-t<=r.pos-o.pos||skipClosingAndOpening(o,!0,!1)<r.pos)return!1;var i=e.resolve(t);if(i.parentOffset<i.parent.content.size||!i.parent.isTextblock)return!1;var s=e.resolve(skipClosingAndOpening(i,!0,!0));return!(!s.parent.isTextblock||s.pos>n||skipClosingAndOpening(s,!0,!1)<n)&&o.parent.content.cut(o.parentOffset).eq(s.parent.content)}function skipClosingAndOpening(e,t,n){for(var o=e.depth,r=t?e.end():e.pos;o>0&&(t||e.indexAfter(o)==e.node(o).childCount);)o--,r++,t=!1;if(n)for(var i=e.node(o).maybeChild(e.indexAfter(o));i&&!i.isLeaf;)i=i.firstChild,r++;return r}function findDiff(e,t,n,o,r){var i=e.findDiffStart(t,n);if(null==i)return null;var s=e.findDiffEnd(t,n+e.size,n+t.size),a=s.a,l=s.b;"end"==r&&(o-=a+Math.max(0,i-Math.min(a,l))-i);if(a<i&&e.size<t.size)l=(i-=o<=i&&o>=a?i-o:0)+(l-a),a=i;else if(l<i){a=(i-=o<=i&&o>=l?i-o:0)+(a-l),l=i}return{start:i,endA:a,endB:l}}var __serializeForClipboard=serializeForClipboard,__parseFromClipboard=parseFromClipboard,__endComposition=endComposition,EditorView=function(){function e(t,n){var o=this;_classCallCheck(this,e),this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new InputState,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=n,this.state=n.state,this.directPlugins=n.plugins||[],this.directPlugins.forEach(checkStateComponent),this.dispatch=this.dispatch.bind(this),this.dom=t&&t.mount||document.createElement("div"),t&&(t.appendChild?t.appendChild(this.dom):"function"==typeof t?t(this.dom):t.mount&&(this.mounted=!0)),this.editable=getEditable(this),updateCursorWrapper(this),this.nodeViews=buildNodeViews(this),this.docView=docViewDesc(this.state.doc,computeDocDeco(this),viewDecorations(this),this.dom,this),this.domObserver=new DOMObserver(this,(function(e,t,n,r){return readDOMChange(o,e,t,n,r)})),this.domObserver.start(),initInput(this),this.updatePluginViews()}return _createClass(e,[{key:"composing",get:function(){return this.input.composing}},{key:"props",get:function(){if(this._props.state!=this.state){var e=this._props;for(var t in this._props={},e)this._props[t]=e[t];this._props.state=this.state}return this._props}},{key:"update",value:function(e){e.handleDOMEvents!=this._props.handleDOMEvents&&ensureListeners(this),this._props=e,e.plugins&&(e.plugins.forEach(checkStateComponent),this.directPlugins=e.plugins),this.updateStateInner(e.state,!0)}},{key:"setProps",value:function(e){var t={};for(var n in this._props)t[n]=this._props[n];for(var o in t.state=this.state,e)t[o]=e[o];this.update(t)}},{key:"updateState",value:function(e){this.updateStateInner(e,this.state.plugins!=e.plugins)}},{key:"updateStateInner",value:function(e,t){var n=this.state,o=!1,r=!1;if(e.storedMarks&&this.composing&&(clearComposition(this),r=!0),this.state=e,t){var i=buildNodeViews(this);changedNodeViews(i,this.nodeViews)&&(this.nodeViews=i,o=!0),ensureListeners(this)}this.editable=getEditable(this),updateCursorWrapper(this);var s=viewDecorations(this),a=computeDocDeco(this),l=t?"reset":e.scrollToSelection>n.scrollToSelection?"to selection":"preserve",c=o||!this.docView.matchesNode(e.doc,a,s);!c&&e.selection.eq(n.selection)||(r=!0);var d="preserve"==l&&r&&null==this.dom.style.overflowAnchor&&storeScrollPos(this);if(r){this.domObserver.stop();var u=c&&(ie||chrome)&&!this.composing&&!n.selection.empty&&!e.selection.empty&&selectionContextChanged(n.selection,e.selection);if(c){var h=chrome?this.trackWrites=this.domSelection().focusNode:null;!o&&this.docView.update(e.doc,a,s,this)||(this.docView.updateOuterDeco([]),this.docView.destroy(),this.docView=docViewDesc(e.doc,a,s,this.dom,this)),h&&!this.trackWrites&&(u=!0)}u||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelection())&&anchorInRightPlace(this))?selectionToDOM(this,u):(syncNodeSelection(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(n),"reset"==l?this.dom.scrollTop=0:"to selection"==l?this.scrollToSelection():d&&resetScrollPos(d)}},{key:"scrollToSelection",value:function(){var e=this,t=this.domSelection().focusNode;if(this.someProp("handleScrollToSelection",(function(t){return t(e)})));else if(this.state.selection instanceof prosemirrorState.NodeSelection){var n=this.docView.domAfterPos(this.state.selection.from);1==n.nodeType&&scrollRectIntoView(this,n.getBoundingClientRect(),t)}else scrollRectIntoView(this,this.coordsAtPos(this.state.selection.head,1),t)}},{key:"destroyPluginViews",value:function(){for(var e;e=this.pluginViews.pop();)e.destroy&&e.destroy()}},{key:"updatePluginViews",value:function(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(var t=0;t<this.pluginViews.length;t++){var n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(var o=0;o<this.directPlugins.length;o++){var r=this.directPlugins[o];r.spec.view&&this.pluginViews.push(r.spec.view(this))}for(var i=0;i<this.state.plugins.length;i++){var s=this.state.plugins[i];s.spec.view&&this.pluginViews.push(s.spec.view(this))}}}},{key:"someProp",value:function(e,t){var n,o=this._props&&this._props[e];if(null!=o&&(n=t?t(o):o))return n;for(var r=0;r<this.directPlugins.length;r++){var i=this.directPlugins[r].props[e];if(null!=i&&(n=t?t(i):i))return n}var s=this.state.plugins;if(s)for(var a=0;a<s.length;a++){var l=s[a].props[e];if(null!=l&&(n=t?t(l):l))return n}}},{key:"hasFocus",value:function(){return this.root.activeElement==this.dom}},{key:"focus",value:function(){this.domObserver.stop(),this.editable&&focusPreventScroll(this.dom),selectionToDOM(this),this.domObserver.start()}},{key:"root",get:function(){var e=this,t=this._root;if(null==t)for(var n=function(t){if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=function(){return t.ownerDocument.getSelection()}),{v:e._root=t}},o=this.dom.parentNode;o;o=o.parentNode){var r=n(o);if("object"===_typeof(r))return r.v}return t||document}},{key:"posAtCoords",value:function(e){return _posAtCoords(this,e)}},{key:"coordsAtPos",value:function(e){return _coordsAtPos(this,e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:1)}},{key:"domAtPos",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this.docView.domFromPos(e,t)}},{key:"nodeDOM",value:function(e){var t=this.docView.descAt(e);return t?t.nodeDOM:null}},{key:"posAtDOM",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,o=this.docView.posFromDOM(e,t,n);if(null==o)throw new RangeError("DOM position not inside the editor");return o}},{key:"endOfTextblock",value:function(e,t){return _endOfTextblock(this,t||this.state,e)}},{key:"destroy",value:function(){this.docView&&(destroyInput(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],viewDecorations(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null)}},{key:"isDestroyed",get:function(){return null==this.docView}},{key:"dispatchEvent",value:function(e){return _dispatchEvent(this,e)}},{key:"dispatch",value:function(e){var t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}},{key:"domSelection",value:function(){return this.root.getSelection()}}]),e}();function computeDocDeco(e){var t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(e.editable),t.translate="no",e.someProp("attributes",(function(n){if("function"==typeof n&&(n=n(e.state)),n)for(var o in n)"class"==o&&(t.class+=" "+n[o]),"style"==o?t.style=(t.style?t.style+";":"")+n[o]:t[o]||"contenteditable"==o||"nodeName"==o||(t[o]=String(n[o]))})),[Decoration.node(0,e.state.doc.content.size,t)]}function updateCursorWrapper(e){if(e.markCursor){var t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),e.cursorWrapper={dom:t,deco:Decoration.widget(e.state.selection.head,t,{raw:!0,marks:e.markCursor})}}else e.cursorWrapper=null}function getEditable(e){return!e.someProp("editable",(function(t){return!1===t(e.state)}))}function selectionContextChanged(e,t){var n=Math.min(e.$anchor.sharedDepth(e.head),t.$anchor.sharedDepth(t.head));return e.$anchor.start(n)!=t.$anchor.start(n)}function buildNodeViews(e){var t=Object.create(null);function n(e){for(var n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return e.someProp("nodeViews",n),e.someProp("markViews",n),t}function changedNodeViews(e,t){var n=0,o=0;for(var r in e){if(e[r]!=t[r])return!0;n++}for(var i in t)o++;return n!=o}function checkStateComponent(e){if(e.spec.state||e.spec.filterTransaction||e.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}exports.Decoration=Decoration,exports.DecorationSet=DecorationSet,exports.EditorView=EditorView,exports.__endComposition=__endComposition,exports.__parseFromClipboard=__parseFromClipboard,exports.__serializeForClipboard=__serializeForClipboard;