"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[5117],{30351:(e,t,n)=>{n.d(t,{s:()=>a});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12l4.58-4.59z"}))}));a.displayName="ChevronLeft"},31197:(e,t,n)=>{n.d(t,{U:()=>a});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"}))}));a.displayName="Clear"},74529:(e,t,n)=>{n.d(t,{n:()=>a});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M19.35 10.04A7.49 7.49 0 0012 4C9.11 4 6.6 5.64 5.35 8.04A5.994 5.994 0 000 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95A5.469 5.469 0 0112 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11A2.98 2.98 0 0122 15c0 1.65-1.35 3-3 3zM8 13h2.55v3h2.9v-3H16l-4-4z"}))}));a.displayName="CloudUpload"},62288:(e,t,n)=>{n.d(t,{j:()=>a});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"}))}));a.displayName="Error"},87847:(e,t,n)=>{n.d(t,{h:()=>a});var r=n(25773),i=n(66845),o=n(69),a=i.forwardRef((function(e,t){return i.createElement(o.D,(0,r.Z)({iconAttrs:{fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),i.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),i.createElement("path",{d:"M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zM6 20V4h7v5h5v11H6z"}))}));a.displayName="InsertDriveFile"},1681:(e,t)=>{t.Z=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(","),r=e.name||"",i=(e.type||"").toLowerCase(),o=i.replace(/\/.*$/,"");return n.some((function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?r.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):i===t}))}return!0}},51622:(e,t,n)=>{n.d(t,{ZP:()=>ie});var r=n(66845),i=n(8984),o=n.n(i),a=n(95099),c=new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]);function l(e,t){var n=function(e){var t=e.name;if(t&&-1!==t.lastIndexOf(".")&&!e.type){var n=t.split(".").pop().toLowerCase(),r=c.get(n);r&&Object.defineProperty(e,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}(e);if("string"!==typeof n.path){var r=e.webkitRelativePath;Object.defineProperty(n,"path",{value:"string"===typeof t?t:"string"===typeof r&&r.length>0?r:e.name,writable:!1,configurable:!1,enumerable:!0})}return n}var u=[".DS_Store","Thumbs.db"];function s(e){return"object"===typeof e&&null!==e}function f(e){return v(e.target.files).map((function(e){return l(e)}))}function p(e){return(0,a.mG)(this,void 0,void 0,(function(){return(0,a.Jh)(this,(function(t){switch(t.label){case 0:return[4,Promise.all(e.map((function(e){return e.getFile()})))];case 1:return[2,t.sent().map((function(e){return l(e)}))]}}))}))}function d(e,t){return(0,a.mG)(this,void 0,void 0,(function(){var n;return(0,a.Jh)(this,(function(r){switch(r.label){case 0:return null===e?[2,[]]:e.items?(n=v(e.items).filter((function(e){return"file"===e.kind})),"drop"!==t?[2,n]:[4,Promise.all(n.map(g))]):[3,2];case 1:return[2,m(y(r.sent()))];case 2:return[2,m(v(e.files).map((function(e){return l(e)})))]}}))}))}function m(e){return e.filter((function(e){return-1===u.indexOf(e.name)}))}function v(e){if(null===e)return[];for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r)}return t}function g(e){if("function"!==typeof e.webkitGetAsEntry)return h(e);var t=e.webkitGetAsEntry();return t&&t.isDirectory?w(t):h(e)}function y(e){return e.reduce((function(e,t){return(0,a.fl)(e,Array.isArray(t)?y(t):[t])}),[])}function h(e){var t=e.getAsFile();if(!t)return Promise.reject(e+" is not a File");var n=l(t);return Promise.resolve(n)}function b(e){return(0,a.mG)(this,void 0,void 0,(function(){return(0,a.Jh)(this,(function(t){return[2,e.isDirectory?w(e):D(e)]}))}))}function w(e){var t=e.createReader();return new Promise((function(e,n){var r=[];!function i(){var o=this;t.readEntries((function(t){return(0,a.mG)(o,void 0,void 0,(function(){var o,c,l;return(0,a.Jh)(this,(function(a){switch(a.label){case 0:if(t.length)return[3,5];a.label=1;case 1:return a.trys.push([1,3,,4]),[4,Promise.all(r)];case 2:return o=a.sent(),e(o),[3,4];case 3:return c=a.sent(),n(c),[3,4];case 4:return[3,6];case 5:l=Promise.all(t.map(b)),r.push(l),i(),a.label=6;case 6:return[2]}}))}))}),(function(e){n(e)}))}()}))}function D(e){return(0,a.mG)(this,void 0,void 0,(function(){return(0,a.Jh)(this,(function(t){return[2,new Promise((function(t,n){e.file((function(n){var r=l(n,e.fullPath);t(r)}),(function(e){n(e)}))}))]}))}))}var x=n(1681);function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){j(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function j(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,i,o=[],a=!0,c=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(l){c=!0,i=l}finally{try{a||null==n.return||n.return()}finally{if(c)throw i}}return o}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return F(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return F(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var C="file-invalid-type",k="file-too-large",z="file-too-small",P="too-many-files",S=function(e){e=Array.isArray(e)&&1===e.length?e[0]:e;var t=Array.isArray(e)?"one of ".concat(e.join(", ")):e;return{code:C,message:"File type must be ".concat(t)}},R=function(e){return{code:k,message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},M=function(e){return{code:z,message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},L={code:P,message:"Too many files"};function T(e,t){var n="application/x-moz-file"===e.type||(0,x.Z)(e,t);return[n,n?null:S(t)]}function I(e,t,n){if(V(e.size))if(V(t)&&V(n)){if(e.size>n)return[!1,R(n)];if(e.size<t)return[!1,M(t)]}else{if(V(t)&&e.size<t)return[!1,M(t)];if(V(n)&&e.size>n)return[!1,R(n)]}return[!0,null]}function V(e){return void 0!==e&&null!==e}function B(e){return"function"===typeof e.isPropagationStopped?e.isPropagationStopped():"undefined"!==typeof e.cancelBubble&&e.cancelBubble}function H(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,(function(e){return"Files"===e||"application/x-moz-file"===e})):!!e.target&&!!e.target.files}function K(e){e.preventDefault()}function G(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.some((function(t){return!B(e)&&t&&t.apply(void 0,[e].concat(r)),B(e)}))}}function Z(e){return e="string"===typeof e?e.split(","):e,[{description:"everything",accept:Array.isArray(e)?e.filter((function(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)})).reduce((function(e,t){return A(A({},e),{},j({},t,[]))}),{}):{}}]}var N=["children"],_=["open"],J=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],U=["refKey","onChange","onClick"];function $(e){return function(e){if(Array.isArray(e))return Y(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||W(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,i,o=[],a=!0,c=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(l){c=!0,i=l}finally{try{a||null==n.return||n.return()}finally{if(c)throw i}}return o}(e,t)||W(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(e,t){if(e){if("string"===typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Y(e,t):void 0}}function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function X(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(n),!0).forEach((function(t){ee(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function te(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var ne=(0,r.forwardRef)((function(e,t){var n=e.children,i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=X(X({},re),e),n=t.accept,i=t.disabled,o=t.getFilesFromEvent,a=t.maxSize,c=t.minSize,l=t.multiple,u=t.maxFiles,s=t.onDragEnter,f=t.onDragLeave,p=t.onDragOver,d=t.onDrop,m=t.onDropAccepted,v=t.onDropRejected,g=t.onFileDialogCancel,y=t.onFileDialogOpen,h=t.useFsAccessApi,b=t.preventDropOnDocument,w=t.noClick,D=t.noKeyboard,x=t.noDrag,O=t.noDragEventsBubbling,A=t.validator,j=(0,r.useMemo)((function(){return"function"===typeof y?y:ce}),[y]),F=(0,r.useMemo)((function(){return"function"===typeof g?g:ce}),[g]),C=(0,r.useRef)(null),k=(0,r.useRef)(null),z=q((0,r.useReducer)(ae,oe),2),P=z[0],S=z[1],R=P.isFocused,M=P.isFileDialogActive,V=P.draggedFiles,N=(0,r.useRef)("undefined"!==typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),_=function(){!N.current&&M&&setTimeout((function(){k.current&&(k.current.files.length||(S({type:"closeDialog"}),F()))}),300)};(0,r.useEffect)((function(){return window.addEventListener("focus",_,!1),function(){window.removeEventListener("focus",_,!1)}}),[k,M,F,N]);var W=(0,r.useRef)([]),Y=function(e){C.current&&C.current.contains(e.target)||(e.preventDefault(),W.current=[])};(0,r.useEffect)((function(){return b&&(document.addEventListener("dragover",K,!1),document.addEventListener("drop",Y,!1)),function(){b&&(document.removeEventListener("dragover",K),document.removeEventListener("drop",Y))}}),[C,b]);var Q=(0,r.useCallback)((function(e){e.preventDefault(),e.persist(),he(e),W.current=[].concat($(W.current),[e.target]),H(e)&&Promise.resolve(o(e)).then((function(t){B(e)&&!O||(S({draggedFiles:t,isDragActive:!0,type:"setDraggedFiles"}),s&&s(e))}))}),[o,s,O]),ne=(0,r.useCallback)((function(e){e.preventDefault(),e.persist(),he(e);var t=H(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(n){}return t&&p&&p(e),!1}),[p,O]),ie=(0,r.useCallback)((function(e){e.preventDefault(),e.persist(),he(e);var t=W.current.filter((function(e){return C.current&&C.current.contains(e)})),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),W.current=t,t.length>0||(S({isDragActive:!1,type:"setDraggedFiles",draggedFiles:[]}),H(e)&&f&&f(e))}),[C,f,O]),le=(0,r.useCallback)((function(e,t){var r=[],i=[];e.forEach((function(e){var t=q(T(e,n),2),o=t[0],l=t[1],u=q(I(e,c,a),2),s=u[0],f=u[1],p=A?A(e):null;if(o&&s&&!p)r.push(e);else{var d=[l,f];p&&(d=d.concat(p)),i.push({file:e,errors:d.filter((function(e){return e}))})}})),(!l&&r.length>1||l&&u>=1&&r.length>u)&&(r.forEach((function(e){i.push({file:e,errors:[L]})})),r.splice(0)),S({acceptedFiles:r,fileRejections:i,type:"setFiles"}),d&&d(r,i,t),i.length>0&&v&&v(i,t),r.length>0&&m&&m(r,t)}),[S,l,n,c,a,u,d,m,v,A]),ue=(0,r.useCallback)((function(e){e.preventDefault(),e.persist(),he(e),W.current=[],H(e)&&Promise.resolve(o(e)).then((function(t){B(e)&&!O||le(t,e)})),S({type:"reset"})}),[o,le,O]),se=(0,r.useCallback)((function(){if(N.current){S({type:"openDialog"}),j();var e={multiple:l,types:Z(n)};window.showOpenFilePicker(e).then((function(e){return o(e)})).then((function(e){le(e,null),S({type:"closeDialog"})})).catch((function(e){var t;(t=e)instanceof DOMException&&("AbortError"===t.name||t.code===t.ABORT_ERR)?(F(e),S({type:"closeDialog"})):function(e){return e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)}(e)&&(N.current=!1,k.current&&(k.current.value=null,k.current.click()))}))}else k.current&&(S({type:"openDialog"}),j(),k.current.value=null,k.current.click())}),[S,j,F,h,le,n,l]),fe=(0,r.useCallback)((function(e){C.current&&C.current.isEqualNode(e.target)&&(" "!==e.key&&"Enter"!==e.key&&32!==e.keyCode&&13!==e.keyCode||(e.preventDefault(),se()))}),[C,se]),pe=(0,r.useCallback)((function(){S({type:"focus"})}),[]),de=(0,r.useCallback)((function(){S({type:"blur"})}),[]),me=(0,r.useCallback)((function(){w||(!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return function(e){return-1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")}(e)||function(e){return-1!==e.indexOf("Edge/")}(e)}()?se():setTimeout(se,0))}),[w,se]),ve=function(e){return i?null:e},ge=function(e){return D?null:ve(e)},ye=function(e){return x?null:ve(e)},he=function(e){O&&e.stopPropagation()},be=(0,r.useMemo)((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=void 0===t?"ref":t,r=e.role,o=e.onKeyDown,a=e.onFocus,c=e.onBlur,l=e.onClick,u=e.onDragEnter,s=e.onDragOver,f=e.onDragLeave,p=e.onDrop,d=te(e,J);return X(X(ee({onKeyDown:ge(G(o,fe)),onFocus:ge(G(a,pe)),onBlur:ge(G(c,de)),onClick:ve(G(l,me)),onDragEnter:ye(G(u,Q)),onDragOver:ye(G(s,ne)),onDragLeave:ye(G(f,ie)),onDrop:ye(G(p,ue)),role:"string"===typeof r&&""!==r?r:"button"},n,C),i||D?{}:{tabIndex:0}),d)}}),[C,fe,pe,de,me,Q,ne,ie,ue,D,x,i]),we=(0,r.useCallback)((function(e){e.stopPropagation()}),[]),De=(0,r.useMemo)((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,r=void 0===t?"ref":t,i=e.onChange,o=e.onClick,a=te(e,U);return X(X({},ee({accept:n,multiple:l,type:"file",style:{display:"none"},onChange:ve(G(i,ue)),onClick:ve(G(o,we)),tabIndex:-1},r,k)),a)}}),[k,n,l,ue,i]),xe=V.length,Oe=xe>0&&function(e){var t=e.files,n=e.accept,r=e.minSize,i=e.maxSize,o=e.multiple,a=e.maxFiles;return!(!o&&t.length>1||o&&a>=1&&t.length>a)&&t.every((function(e){var t=E(T(e,n),1)[0],o=E(I(e,r,i),1)[0];return t&&o}))}({files:V,accept:n,minSize:c,maxSize:a,multiple:l,maxFiles:u}),Ae=xe>0&&!Oe;return X(X({},P),{},{isDragAccept:Oe,isDragReject:Ae,isFocused:R&&!i,getRootProps:be,getInputProps:De,rootRef:C,inputRef:k,open:ve(se)})}(te(e,N)),o=i.open,a=te(i,_);return(0,r.useImperativeHandle)(t,(function(){return{open:o}}),[o]),r.createElement(r.Fragment,null,n(X(X({},a),{},{open:o})))}));ne.displayName="Dropzone";var re={disabled:!1,getFilesFromEvent:function(e){return(0,a.mG)(this,void 0,void 0,(function(){return(0,a.Jh)(this,(function(t){return s(e)&&s(e.dataTransfer)?[2,d(e.dataTransfer,e.type)]:function(e){return s(e)&&s(e.target)}(e)?[2,f(e)]:Array.isArray(e)&&e.every((function(e){return"getFile"in e&&"function"===typeof e.getFile}))?[2,p(e)]:[2,[]]}))}))},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!0};ne.defaultProps=re,ne.propTypes={children:o().func,accept:o().oneOfType([o().string,o().arrayOf(o().string)]),multiple:o().bool,preventDropOnDocument:o().bool,noClick:o().bool,noKeyboard:o().bool,noDrag:o().bool,noDragEventsBubbling:o().bool,minSize:o().number,maxSize:o().number,maxFiles:o().number,disabled:o().bool,getFilesFromEvent:o().func,onFileDialogCancel:o().func,onFileDialogOpen:o().func,useFsAccessApi:o().bool,onDragEnter:o().func,onDragLeave:o().func,onDragOver:o().func,onDrop:o().func,onDropAccepted:o().func,onDropRejected:o().func,validator:o().func};const ie=ne;var oe={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,draggedFiles:[],acceptedFiles:[],fileRejections:[]};function ae(e,t){switch(t.type){case"focus":return X(X({},e),{},{isFocused:!0});case"blur":return X(X({},e),{},{isFocused:!1});case"openDialog":return X(X({},oe),{},{isFileDialogActive:!0});case"closeDialog":return X(X({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":var n=t.isDragActive,r=t.draggedFiles;return X(X({},e),{},{draggedFiles:r,isDragActive:n});case"setFiles":return X(X({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections});case"reset":return X({},oe);default:return e}}function ce(){}}}]);