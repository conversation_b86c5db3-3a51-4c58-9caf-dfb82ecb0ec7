"use strict";(self.webpackChunk_streamlit_app=self.webpackChunk_streamlit_app||[]).push([[7602],{90186:(e,t,s)=>{s.d(t,{$:()=>c,Z:()=>h});var i=s(66845),r=s(25621),o=s(66694),a=s(27466),n=s(38570),l=s(80318),d=s(40864);let c;!function(e){e.EXTRASMALL="xs",e.SMALL="sm",e.MEDIUM="md",e.LARGE="lg",e.EXTRALARGE="xl"}(c||(c={}));const h=function(e){let{value:t,width:s,size:h=c.SMALL,overrides:u}=e;const g=(0,r.u)(),p={xs:g.spacing.twoXS,sm:g.spacing.sm,md:g.spacing.lg,lg:g.spacing.xl,xl:g.spacing.twoXL},{activeTheme:m}=i.useContext(o.E),f=!(0,a.MJ)(m),S={BarContainer:{style:{marginTop:g.spacing.none,marginBottom:g.spacing.none,marginRight:g.spacing.none,marginLeft:g.spacing.none}},Bar:{style:e=>{let{$theme:t}=e;return{width:s?s.toString():void 0,marginTop:g.spacing.none,marginBottom:g.spacing.none,marginRight:g.spacing.none,marginLeft:g.spacing.none,height:p[h],backgroundColor:t.colors.progressbarTrackFill,borderTopLeftRadius:g.spacing.twoXS,borderTopRightRadius:g.spacing.twoXS,borderBottomLeftRadius:g.spacing.twoXS,borderBottomRightRadius:g.spacing.twoXS}}},BarProgress:{style:()=>({backgroundColor:f?g.colors.primary:g.colors.blue70,borderTopLeftRadius:g.spacing.twoXS,borderTopRightRadius:g.spacing.twoXS,borderBottomLeftRadius:g.spacing.twoXS,borderBottomRightRadius:g.spacing.twoXS})}};return(0,d.jsx)(n.Z,{value:t,overrides:(0,l.aO)(S,u)})}},47602:(e,t,s)=>{s.r(t),s.d(t,{default:()=>q});var i,r=s(47869),o=s(23183),a=s(62813),n=s.n(a),l=s(66845),d=s(16295),c=s(46927),h=s(68411),u=s(8879),g=s(98478),p=s(86659),m=s(87814),f=s(23849),S=s(50641),C=s(77367),R=s(90186),x=s(1515),w=s(35704);function b(e,t){switch(e){case i.XSMALL:return{padding:"".concat(t.spacing.twoXS," ").concat(t.spacing.sm),fontSize:t.fontSizes.sm};case i.SMALL:return{padding:"".concat(t.spacing.twoXS," ").concat(t.spacing.md)};case i.LARGE:return{padding:"".concat(t.spacing.md," ").concat(t.spacing.md)};default:return{padding:"".concat(t.spacing.xs," ").concat(t.spacing.md)}}}!function(e){e.XSMALL="xsmall",e.SMALL="small",e.MEDIUM="medium",e.LARGE="large"}(i||(i={}));const F=(0,x.Z)("div",{target:"etz5kuj9"})((()=>({position:"relative",overflow:"hidden",width:"100%",objectFit:"contain"})),""),v=(0,x.Z)("div",{target:"etz5kuj8"})((e=>{let{theme:t,width:s}=e;return{backgroundColor:t.colors.secondaryBg,borderRadius:"".concat(t.radii.lg," ").concat(t.radii.lg," 0 0"),width:"100%",height:9*s/16,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"}}),""),I=(0,x.Z)("p",{target:"etz5kuj7"})((e=>{let{theme:t}=e;return{marginTop:t.spacing.sm,textAlign:"center"}}),""),M=(0,x.Z)("img",{target:"etz5kuj6"})((e=>{let{theme:t,opacity:s}=e;return{borderRadius:"".concat(t.radii.lg," ").concat(t.radii.lg," 0 0"),objectFit:"contain",opacity:s}}),""),y=(0,x.Z)("a",{target:"etz5kuj5"})((e=>{let{theme:t}=e;return{color:t.colors.primary,display:"block",textDecoration:"none"}}),""),E=(0,x.Z)("span",{target:"etz5kuj4"})((()=>({display:"flex",alignItems:"center"})),""),T=(0,x.Z)("div",{target:"etz5kuj3"})((e=>{let{theme:t}=e;return{position:"absolute",top:t.spacing.lg,right:t.spacing.lg,zIndex:1,color:t.colors.fadedText40,mixBlendMode:"difference",opacity:.6}}),""),j=(0,x.Z)("div",{target:"etz5kuj1"})((()=>({height:"fit-content",width:"100%",position:"absolute",bottom:0})),""),L=(0,x.Z)("button",{target:"etz5kuj0"})((e=>{let{theme:t}=e;return{position:"relative",display:"inline-flex",flexDirection:"column",alignItems:"center",justifyContent:"center",backgroundColor:t.colors.lightenedBg05,border:"1px solid ".concat(t.colors.fadedText10),borderRadius:"0 0 ".concat(t.radii.lg," ").concat(t.radii.lg),"&:hover":{borderColor:t.colors.primary,color:t.colors.primary},"&:active":{color:t.colors.white,borderColor:t.colors.primary,backgroundColor:t.colors.primary},"&:focus:not(:active)":{borderColor:t.colors.primary,color:t.colors.primary},"&:disabled, &:disabled:hover, &:disabled:active":{color:t.colors.fadedText40},fontWeight:t.fontWeights.normal,padding:"".concat(t.spacing.xs," ").concat(t.spacing.md),margin:0,lineHeight:t.lineHeights.base,color:"inherit",width:"100%",userSelect:"none","&:focus":{outline:"none"},"&:focus-visible":{boxShadow:"0 0 0 0.2rem ".concat((0,w.DZ)(t.colors.primary,.5))},...b(i.MEDIUM,t)}}),"");var U=s(40864);const P=function(e){let{disabled:t,onClick:s,children:i,progress:r}=e;return(0,U.jsxs)(L,{disabled:t||!1,onClick:s||(()=>{}),progress:r||null,"data-testid":"stCameraInputButton",children:[i,r&&(0,U.jsx)(j,{children:(0,U.jsx)(R.Z,{value:r,size:R.$.EXTRASMALL,overrides:{Bar:{style:{borderTopLeftRadius:"0px",borderTopRightRadius:"0px"}},BarProgress:{style:{borderTopLeftRadius:"0px",borderTopRightRadius:"0px"}},BarContainer:{style:{borderTopLeftRadius:"0px",borderTopRightRadius:"0px"}}}})})]})};var k=s(77979),z=s(9003),Z=s(81354),D=s(11765);let O;!function(e){e.USER="user",e.ENVIRONMENT="environment"}(O||(O={}));const X=e=>{let{switchFacingMode:t}=e;return(0,U.jsx)(T,{"data-testid":"stCameraSwitchButton",children:(0,U.jsx)(h.Z,{content:"Switch camera",placement:h.u.TOP_RIGHT,children:(0,U.jsx)(z.ZP,{kind:Z.nW.MINIMAL,onClick:t,children:(0,U.jsx)(c.Z,{content:k.Z,size:"twoXL",color:D.Z.white})})})})};var B=s(3084),_=s(25621),W=s(91706),A=s(48051),V=s.n(A),G=s(84192);let N;!function(e){e.PENDING="pending",e.SUCCESS="success",e.ERROR="error"}(N||(N={}));const H=e=>{let{width:t}=e;return(0,U.jsxs)(v,{width:t,children:[(0,U.jsx)(c.Z,{size:"threeXL",color:D.Z.gray60,content:B.n}),(0,U.jsxs)(I,{children:["This app would like to use your camera.",(0,U.jsx)(y,{href:G.U3,rel:"noopener noreferrer",target:"_blank",children:"Learn how to allow access."})]})]})},$=e=>{let{handleCapture:t,width:s,disabled:i,clearPhotoInProgress:r,setClearPhotoInProgress:o,facingMode:a,setFacingMode:n,testOverride:d}=e;const[c,h]=(0,l.useState)(d||N.PENDING),u=(0,l.useRef)(null),[g,p]=(0,l.useState)(s),m=(0,l.useCallback)((0,S.Ds)(1e3,p),[]);(0,l.useEffect)((()=>{m(s)}),[s,m]);const f=(0,_.u)();return(0,U.jsxs)(F,{width:g,"data-testid":"stWebcamComponent",children:[c===N.SUCCESS||i||r?W.tq&&(0,U.jsx)(X,{switchFacingMode:n}):(0,U.jsx)(H,{width:g}),(0,U.jsx)(v,{"data-testid":"stWebcamStyledBox",hidden:c!==N.SUCCESS&&!i&&!r,width:g,children:!i&&(0,U.jsx)(V(),{audio:!1,ref:u,screenshotFormat:"image/jpeg",screenshotQuality:1,width:g,height:9*g/16,style:{borderRadius:"".concat(f.radii.lg," ").concat(f.radii.lg," 0 0")},onUserMediaError:()=>{h(N.ERROR)},onUserMedia:()=>{h(N.SUCCESS),o(!1)},videoConstraints:{width:{ideal:g},facingMode:a}})}),(0,U.jsx)(P,{onClick:function(){if(null!==u.current){const e=u.current.getScreenshot();t(e)}},disabled:c!==N.SUCCESS||i||r,children:"Take Photo"})]})};class K extends l.PureComponent{constructor(e){super(e),this.localFileIdCounter=1,this.RESTORED_FROM_WIDGET_STRING="RESTORED_FROM_WIDGET",this.formClearHelper=new m.K,this.getProgress=()=>{if(this.state.files.length>0&&"uploading"===this.state.files[this.state.files.length-1].status.type){return this.state.files[this.state.files.length-1].status.progress}},this.setClearPhotoInProgress=e=>{this.setState({clearPhotoInProgress:e})},this.setFacingMode=()=>{this.setState((e=>({facingMode:e.facingMode===O.USER?O.ENVIRONMENT:O.USER})))},this.handleCapture=e=>{if(null===e)return Promise.resolve();this.setState({imgSrc:e,shutter:!0,minShutterEffectPassed:!1});return(t=e,s="camera-input-".concat((new Date).toISOString().replace(/:/g,"_"),".jpg"),fetch(t).then((e=>e.arrayBuffer())).then((e=>new File([e],s,{type:"image/jpeg"})))).then((e=>this.props.uploadClient.fetchFileURLs([e]).then((t=>({file:e,fileUrls:t[0]}))))).then((e=>{let{file:t,fileUrls:s}=e;return this.uploadFile(s,t)})).then((()=>{return e=150,new Promise((t=>setTimeout(t,e)));var e})).then((()=>{this.setState(((t,s)=>({imgSrc:e,shutter:t.shutter,minShutterEffectPassed:!0})))})).catch((e=>{(0,f.H)(e)}));var t,s},this.removeCapture=()=>{0!==this.state.files.length&&(this.state.files.forEach((e=>this.deleteFile(e.id))),this.setState({imgSrc:null,clearPhotoInProgress:!0}))},this.componentDidUpdate=()=>{if("ready"!==this.status)return;const e=this.createWidgetValue(),{element:t,widgetMgr:s}=this.props,i=s.getFileUploaderStateValue(t);n()(e,i)||s.setFileUploaderStateValue(t,e,{fromUi:!0})},this.onFormCleared=()=>{this.setState({files:[]},(()=>{const e=this.createWidgetValue();null!=e&&(this.setState({imgSrc:null}),this.props.widgetMgr.setFileUploaderStateValue(this.props.element,e,{fromUi:!0}))}))},this.deleteFile=e=>{const t=this.getFile(e);null!=t&&("uploading"===t.status.type&&t.status.cancelToken.cancel(),"uploaded"===t.status.type&&t.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(t.status.fileUrls.deleteUrl),this.removeFile(e))},this.addFile=e=>{this.setState((t=>({files:[...t.files,e]})))},this.removeFile=e=>{this.setState((t=>({files:t.files.filter((t=>t.id!==e))})))},this.getFile=e=>this.state.files.find((t=>t.id===e)),this.updateFile=(e,t)=>{this.setState((s=>({files:s.files.map((s=>s.id===e?t:s))})))},this.onUploadComplete=(e,t)=>{this.setState((()=>({shutter:!1})));const s=this.getFile(e);null!=s&&"uploading"===s.status.type&&this.updateFile(s.id,s.setStatus({type:"uploaded",fileId:t.fileId,fileUrls:t}))},this.onUploadProgress=(e,t)=>{const s=this.getFile(t);if(null==s||"uploading"!==s.status.type)return;const i=Math.round(100*e.loaded/e.total);s.status.progress!==i&&this.updateFile(t,s.setStatus({type:"uploading",cancelToken:s.status.cancelToken,progress:i}))},this.reset=()=>{this.setState({files:[],imgSrc:null})},this.uploadFile=(e,t)=>{const s=o.Z.CancelToken.source(),i=new C.R(t.name,t.size,this.nextLocalFileId(),{type:"uploading",cancelToken:s,progress:1});this.addFile(i),this.props.uploadClient.uploadFile(this.props.element,e.uploadUrl,t,(e=>this.onUploadProgress(e,i.id)),s.token).then((()=>this.onUploadComplete(i.id,e))).catch((e=>{o.Z.isCancel(e)||this.updateFile(i.id,i.setStatus({type:"error",errorMessage:e?e.toString():"Unknown error"}))}))},this.state=this.initialValue}get initialValue(){const e={files:[],imgSrc:null,shutter:!1,minShutterEffectPassed:!0,clearPhotoInProgress:!1,facingMode:O.USER},{widgetMgr:t,element:s}=this.props,i=t.getFileUploaderStateValue(s);if(null==i)return e;const{uploadedFileInfo:r}=i;return null==r||0===r.length?e:{files:r.map((e=>{const t=e.name,s=e.size,i=e.fileId,r=e.fileUrls;return new C.R(t,s,this.nextLocalFileId(),{type:"uploaded",fileId:i,fileUrls:r})})),imgSrc:0===r.length?"":this.RESTORED_FROM_WIDGET_STRING,shutter:!1,minShutterEffectPassed:!1,clearPhotoInProgress:!1,facingMode:O.USER}}componentWillUnmount(){this.formClearHelper.disconnect()}get status(){return this.state.files.some((e=>"uploading"===e.status.type))?"updating":"ready"}componentDidMount(){const e=this.createWidgetValue(),{element:t,widgetMgr:s}=this.props;void 0===s.getFileUploaderStateValue(t)&&s.setFileUploaderStateValue(t,e,{fromUi:!1})}createWidgetValue(){const e=this.state.files.filter((e=>"uploaded"===e.status.type)).map((e=>{const{name:t,size:s,status:i}=e;return new d.jM({fileId:i.fileId,fileUrls:i.fileUrls,name:t,size:s})}));return new d.xO({uploadedFileInfo:e})}render(){var e;const{element:t,widgetMgr:s,disabled:i,width:o}=this.props;return this.formClearHelper.manageFormClearListener(s,t.formId,this.onFormCleared),(0,U.jsxs)(F,{width:o,className:"row-widget","data-testid":"stCameraInput",children:[(0,U.jsx)(g.O,{label:t.label,disabled:i,labelVisibility:(0,S.iF)(null===(e=t.labelVisibility)||void 0===e?void 0:e.value),children:t.help&&(0,U.jsx)(p.dT,{children:(0,U.jsx)(u.Z,{content:t.help,placement:h.u.TOP_RIGHT})})}),this.state.imgSrc?(0,U.jsxs)(U.Fragment,{children:[(0,U.jsx)(v,{width:o,children:this.state.imgSrc!==this.RESTORED_FROM_WIDGET_STRING&&(0,U.jsx)(M,{src:this.state.imgSrc,alt:"Snapshot",opacity:this.state.shutter||!this.state.minShutterEffectPassed?"50%":"100%",width:o,height:9*o/16})}),(0,U.jsx)(P,{onClick:this.removeCapture,progress:this.getProgress(),disabled:!!this.getProgress()||i,children:this.getProgress()?"Uploading...":(0,U.jsxs)(E,{children:[(0,U.jsx)(c.Z,{content:r.X,margin:"0 xs 0 0",size:"sm"})," Clear photo"]})})]}):(0,U.jsx)($,{handleCapture:this.handleCapture,width:o,disabled:i,clearPhotoInProgress:this.state.clearPhotoInProgress,setClearPhotoInProgress:this.setClearPhotoInProgress,facingMode:this.state.facingMode,setFacingMode:this.setFacingMode,testOverride:this.props.testOverride})]})}nextLocalFileId(){return this.localFileIdCounter++}}const q=K},77367:(e,t,s)=>{s.d(t,{R:()=>i});class i{setStatus(e){return new i(this.name,this.size,this.id,e)}constructor(e,t,s,i){this.name=void 0,this.size=void 0,this.status=void 0,this.id=void 0,this.name=e,this.size=t,this.id=s,this.status=i}}},87814:(e,t,s)=>{s.d(t,{K:()=>r});var i=s(50641);class r{constructor(){this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}manageFormClearListener(e,t,s){null!=this.formClearListener&&this.lastWidgetMgr===e&&this.lastFormId===t||(this.disconnect(),(0,i.bM)(t)&&(this.formClearListener=e.addFormClearedListener(t,s),this.lastWidgetMgr=e,this.lastFormId=t))}disconnect(){var e;null===(e=this.formClearListener)||void 0===e||e.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}}}]);