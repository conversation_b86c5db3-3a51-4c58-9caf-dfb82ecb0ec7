"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.AppPage_pb2
import sys

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class PagesChanged(google.protobuf.message.Message):
    """Message used to tell the client that the app's pages have changed."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_PAGES_FIELD_NUMBER: builtins.int
    @property
    def app_pages(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[streamlit.proto.AppPage_pb2.AppPage]: ...
    def __init__(
        self,
        *,
        app_pages: collections.abc.Iterable[streamlit.proto.AppPage_pb2.AppPage] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_pages", b"app_pages"]) -> None: ...

global___PagesChanged = PagesChanged
