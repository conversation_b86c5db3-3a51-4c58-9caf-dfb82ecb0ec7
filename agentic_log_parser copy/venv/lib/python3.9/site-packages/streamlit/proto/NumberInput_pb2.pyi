"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import streamlit.proto.LabelVisibilityMessage_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class NumberInput(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _DataType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _DataTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[NumberInput._DataType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        INT: NumberInput._DataType.ValueType  # 0
        """Does the input operate on ints or floats? This doesn't change how the
        data is stored, but the frontend needs to know for input parsing.
        """
        FLOAT: NumberInput._DataType.ValueType  # 1

    class DataType(_DataType, metaclass=_DataTypeEnumTypeWrapper): ...
    INT: NumberInput.DataType.ValueType  # 0
    """Does the input operate on ints or floats? This doesn't change how the
    data is stored, but the frontend needs to know for input parsing.
    """
    FLOAT: NumberInput.DataType.ValueType  # 1

    ID_FIELD_NUMBER: builtins.int
    LABEL_FIELD_NUMBER: builtins.int
    FORM_ID_FIELD_NUMBER: builtins.int
    FORMAT_FIELD_NUMBER: builtins.int
    HAS_MIN_FIELD_NUMBER: builtins.int
    HAS_MAX_FIELD_NUMBER: builtins.int
    DATA_TYPE_FIELD_NUMBER: builtins.int
    DEFAULT_FIELD_NUMBER: builtins.int
    STEP_FIELD_NUMBER: builtins.int
    MIN_FIELD_NUMBER: builtins.int
    MAX_FIELD_NUMBER: builtins.int
    HELP_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    SET_VALUE_FIELD_NUMBER: builtins.int
    DISABLED_FIELD_NUMBER: builtins.int
    LABEL_VISIBILITY_FIELD_NUMBER: builtins.int
    PLACEHOLDER_FIELD_NUMBER: builtins.int
    id: builtins.str
    label: builtins.str
    form_id: builtins.str
    format: builtins.str
    has_min: builtins.bool
    has_max: builtins.bool
    data_type: global___NumberInput.DataType.ValueType
    default: builtins.float
    step: builtins.float
    min: builtins.float
    max: builtins.float
    help: builtins.str
    value: builtins.float
    set_value: builtins.bool
    disabled: builtins.bool
    @property
    def label_visibility(self) -> streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage: ...
    placeholder: builtins.str
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        label: builtins.str = ...,
        form_id: builtins.str = ...,
        format: builtins.str = ...,
        has_min: builtins.bool = ...,
        has_max: builtins.bool = ...,
        data_type: global___NumberInput.DataType.ValueType = ...,
        default: builtins.float | None = ...,
        step: builtins.float = ...,
        min: builtins.float = ...,
        max: builtins.float = ...,
        help: builtins.str = ...,
        value: builtins.float | None = ...,
        set_value: builtins.bool = ...,
        disabled: builtins.bool = ...,
        label_visibility: streamlit.proto.LabelVisibilityMessage_pb2.LabelVisibilityMessage | None = ...,
        placeholder: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_default", b"_default", "_value", b"_value", "default", b"default", "label_visibility", b"label_visibility", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_default", b"_default", "_value", b"_value", "data_type", b"data_type", "default", b"default", "disabled", b"disabled", "form_id", b"form_id", "format", b"format", "has_max", b"has_max", "has_min", b"has_min", "help", b"help", "id", b"id", "label", b"label", "label_visibility", b"label_visibility", "max", b"max", "min", b"min", "placeholder", b"placeholder", "set_value", b"set_value", "step", b"step", "value", b"value"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_default", b"_default"]) -> typing_extensions.Literal["default"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_value", b"_value"]) -> typing_extensions.Literal["value"] | None: ...

global___NumberInput = NumberInput
