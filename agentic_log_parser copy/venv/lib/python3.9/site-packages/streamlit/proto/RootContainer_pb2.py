# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/RootContainer.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#streamlit/proto/RootContainer.proto*1\n\rRootContainer\x12\x08\n\x04MAIN\x10\x00\x12\x0b\n\x07SIDEBAR\x10\x01\x12\t\n\x05\x45VENT\x10\x02\x42\x32\n\x1c\x63om.snowflake.apps.streamlitB\x12RootContainerProtob\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.RootContainer_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.snowflake.apps.streamlitB\022RootContainerProto'
  _ROOTCONTAINER._serialized_start=39
  _ROOTCONTAINER._serialized_end=88
# @@protoc_insertion_point(module_scope)
