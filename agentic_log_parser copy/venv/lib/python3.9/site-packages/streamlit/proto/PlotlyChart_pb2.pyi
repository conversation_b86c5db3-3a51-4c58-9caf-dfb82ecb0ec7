"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.message
import sys

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class PlotlyChart(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    FIGURE_FIELD_NUMBER: builtins.int
    USE_CONTAINER_WIDTH_FIELD_NUMBER: builtins.int
    THEME_FIELD_NUMBER: builtins.int
    url: builtins.str
    """If the user chose to send the plot to Plotly's server, then this is the
    URL that can be used to embed to the plot.
    """
    @property
    def figure(self) -> global___Figure:
        """If the user chose to not send the plot to Plotly's server, then we pass
        here the plot's dict spec as JSON.
        """
    use_container_width: builtins.bool
    """If True, will overwrite the chart width spec to fit to container."""
    theme: builtins.str
    """override the properties with a theme. Currently, only "streamlit" or None are accepted."""
    def __init__(
        self,
        *,
        url: builtins.str = ...,
        figure: global___Figure | None = ...,
        use_container_width: builtins.bool = ...,
        theme: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["chart", b"chart", "figure", b"figure", "url", b"url"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["chart", b"chart", "figure", b"figure", "theme", b"theme", "url", b"url", "use_container_width", b"use_container_width"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["chart", b"chart"]) -> typing_extensions.Literal["url", "figure"] | None: ...

global___PlotlyChart = PlotlyChart

class Figure(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    CONFIG_FIELD_NUMBER: builtins.int
    spec: builtins.str
    """JSON-serialized dict containing keys from the set {data, frames, layout}."""
    config: builtins.str
    """JSON-serialized dict with Plotly's config object."""
    def __init__(
        self,
        *,
        spec: builtins.str = ...,
        config: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["config", b"config", "spec", b"spec"]) -> None: ...

global___Figure = Figure
