"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2024)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""
import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class Block(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class Vertical(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        BORDER_FIELD_NUMBER: builtins.int
        HEIGHT_FIELD_NUMBER: builtins.int
        border: builtins.bool
        height: builtins.int
        """Height of the container, activates scrolling"""
        def __init__(
            self,
            *,
            border: builtins.bool = ...,
            height: builtins.int = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["border", b"border", "height", b"height"]) -> None: ...

    class Horizontal(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        GAP_FIELD_NUMBER: builtins.int
        gap: builtins.str
        def __init__(
            self,
            *,
            gap: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["gap", b"gap"]) -> None: ...

    class Column(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        WEIGHT_FIELD_NUMBER: builtins.int
        GAP_FIELD_NUMBER: builtins.int
        weight: builtins.float
        gap: builtins.str
        def __init__(
            self,
            *,
            weight: builtins.float = ...,
            gap: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["gap", b"gap", "weight", b"weight"]) -> None: ...

    class Expandable(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        LABEL_FIELD_NUMBER: builtins.int
        EXPANDED_FIELD_NUMBER: builtins.int
        ICON_FIELD_NUMBER: builtins.int
        label: builtins.str
        expanded: builtins.bool
        icon: builtins.str
        def __init__(
            self,
            *,
            label: builtins.str = ...,
            expanded: builtins.bool | None = ...,
            icon: builtins.str = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_expanded", b"_expanded", "expanded", b"expanded"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_expanded", b"_expanded", "expanded", b"expanded", "icon", b"icon", "label", b"label"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_expanded", b"_expanded"]) -> typing_extensions.Literal["expanded"] | None: ...

    class Form(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        FORM_ID_FIELD_NUMBER: builtins.int
        CLEAR_ON_SUBMIT_FIELD_NUMBER: builtins.int
        BORDER_FIELD_NUMBER: builtins.int
        form_id: builtins.str
        clear_on_submit: builtins.bool
        border: builtins.bool
        def __init__(
            self,
            *,
            form_id: builtins.str = ...,
            clear_on_submit: builtins.bool = ...,
            border: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["border", b"border", "clear_on_submit", b"clear_on_submit", "form_id", b"form_id"]) -> None: ...

    class TabContainer(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        def __init__(
            self,
        ) -> None: ...

    class Tab(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        LABEL_FIELD_NUMBER: builtins.int
        label: builtins.str
        def __init__(
            self,
            *,
            label: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["label", b"label"]) -> None: ...

    class ChatMessage(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        class _AvatarType:
            ValueType = typing.NewType("ValueType", builtins.int)
            V: typing_extensions.TypeAlias = ValueType

        class _AvatarTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Block.ChatMessage._AvatarType.ValueType], builtins.type):  # noqa: F821
            DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
            IMAGE: Block.ChatMessage._AvatarType.ValueType  # 0
            EMOJI: Block.ChatMessage._AvatarType.ValueType  # 1
            ICON: Block.ChatMessage._AvatarType.ValueType  # 2

        class AvatarType(_AvatarType, metaclass=_AvatarTypeEnumTypeWrapper): ...
        IMAGE: Block.ChatMessage.AvatarType.ValueType  # 0
        EMOJI: Block.ChatMessage.AvatarType.ValueType  # 1
        ICON: Block.ChatMessage.AvatarType.ValueType  # 2

        NAME_FIELD_NUMBER: builtins.int
        AVATAR_FIELD_NUMBER: builtins.int
        AVATAR_TYPE_FIELD_NUMBER: builtins.int
        name: builtins.str
        avatar: builtins.str
        avatar_type: global___Block.ChatMessage.AvatarType.ValueType
        def __init__(
            self,
            *,
            name: builtins.str = ...,
            avatar: builtins.str = ...,
            avatar_type: global___Block.ChatMessage.AvatarType.ValueType = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["avatar", b"avatar", "avatar_type", b"avatar_type", "name", b"name"]) -> None: ...

    VERTICAL_FIELD_NUMBER: builtins.int
    HORIZONTAL_FIELD_NUMBER: builtins.int
    COLUMN_FIELD_NUMBER: builtins.int
    EXPANDABLE_FIELD_NUMBER: builtins.int
    FORM_FIELD_NUMBER: builtins.int
    TAB_CONTAINER_FIELD_NUMBER: builtins.int
    TAB_FIELD_NUMBER: builtins.int
    CHAT_MESSAGE_FIELD_NUMBER: builtins.int
    ALLOW_EMPTY_FIELD_NUMBER: builtins.int
    @property
    def vertical(self) -> global___Block.Vertical: ...
    @property
    def horizontal(self) -> global___Block.Horizontal: ...
    @property
    def column(self) -> global___Block.Column: ...
    @property
    def expandable(self) -> global___Block.Expandable: ...
    @property
    def form(self) -> global___Block.Form: ...
    @property
    def tab_container(self) -> global___Block.TabContainer: ...
    @property
    def tab(self) -> global___Block.Tab: ...
    @property
    def chat_message(self) -> global___Block.ChatMessage: ...
    allow_empty: builtins.bool
    def __init__(
        self,
        *,
        vertical: global___Block.Vertical | None = ...,
        horizontal: global___Block.Horizontal | None = ...,
        column: global___Block.Column | None = ...,
        expandable: global___Block.Expandable | None = ...,
        form: global___Block.Form | None = ...,
        tab_container: global___Block.TabContainer | None = ...,
        tab: global___Block.Tab | None = ...,
        chat_message: global___Block.ChatMessage | None = ...,
        allow_empty: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["chat_message", b"chat_message", "column", b"column", "expandable", b"expandable", "form", b"form", "horizontal", b"horizontal", "tab", b"tab", "tab_container", b"tab_container", "type", b"type", "vertical", b"vertical"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["allow_empty", b"allow_empty", "chat_message", b"chat_message", "column", b"column", "expandable", b"expandable", "form", b"form", "horizontal", b"horizontal", "tab", b"tab", "tab_container", b"tab_container", "type", b"type", "vertical", b"vertical"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["type", b"type"]) -> typing_extensions.Literal["vertical", "horizontal", "column", "expandable", "form", "tab_container", "tab", "chat_message"] | None: ...

global___Block = Block
