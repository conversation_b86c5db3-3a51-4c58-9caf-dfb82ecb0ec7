Metadata-Version: 2.4
Name: validators
Version: 0.35.0
Summary: Python Data Validation for Humans™
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
License: MIT
Project-URL: Homepage, https://python-validators.github.io/validators
Project-URL: Documentation, https://yozachar.github.io/pyvalidators
Project-URL: Repository, https://github.com/python-validators/validators
Project-URL: Changelog, https://github.com/python-validators/validators/blob/master/CHANGES.md
Keywords: validation,validator,python-validator
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE.txt
Provides-Extra: crypto-eth-addresses
Requires-Dist: eth-hash[pycryptodome]>=0.7.0; extra == "crypto-eth-addresses"
Dynamic: license-file

# validators - Python Data Validation for Humans™

[![PyCQA][pycqa-badge]][pycqa-link] [![SAST][sast-badge]][sast-link] [![Docs][docs-badge]][docs-link] [![Version][vs-badge]][vs-link] [![Downloads][dw-badge]][dw-link]

<!-- [![Package][package-badge]][package-link] -->

Python has all kinds of data validation tools, but every one of them seems to
require defining a schema or form. I wanted to create a simple validation
library where validating a simple value does not require defining a form or a
schema.

```shell
pip install validators
```

Then,

```python
>>> import validators
>>>
>>> validators.email('<EMAIL>')
True
```

## Resources

<!-- Backup documentation URL :  https://yozachar.github.io/pyvalidators/ -->
<!-- Original documentation URL :  https://python-validators.github.io/validators/ -->

- [Documentation](https://yozachar.github.io/pyvalidators)
- [Bugtracker](https://github.com/python-validators/validators/issues)
- [Security](https://github.com/python-validators/validators/blob/master/SECURITY.md)
- [Code](https://github.com/python-validators/validators/)

<!-- Original docs URL will be restored, once properly versioned docs are ready. -->

---

> **_Python 3.9 [reaches EOL in](https://endoflife.date/python) October 2025._**

<!-- Links -->
[sast-badge]: https://github.com/python-validators/validators/actions/workflows/sast.yaml/badge.svg
[sast-link]: https://github.com/python-validators/validators/actions/workflows/sast.yaml
[pycqa-badge]: https://github.com/python-validators/validators/actions/workflows/pycqa.yaml/badge.svg
[pycqa-link]: https://github.com/python-validators/validators/actions/workflows/pycqa.yaml
[docs-badge]: https://github.com/yozachar/pyvalidators/actions/workflows/pages/pages-build-deployment/badge.svg
[docs-link]: https://github.com/yozachar/pyvalidators/actions/workflows/pages/pages-build-deployment
[vs-badge]: https://img.shields.io/pypi/v/validators?logo=pypi&logoColor=white&label=version&color=blue
[vs-link]: https://pypi.python.org/pypi/validators/
[dw-badge]: https://img.shields.io/pypi/dm/validators?logo=pypi&logoColor=white&color=blue
[dw-link]: https://pypi.python.org/pypi/validators/

<!-- [package-badge]: https://github.com/python-validators/validators/actions/workflows/package.yaml/badge.svg
[package-link]: https://github.com/python-validators/validators/actions/workflows/package.yaml -->
