#!/usr/bin/env python3
"""
Knowledge Base Integration System
Connects error classification with existing DevOps knowledge base and historical data
"""

import os
import json
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np
from collections import defaultdict

# Import our components
from error_classification_system import ErrorCategory, ErrorClassificationResult
from root_cause_analysis import RootCauseHypothesis, KnowledgeBaseIntegrator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class IncidentRecord:
    """Structured incident record for knowledge base"""
    incident_id: str
    title: str
    description: str
    error_category: str
    root_cause: str
    resolution_steps: List[str]
    affected_components: List[str]
    error_patterns: List[str]
    severity: str
    resolution_time_hours: float
    cost_impact: Optional[float]
    prevention_measures: List[str]
    created_at: datetime
    resolved_at: Optional[datetime]
    tags: List[str]

@dataclass
class SolutionTemplate:
    """Template for common solutions"""
    template_id: str
    name: str
    description: str
    applicable_categories: List[ErrorCategory]
    solution_steps: List[str]
    prerequisites: List[str]
    estimated_time_minutes: int
    success_rate: float
    last_updated: datetime

class HistoricalDataManager:
    """Manages historical incident and solution data"""
    
    def __init__(self, db_path: str = "knowledge_base.db"):
        self.db_path = db_path
        self.connection = None
        
    def connect(self) -> bool:
        """Connect to knowledge base database"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row
            self._create_tables()
            logger.info(f"Connected to knowledge base: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to knowledge base: {e}")
            return False
    
    def _create_tables(self):
        """Create knowledge base tables"""
        cursor = self.connection.cursor()
        
        # Incidents table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS incidents (
                incident_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                error_category TEXT,
                root_cause TEXT,
                resolution_steps TEXT,
                affected_components TEXT,
                error_patterns TEXT,
                severity TEXT,
                resolution_time_hours REAL,
                cost_impact REAL,
                prevention_measures TEXT,
                created_at DATETIME,
                resolved_at DATETIME,
                tags TEXT
            )
        """)
        
        # Solution templates table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS solution_templates (
                template_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                applicable_categories TEXT,
                solution_steps TEXT,
                prerequisites TEXT,
                estimated_time_minutes INTEGER,
                success_rate REAL,
                last_updated DATETIME
            )
        """)
        
        # Error patterns table for fast lookup
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS error_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern TEXT NOT NULL,
                incident_id TEXT,
                category TEXT,
                frequency INTEGER DEFAULT 1,
                FOREIGN KEY (incident_id) REFERENCES incidents (incident_id)
            )
        """)
        
        # Solution effectiveness tracking
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS solution_effectiveness (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_id TEXT,
                incident_id TEXT,
                was_successful BOOLEAN,
                actual_time_minutes INTEGER,
                feedback TEXT,
                applied_at DATETIME,
                FOREIGN KEY (template_id) REFERENCES solution_templates (template_id),
                FOREIGN KEY (incident_id) REFERENCES incidents (incident_id)
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_incidents_category ON incidents(error_category)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_incidents_created ON incidents(created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_error_patterns_pattern ON error_patterns(pattern)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_error_patterns_category ON error_patterns(category)")
        
        self.connection.commit()
    
    def store_incident(self, incident: IncidentRecord) -> bool:
        """Store incident record in knowledge base"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO incidents 
                (incident_id, title, description, error_category, root_cause, 
                 resolution_steps, affected_components, error_patterns, severity,
                 resolution_time_hours, cost_impact, prevention_measures, 
                 created_at, resolved_at, tags)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                incident.incident_id,
                incident.title,
                incident.description,
                incident.error_category,
                incident.root_cause,
                json.dumps(incident.resolution_steps),
                json.dumps(incident.affected_components),
                json.dumps(incident.error_patterns),
                incident.severity,
                incident.resolution_time_hours,
                incident.cost_impact,
                json.dumps(incident.prevention_measures),
                incident.created_at,
                incident.resolved_at,
                json.dumps(incident.tags)
            ))
            
            # Store error patterns for fast lookup
            for pattern in incident.error_patterns:
                cursor.execute("""
                    INSERT INTO error_patterns (pattern, incident_id, category)
                    VALUES (?, ?, ?)
                    ON CONFLICT(pattern, incident_id) DO UPDATE SET frequency = frequency + 1
                """, (pattern, incident.incident_id, incident.error_category))
            
            self.connection.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error storing incident: {e}")
            return False
    
    def find_similar_incidents(self, error_text: str, category: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Find similar incidents based on error patterns and category"""
        try:
            cursor = self.connection.cursor()
            
            # First, try to find exact pattern matches
            similar_incidents = []
            error_words = error_text.lower().split()
            
            for word in error_words:
                if len(word) > 3:  # Skip short words
                    cursor.execute("""
                        SELECT i.*, ep.pattern, ep.frequency
                        FROM incidents i
                        JOIN error_patterns ep ON i.incident_id = ep.incident_id
                        WHERE ep.pattern LIKE ? AND i.error_category = ?
                        ORDER BY ep.frequency DESC
                    """, (f"%{word}%", category))
                    
                    results = cursor.fetchall()
                    for row in results:
                        incident_dict = dict(row)
                        # Parse JSON fields
                        for field in ['resolution_steps', 'affected_components', 'error_patterns', 'prevention_measures', 'tags']:
                            if incident_dict[field]:
                                incident_dict[field] = json.loads(incident_dict[field])
                        similar_incidents.append(incident_dict)
            
            # Remove duplicates and sort by relevance
            unique_incidents = {}
            for incident in similar_incidents:
                incident_id = incident['incident_id']
                if incident_id not in unique_incidents:
                    unique_incidents[incident_id] = incident
                    unique_incidents[incident_id]['relevance_score'] = 1
                else:
                    unique_incidents[incident_id]['relevance_score'] += 1
            
            # Sort by relevance score
            sorted_incidents = sorted(
                unique_incidents.values(),
                key=lambda x: x['relevance_score'],
                reverse=True
            )
            
            return sorted_incidents[:limit]
            
        except Exception as e:
            logger.error(f"Error finding similar incidents: {e}")
            return []
    
    def get_solution_templates(self, category: ErrorCategory) -> List[Dict[str, Any]]:
        """Get solution templates for a specific error category"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT * FROM solution_templates 
                WHERE applicable_categories LIKE ?
                ORDER BY success_rate DESC
            """, (f"%{category.value}%",))
            
            templates = []
            for row in cursor.fetchall():
                template_dict = dict(row)
                # Parse JSON fields
                for field in ['applicable_categories', 'solution_steps', 'prerequisites']:
                    if template_dict[field]:
                        template_dict[field] = json.loads(template_dict[field])
                templates.append(template_dict)
            
            return templates
            
        except Exception as e:
            logger.error(f"Error getting solution templates: {e}")
            return []

class IntelligentSuggestionEngine:
    """Generates intelligent suggestions based on historical data and patterns"""
    
    def __init__(self, historical_manager: HistoricalDataManager):
        self.historical_manager = historical_manager
        self.pattern_weights = defaultdict(float)
        self.solution_success_rates = defaultdict(float)
        
    def analyze_historical_patterns(self):
        """Analyze historical data to identify patterns and success rates"""
        try:
            cursor = self.historical_manager.connection.cursor()
            
            # Analyze pattern effectiveness
            cursor.execute("""
                SELECT ep.pattern, ep.category, COUNT(*) as frequency,
                       AVG(i.resolution_time_hours) as avg_resolution_time
                FROM error_patterns ep
                JOIN incidents i ON ep.incident_id = i.incident_id
                WHERE i.resolved_at IS NOT NULL
                GROUP BY ep.pattern, ep.category
                HAVING frequency > 1
            """)
            
            for row in cursor.fetchall():
                pattern, category, frequency, avg_time = row
                # Weight patterns by frequency and inverse resolution time
                weight = frequency / (avg_time + 1)  # +1 to avoid division by zero
                self.pattern_weights[f"{category}:{pattern}"] = weight
            
            # Analyze solution success rates
            cursor.execute("""
                SELECT st.template_id, st.name, 
                       COUNT(se.id) as applications,
                       SUM(CASE WHEN se.was_successful THEN 1 ELSE 0 END) as successes
                FROM solution_templates st
                LEFT JOIN solution_effectiveness se ON st.template_id = se.template_id
                GROUP BY st.template_id, st.name
            """)
            
            for row in cursor.fetchall():
                template_id, name, applications, successes = row
                if applications > 0:
                    success_rate = successes / applications
                    self.solution_success_rates[template_id] = success_rate
            
            logger.info("Historical pattern analysis completed")
            
        except Exception as e:
            logger.error(f"Error analyzing historical patterns: {e}")
    
    def generate_suggestions(self, 
                           error_classification: ErrorClassificationResult,
                           error_text: str) -> List[Dict[str, Any]]:
        """Generate intelligent suggestions based on historical data"""
        
        suggestions = []
        
        # Find similar incidents
        similar_incidents = self.historical_manager.find_similar_incidents(
            error_text, 
            error_classification.predicted_category.value
        )
        
        # Generate suggestions from similar incidents
        for incident in similar_incidents[:3]:  # Top 3 similar incidents
            suggestion = {
                'type': 'historical_incident',
                'title': f"Similar to incident: {incident['title']}",
                'description': incident['description'],
                'root_cause': incident['root_cause'],
                'resolution_steps': incident['resolution_steps'],
                'estimated_time': incident['resolution_time_hours'],
                'confidence': min(incident['relevance_score'] / 5.0, 1.0),
                'source': f"Incident {incident['incident_id']}"
            }
            suggestions.append(suggestion)
        
        # Get solution templates
        templates = self.historical_manager.get_solution_templates(
            error_classification.predicted_category
        )
        
        # Generate suggestions from templates
        for template in templates[:2]:  # Top 2 templates
            success_rate = self.solution_success_rates.get(template['template_id'], template['success_rate'])
            
            suggestion = {
                'type': 'solution_template',
                'title': template['name'],
                'description': template['description'],
                'solution_steps': template['solution_steps'],
                'prerequisites': template['prerequisites'],
                'estimated_time': template['estimated_time_minutes'] / 60.0,  # Convert to hours
                'confidence': success_rate,
                'source': f"Template {template['template_id']}"
            }
            suggestions.append(suggestion)
        
        # Sort suggestions by confidence
        suggestions.sort(key=lambda x: x['confidence'], reverse=True)
        
        return suggestions

class KnowledgeBaseManager:
    """Main knowledge base integration manager"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize components
        self.historical_manager = HistoricalDataManager(
            self.config.get('db_path', 'knowledge_base.db')
        )
        self.suggestion_engine = None
        
        # Configuration
        self.auto_learn = self.config.get('auto_learn', True)
        self.min_confidence_threshold = self.config.get('min_confidence_threshold', 0.6)
        
    def initialize(self) -> bool:
        """Initialize the knowledge base system"""
        if not self.historical_manager.connect():
            return False
        
        # Initialize suggestion engine
        self.suggestion_engine = IntelligentSuggestionEngine(self.historical_manager)
        
        # Load and analyze historical patterns
        self.suggestion_engine.analyze_historical_patterns()
        
        # Populate with sample data if database is empty
        self._populate_sample_data()
        
        logger.info("Knowledge base system initialized")
        return True
    
    def _populate_sample_data(self):
        """Populate database with sample incidents and solutions"""
        try:
            cursor = self.historical_manager.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM incidents")
            incident_count = cursor.fetchone()[0]
            
            if incident_count == 0:
                # Add sample incidents
                sample_incidents = [
                    IncidentRecord(
                        incident_id="INC-001",
                        title="Database Connection Pool Exhaustion",
                        description="Application unable to connect to database due to connection pool exhaustion",
                        error_category=ErrorCategory.DATABASE_ERROR.value,
                        root_cause="High traffic spike without proper connection pooling configuration",
                        resolution_steps=[
                            "Increase connection pool size in application configuration",
                            "Implement connection pooling best practices",
                            "Add connection monitoring and alerting"
                        ],
                        affected_components=["application_server", "database"],
                        error_patterns=["connection pool exhausted", "too many connections", "database timeout"],
                        severity="high",
                        resolution_time_hours=2.5,
                        cost_impact=5000.0,
                        prevention_measures=[
                            "Implement proper load testing",
                            "Set up connection pool monitoring",
                            "Configure auto-scaling for database connections"
                        ],
                        created_at=datetime.now() - timedelta(days=30),
                        resolved_at=datetime.now() - timedelta(days=30, hours=-2.5),
                        tags=["database", "performance", "connection_pool"]
                    ),
                    IncidentRecord(
                        incident_id="INC-002",
                        title="Memory Leak in Application Server",
                        description="Application server experiencing memory leaks causing frequent crashes",
                        error_category=ErrorCategory.RESOURCE_ERROR.value,
                        root_cause="Memory leak in user session management code",
                        resolution_steps=[
                            "Identify memory leak using profiling tools",
                            "Fix session cleanup logic in application code",
                            "Increase heap size as temporary measure",
                            "Implement memory monitoring"
                        ],
                        affected_components=["application_server"],
                        error_patterns=["out of memory", "heap space", "gc overhead limit", "memory exhausted"],
                        severity="critical",
                        resolution_time_hours=6.0,
                        cost_impact=15000.0,
                        prevention_measures=[
                            "Regular memory profiling",
                            "Automated memory leak detection",
                            "Code review for resource management"
                        ],
                        created_at=datetime.now() - timedelta(days=15),
                        resolved_at=datetime.now() - timedelta(days=15, hours=-6),
                        tags=["memory", "application", "performance"]
                    )
                ]
                
                for incident in sample_incidents:
                    self.historical_manager.store_incident(incident)
                
                logger.info("Sample incident data populated")
                
        except Exception as e:
            logger.error(f"Error populating sample data: {e}")
    
    def get_intelligent_suggestions(self, 
                                  error_classification: ErrorClassificationResult,
                                  error_text: str) -> List[Dict[str, Any]]:
        """Get intelligent suggestions based on historical data"""
        if not self.suggestion_engine:
            return []
        
        return self.suggestion_engine.generate_suggestions(error_classification, error_text)
    
    def record_incident(self, incident: IncidentRecord) -> bool:
        """Record a new incident in the knowledge base"""
        return self.historical_manager.store_incident(incident)
    
    def learn_from_feedback(self, incident_id: str, solution_id: str, 
                           was_successful: bool, actual_time_minutes: int,
                           feedback: str = ""):
        """Learn from solution effectiveness feedback"""
        if self.auto_learn:
            try:
                cursor = self.historical_manager.connection.cursor()
                cursor.execute("""
                    INSERT INTO solution_effectiveness 
                    (template_id, incident_id, was_successful, actual_time_minutes, feedback, applied_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (solution_id, incident_id, was_successful, actual_time_minutes, feedback, datetime.now()))
                
                self.historical_manager.connection.commit()
                
                # Re-analyze patterns to update weights
                self.suggestion_engine.analyze_historical_patterns()
                
                logger.info(f"Feedback recorded for incident {incident_id}")
                
            except Exception as e:
                logger.error(f"Error recording feedback: {e}")

# Example usage
if __name__ == "__main__":
    # Initialize knowledge base manager
    kb_manager = KnowledgeBaseManager()
    
    if kb_manager.initialize():
        # Test with sample error classification
        from error_classification_system import ErrorCategory, ErrorClassificationResult
        
        error_result = ErrorClassificationResult(
            predicted_category=ErrorCategory.DATABASE_ERROR,
            confidence_score=0.85,
            probability_distribution={"database_error": 0.85},
            features_used=["text_features"],
            model_name="test",
            timestamp=datetime.now()
        )
        
        error_text = "Database connection pool exhausted - unable to get connection"
        
        # Get suggestions
        suggestions = kb_manager.get_intelligent_suggestions(error_result, error_text)
        
        print(f"Found {len(suggestions)} suggestions:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"\n--- Suggestion {i} ---")
            print(f"Type: {suggestion['type']}")
            print(f"Title: {suggestion['title']}")
            print(f"Confidence: {suggestion['confidence']:.3f}")
            print(f"Estimated Time: {suggestion['estimated_time']:.1f} hours")
            if 'resolution_steps' in suggestion:
                print(f"Steps: {suggestion['resolution_steps'][:2]}...")  # First 2 steps
    else:
        print("Failed to initialize knowledge base system")
